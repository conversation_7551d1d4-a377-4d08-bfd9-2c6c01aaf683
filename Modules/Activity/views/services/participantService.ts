import http from '/admin/support/http'

export const participantService = {
  getList: (params: any) => http.get('/activity/invitation/1/members', params),
  confirm: (id: number | string) => http.post(`/api/participants/${id}/confirm`),
  reject: (id: number | string) => http.post(`/api/participants/${id}/reject`),
  batchConfirm: (ids: (number | string)[]) => http.post('/api/participants/batch-confirm', { ids }),
  batchReject: (ids: (number | string)[]) => http.post('/api/participants/batch-reject', { ids }),
  export: () => http.get('/api/participants/export', { responseType: 'blob' }),
  getDetail: (id: number | string) => http.get(`/api/participants/${id}`),
  updateStatus: (id: number | string, status: string) => http.put(`/api/participants/${id}/status`, { status }),
  batchUpdateStatus: (ids: (number | string)[], status: string) => http.post('/api/participants/batch-update-status', { ids, status }),
  resendInvitation: (id: number | string) => http.post(`/api/participants/${id}/resend-invitation`),
  addToBlacklist: (id: number | string) => http.post(`/api/participants/${id}/blacklist`),
  getActionHistory: (id: number | string) => http.get(`/api/participants/${id}/actions`),
  create: (data: any) => http.post('/api/participants', data),
  update: (id: number | string, data: any) => http.put(`/api/participants/${id}`, data)
} 