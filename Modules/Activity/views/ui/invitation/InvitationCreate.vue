<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <h2>新建邀请列表</h2>
    </div>
    <div class="module-con">
      <div class="box">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="列表设置" name="list">
              <el-form ref="formRef" label-position="top" :model="formData" style='width: 100%' class="invitation-form">
                <div class="title">
                  <el-icon><Tickets /></el-icon>
                  邀请列表基础设置
                </div>

                <el-form-item label="列表名称" prop="name" required>
                  <el-input v-model="formData.name" placeholder="2025慈善晚会邀请人员名单" />
                </el-form-item>

                <el-form-item label="列表描述" prop="description">
                  <el-input v-model="formData.description" type="textarea" placeholder="NGO工作人员" :maxlength="500" show-word-limit />
                </el-form-item>

                <el-form-item label="设置为默认列表">
                  <el-radio-group v-model="formData.isAuditList">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="是否为此列表成员分配群组">
                  <el-radio-group v-model="formData.isGroupList">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="群组类型" v-if="formData.isGroupList">
                  <el-select v-model="formData.groupType">
                    <el-option label="VIP" value="vip" />
                    <el-option label="普通会员" value="normal" />
                  </el-select>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="成员列表" name="members"> 
              <div class='title-box'>
                <div class="title">
                  <el-icon><Tickets /></el-icon>
                  成員列表
                </div>
                <el-button type="primary" link>
                  <el-icon class="el-icon--right"><Download /></el-icon>下載名單上傳模版
                </el-button>
              </div>
              <el-table :data="tableData" style="width: 100%" >
                <el-table-column prop="groupName" label="名字" />
                <el-table-column prop="groupId" label="公司" />
                <el-table-column prop="memberCount" label="職位" width="120" />  
                <el-table-column prop="memberCount" label="工作電話" width="120" />          
                <el-table-column prop="memberCount" label="email" />
              </el-table>
              <el-button style="width: 100%" @click="onAddItem">
                請添加成員<el-icon><Plus /></el-icon>
              </el-button>
            </el-tab-pane>
          </el-tabs>
        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleNext" v-if="activeTab !== 'members'"> 下一步 </el-button>
          <el-button type="primary" @click="handleSubmit" v-else> 保存并提交 </el-button>
        </div>
      </div>
    </div>

    <AddMembers 
      v-model:visible="addMembersVis" 
      @cancel="handleCancelMember" 
      @create="handleCreateMember" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import type { InvitationForm } from './types'
import { Tickets, Download, Plus } from '@element-plus/icons-vue'
import AddMembers from "./addMembers.vue"

const router = useRouter()
const formRef = ref<FormInstance>()
const activeTab = ref('list')
const addMembersVis = ref(false)

const formData = reactive<InvitationForm>({
  name: '',
  description: '',
  isAuditList: false,
  isGroupList: false,
  groupType: 'vip',
})

const tableData = ref([]);

const handleNext = async () => {
  if (activeTab.value === 'list') {
    const valid = await formRef.value?.validate()
    if (!valid) return
  }

  switch (activeTab.value) {
    case 'list':
      activeTab.value = 'email'
      break
    case 'email':
      activeTab.value = 'members'
      break
  }
}

const onAddItem = () => {
  addMembersVis.value = true;
}

const handleCancelMember = () => {
  addMembersVis.value = false;
}

const handleCreateMember = (val) => {
  addMembersVis.value = false;
  tableData.value.push(val);
}

const handleSubmit = async () => {
  try {
    // TODO: 实现提交逻辑
    ElMessage.success('创建成功')
    router.push({ name: 'InvitationList' })
  } catch (error) {
    ElMessage.error('创建失败')
  }
}

const handleCancel = () => {
  router.back()
}
</script>

<style scoped>
.title-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title {
  font-size: 22px;
  display: flex;
  align-items: center;
  margin: 0px 0px 15px 0px
}
.invitation-form {
  max-width: 800px;
}

.form-section {
  margin-bottom: 24px;
}

.form-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: normal;
}

.form-actions {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-light);
  text-align: right;
}
</style>
