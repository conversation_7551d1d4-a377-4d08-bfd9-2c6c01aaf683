<template>
  <div class="bwms-module">
    <div class="module-header">
      <el-button @click="handleReset">重置</el-button>
      <el-button type="primary" @click="handleCreate">
        新建邀请列表
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <!-- 数据表格 -->
        <el-table
          v-loading="loading"
          :data="invitationList"
          style="width: 100%"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="name" label="列表名" />
          <el-table-column prop="member_count" label="成员数" width="120" />
          <el-table-column prop="mailbox_count" label="邮箱" width="120" />          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" :icon="Edit" circle size="small" @click="handleEdit(row)"></el-button>
              <el-button type="danger" :icon="Delete" circle size="small" @click="handleDelete(row)"></el-button>
              <el-button type="info" :icon="Document" circle size="small" @click="handleView(row)"></el-button>
              <el-button type="info" :icon="Share" circle size="small" @click="handleShare(row)"></el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="queryParams.page"
            v-model:page-size="queryParams.per_page"
            :total="total"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { InvitationItem, InvitationQuery } from './types'
import { Edit, Delete, Document, Share } from '@element-plus/icons-vue'
import http from '/admin/support/http'
import { useRouter } from 'vue-router'
const router = useRouter()
// 查询参数
const queryParams = reactive<InvitationQuery>({
  page: 1,
  per_page: 10,
  keyword: ''
})

const loading = ref(false)
const total = ref(0)
const invitationList = ref([])

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    const { data } = await http.get('/activity/invitation')

    if (data.code === 200) {
      invitationList.value = data.data
      total.value = data.total
    }
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 重置
const handleReset = () => {
  queryParams.keyword = ''
  queryParams.page = 1
  getList()
}

// 新建
const handleCreate = () => {
  router.push({
    name: 'InvitationCreate'
  })
}

// 编辑
const handleEdit = (row: InvitationItem) => {
  // TODO: 实现编辑逻辑
}

// 详情
const handleView = () => {
  router.push({
    path: 'details',
    query: {
      id: 1111
    }
  })
}

// 复制
const handleCopy = async (row: InvitationItem) => {
  try {
    // TODO: 实现复制逻辑
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 分享
const handleShare = (row: InvitationItem) => {
  // TODO: 实现分享逻辑
}

// 删除
const handleDelete = (row: InvitationItem) => {
  ElMessageBox.confirm(
    '确认删除该邀请列表吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // TODO: 实现删除逻辑
      await fetch(`/api/invitations/${row.id}`, {
        method: 'DELETE'
      })
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 分页
const handleSizeChange = (val: number) => {
  queryParams.per_page = val
  getList()
}

const handleCurrentChange = (val: number) => {
  queryParams.page = val
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.bwms-module{
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}
.invitation-list {
  padding: 20px;
}

.search-wrapper {
  padding: 16px 0;
  display: flex;
  align-items: center;
}

.search-input {
  width: 300px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 