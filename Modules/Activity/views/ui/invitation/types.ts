export interface InvitationItem {
  id: number
  name: string
  email: string
  status: 'pending' | 'accepted' | 'rejected'
  created_at: string
  updated_at: string
  member_count: number
  mailbox_count: number
}

export interface InvitationQuery {
  page: number
  per_page: number
  keyword?: string
  status?: string
}

export interface InvitationForm {
  name: string
  description: string
  isAuditList: boolean
  isGroupList: boolean
  groupType: string
} 