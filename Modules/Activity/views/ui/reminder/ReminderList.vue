<template>
  <div class="bwms-module">
    <div class="module-header">
      <div class="actions">
        <el-button type="primary" @click="handleCreate">新建提醒</el-button>
      </div>
    </div>

    <div class='box'>
      <el-table :data="ticketList" style="width: 100%" border>
        <template #empty>
          <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
        </template>
        <el-table-column prop="name" label="提醒名稱"></el-table-column>
        <el-table-column prop="code" label="recipient" width="180"></el-table-column>
        <el-table-column prop="createDate" label="創建時間" width="180"></el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" :icon="Edit" circle size="small" @click="handleEdit(scope.row)"></el-button>
            <el-button type="danger" :icon="Delete" circle size="small" @click="handleDelete(scope.row)"></el-button>
            <el-button type="info" :icon="Document" circle size="small" @click="handleView(scope.row)"></el-button>
            <el-switch @change="changHandle" />
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          layout="prev, pager, next"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          @current-change="handleCurrentChange"
        >
          <template #default>
            {{ currentPage }}/{{ totalPages }}
          </template>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Delete, Document } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表格数据
const ticketList = ref([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(2)
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

const loading = ref(false);

// 操作方法
const handleEdit = (row) => {
  router.push({
    path: '/activity/admin/create',
    query: {
      id: row?.id || 1111
    }
  })
}

// 删除
const handleDelete = (row) => {
}

// 复制
const handleView = (row) => {
  console.log('查看', row)
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 开关
const changHandle = () => {

}

// 创建
const handleCreate = () => {
  router.push('/activity/admin/create')
}

// 查询列表
const getTableDataList = async () => {
  loading.value = true
  try {
    const params = {}
    // const { data } = await ticketService.getList(params)
    ticketList.value = data.list
    total.value = data.total
  } catch (error) {
    ElMessage.error('获取邀请列表失败')
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  // getTableDataList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .box {
    padding: 20px;
    background: #fff;
    border-radius: 4px;

    .search-area {
      margin-bottom: 20px;
    }

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style> 