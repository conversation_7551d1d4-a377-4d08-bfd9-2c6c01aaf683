<template>
    <div class="bwms-module">
      <!-- 面包屑导航 -->
    
  
      <!-- 页面标题 -->
      <div class="module-header">
        
        <el-button type="primary" @click="handleCreate">
          新增邮件模版
        </el-button>
      </div>
  
      <!-- 表格区域 -->
      <div class="module-con">
        <div class="box">
          <!-- 搜索和操作区 -->
          <div class="table-header">
            <div class="left">
              <el-input
                v-model="searchQuery"
                placeholder="请输入"
                class="search-input"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button class="filter-button">
                <el-icon><Filter /></el-icon>
              </el-button>
            </div>
            <div class="right">
              <el-button>移动至</el-button>
              <el-button>批量复制</el-button>
            </div>
          </div>
  
          <!-- 表格 -->
          <el-table
            v-loading="loading"
            :data="tableData"
            @selection-change="handleSelectionChange"
          >
            <template #empty>
              <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
            </template>
            <el-table-column type="selection" width="55" />
            <el-table-column 
              prop="name" 
              label="模版名称" 
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column 
              prop="scene" 
              label="使用场景" 
              min-width="150"
              show-overflow-tooltip
            />
            <el-table-column 
              prop="updateTime" 
              label="最后修改时间" 
              min-width="180"
              :formatter="(row) => formatDateTime(row.updateTime)"
            />
            <el-table-column width="200" fixed="right">
              <template #default="{ row }">
                <el-link type="primary" @click="handleEdit(row)">编辑</el-link>
                <el-link type="danger" class="ml-2" @click="handleDelete(row)">删除</el-link>
                <el-switch
                  v-model="row.status"
                  class="ml-2"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleStatusChange(row)"
                />
                <el-dropdown trigger="click" class="ml-2">
                  <el-icon><More /></el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>...</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
  
          <!-- 分页 -->
          <div class="pagination-container">
            <div class="pagination-info">
              <el-button :disabled="currentPage <= 1" @click="handleCurrentChange(currentPage - 1)">
                上一页
              </el-button>
              <span class="page-info">{{ currentPage }}/{{ Math.ceil(total / pageSize) }}</span>
              <el-button 
                :disabled="currentPage >= Math.ceil(total / pageSize)"
                @click="handleCurrentChange(currentPage + 1)"
              >
                下一页
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Filter, More } from '@element-plus/icons-vue'
  import { ruleTemplateService } from '../../services/ruleTemplateService'
  import type { IRuleTemplate } from '../../types'
  
  const router = useRouter()
  const loading = ref(false)
  const searchQuery = ref('')
  const tableData = ref<IRuleTemplate[]>([])
  const selectedRows = ref<IRuleTemplate[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  
  // 添加防抖搜索
  let searchTimer: NodeJS.Timeout | null = null
  watch(searchQuery, (newVal) => {
    if (searchTimer) clearTimeout(searchTimer)
    searchTimer = setTimeout(() => {
      currentPage.value = 1
      fetchData()
    }, 300)
  })
  
  // 获取列表数据
  const fetchData = async () => {
    try {
      loading.value = true
      const params = {
        page: currentPage.value,
        limit: pageSize.value,
        keyword: searchQuery.value
      }
      const { data } = await ruleTemplateService.getList(params)
      tableData.value = data.list
      total.value = data.total
    } catch (error) {
      console.error('获取列表失败:', error)
      ElMessage.error('获取列表失败')
    } finally {
      loading.value = false
    }
  }
  
  // 创建模版
  const handleCreate = () => {
    router.push({ name: 'RuleTemplateCreate' })
  }
  
  // 编辑模版
  const handleEdit = (row: IRuleTemplate) => {
    router.push({ 
      name: 'RuleTemplateEdit',
      params: { id: row.id }
    })
  }
  
  // 复制模版
  const handleCopy = async (row: IRuleTemplate) => {
    try {
      await ruleTemplateService.copy(row.id)
      ElMessage.success('复制成功')
      fetchData()
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败')
    }
  }
  
  // 批量复制
  const handleBatchCopy = async () => {
    try {
      const ids = selectedRows.value.map(row => row.id)
      await ruleTemplateService.batchCopy(ids)
      ElMessage.success('批量复制成功')
      fetchData()
    } catch (error) {
      console.error('批量复制失败:', error)
      ElMessage.error('批量复制失败')
    }
  }
  
  // 删除模版
  const handleDelete = (row: IRuleTemplate) => {
    ElMessageBox.confirm(
      '确定要删除该模版吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      try {
        await ruleTemplateService.delete(row.id)
        ElMessage.success('删除成功')
        fetchData()
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
  }
  
  // 状态变更
  const handleStatusChange = async (row: IRuleTemplate) => {
    try {
      await ruleTemplateService.updateStatus(row.id, row.status)
      ElMessage.success('状态更新成功')
    } catch (error) {
      console.error('状态更新失败:', error)
      ElMessage.error('状态更新失败')
      // 恢复状态
      row.status = row.status === 1 ? 0 : 1
    }
  }
  
  // 选择变更
  const handleSelectionChange = (rows: IRuleTemplate[]) => {
    selectedRows.value = rows
  }
  
  // 分页大小变更
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    fetchData()
  }
  
  // 页码变更
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    fetchData()
  }
  
  // 初始化
  onMounted(() => {
    // fetchData()
  })
  </script>
  
  <style lang="scss" scoped>
  .bwms-module {
    .breadcrumb {
      margin-bottom: 16px;
      color: #666;
    }
  
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
  
      .title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
      }
    }
  
    .module-con {
      .box {
        padding: 20px;
        background: #fff;
        border-radius: 4px;
  
        .table-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
  
          .left {
            display: flex;
            align-items: center;
            gap: 12px;
  
            .search-input {
              width: 240px;
            }
  
            .filter-button {
              padding: 8px;
            }
          }
  
          .right {
            display: flex;
            gap: 12px;
          }
        }
  
        .el-link {
          font-size: 14px;
          
          & + .el-link {
            margin-left: 16px;
          }
        }
  
        .pagination-container {
          margin-top: 20px;
          display: flex;
          justify-content: center;
  
          .pagination-info {
            display: flex;
            align-items: center;
            gap: 12px;
  
            .page-info {
              color: #666;
              margin: 0 12px;
            }
          }
        }
      }
    }
  }
  
  .ml-2 {
    margin-left: 8px;
  }
  </style> 