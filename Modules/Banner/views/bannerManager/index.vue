<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <FilterPopover 
        v-model="filterDialog"
        :width="400"
      >
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
            <el-icon>
              <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
            </el-icon>
            <span>{{ $t('Banner.common.filter') }}</span>
          </el-button>
        </template>
        <el-form :model="search" label-position="top">
          <el-form-item :label="$t('Banner.common.create.label1')">
            <el-input v-model="search.title" :placeholder="$t('Banner.common.input_placeholder')+t('Banner.common.create.label1')" size="large" />
          </el-form-item>

        </el-form>

        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="refreshBanner">
              <el-icon>
                <Refresh />
              </el-icon>
              <span>{{ $t('Banner.common.refresh') }}</span>
            </el-button>
            <el-button class="button-no-border" @click="searchBanner" type="primary">
              <el-icon>
                <Filter />
              </el-icon>
              <span>{{ $t('Banner.common.filter') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      
      <el-button @click="addBanner" type="primary">
        <el-icon>
          <Plus />
        </el-icon>
        <span>{{ $t('Banner.common.add') }}</span>
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <el-table :data="bannerList" style="width: 100%; height: 100%" v-loading="loading">
          <template #empty>
              <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="title" show-overflow-tooltip :label="$t('Banner.common.create.label1')" min-width="200" />
          <el-table-column prop="created_at" :label="$t('Banner.common.createTime')" width="300" />
          <el-table-column fixed="right" :label="$t('Banner.common.create.label7')" width="150">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" type="text" @click="editBannerHandle(scope.row)">
                  <el-icon>
                    <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                  </el-icon>
                </el-button>
                <el-button class="bwms-operate-btn" type="text" @click="delBannerHandle(scope.row)">
                  <el-icon>
                    <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Banner.common.page_size_text') }}</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="changePage"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  {{ t('Banner.common.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Banner.common.total_items', { total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="changePage"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      class="el-dialog-common-cls"
      v-model="bannerDialog" 
      :title="isEdit ? $t('Banner.common.editTitle') : $t('Banner.common.addTitle')" 
      width="500"
    >
      <el-form 
        ref="bannerFormRef"
        :model="bannerForm"
        :rules="rules"
        label-position="top"
      >
        <div class="form-row">
          <el-form-item 
            :label="$t('Banner.common.create.label1')" 
            prop="title"
            required
            class="form-item"
          >
            <el-input 
              v-model="bannerForm.title"
              :placeholder="$t('Banner.common.input_placeholder')+$t('Banner.common.create.label1')"
            />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="bannerDialog = false">
            {{ $t('Banner.common.cancel') }}
          </el-button>
          <el-button 
            class="button-no-border" 
            type="primary" 
            @click="saveBanner" 
            :loading="submitLoading"
          >
            {{ $t('Banner.common.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialog" :title="$t('Banner.common.deleteTitle')" width="500">
      <div>{{ $t('Banner.common.deleteConfirm') }}</div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="deleteDialog = false">
            {{ $t('Banner.common.cancel') }}
          </el-button>
          <el-button 
            class="button-no-border" 
            type="primary" 
            @click="confirmDelete" 
            :loading="deleteLoading"
          >
            {{ $t('Banner.common.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Filter, 
  Plus, 
  Refresh
} from '@element-plus/icons-vue'
import http from '/admin/support/http'
import FilterPopover from '/resources/admin/components/popover/index.vue'

const { t } = useI18n()

// 语言设置
const lang = localStorage.getItem('bwms_language') || 'zh_HK'
const langList = ref([
  {
    label: t('Banner.common.lang_zh_CN'),
    value: 'zh_CN',
  },
  {
    label: t('Banner.common.lang_en'),
    value: 'en',
  },
  {
    label: t('Banner.common.lang_zh_HK'),
    value: 'zh_HK',
  },
])

// 广告组列表数据
const bannerList = ref<any[]>([])
const loading = ref(true)
const page = ref(1)
const limit = ref(15)
const total = ref(0)

// 筛选相关
const filterDialog = ref(false)
const search = reactive({
  id: '',
  title: '',
  data_lang: lang,
})

// 广告组表单
const bannerDialog = ref(false)
const isEdit = ref(false)
const bannerFormRef = ref()
const bannerForm = reactive({
  id: undefined as number | undefined,
  title: '',
  link: '',
  image: '',
  data_lang: lang,
  sort: 0
})
const rules = {
  title: [{ required: true, message: t('Banner.common.required'), trigger: 'blur' }],
  data_lang: [{ required: true, message: t('Banner.common.required'), trigger: 'change' }],
  image: [{ required: true, message: t('Banner.common.required'), trigger: 'change' }]
}

// 删除相关
const deleteDialog = ref(false)
const currentBanner = ref<any>(null)

// 在现有状态声明附近添加保存按钮的loading状态
const submitLoading = ref(false)
const deleteLoading = ref(false)

// 获取广告组列表
const getBannerList = async () => {
  loading.value = true
  
  try {
    // 构建请求参数，使用类型断言解决类型错误
    const params: any = {
      page: page.value,
      per_page: limit.value,
    }
    
    // 添加搜索条件
    if (search.id) {
      params.id = search.id
    }
    
    if (search.title) {
      params.title = search.title
    }
    
    if (search.data_lang) {
      params.data_lang = search.data_lang
    }
    
    // 调用获取广告组列表接口
    const response = await http.get('/banner/group', params)
    
    if (response.data && response.data.code === 200) {
      bannerList.value = response.data.data || []
      total.value = response.data.data.length || 0
    } else {
      // 错误处理
      ElMessage.error(response.data?.message || t('Banner.common.get_data_failed'))
      // 如果请求失败但状态码不是200，使用空数据
      bannerList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取广告组列表失败:', error)
    ElMessage.error(t('Banner.common.get_data_failed'))
    bannerList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 分页变化
const changePage = () => {
  getBannerList()
}

// 搜索广告组
const searchBanner = () => {
  page.value = 1
  getBannerList()
  filterDialog.value = false
}

// 重置筛选
const refreshBanner = () => {

  search.title = ''

  page.value = 1
  getBannerList()
  filterDialog.value = false
}

// 新增广告组
const addBanner = () => {
  isEdit.value = false
  bannerForm.id = undefined
  bannerForm.title = ''
  bannerForm.link = ''
  bannerForm.image = ''
  bannerForm.data_lang = lang
  bannerForm.sort = 0
  bannerDialog.value = true
}

// 编辑广告组
const editBannerHandle = (row: any) => {
  isEdit.value = true
  bannerForm.id = row.id
  bannerForm.title = row.title
  bannerForm.link = row.link || ''
  bannerForm.image = row.image || ''
  bannerForm.data_lang = row.data_lang || lang
  bannerForm.sort = row.sort || 0
  bannerDialog.value = true
}

// 删除广告组
const delBannerHandle = (row: any) => {
  ElMessageBox.confirm(
    t('Banner.common.deleteConfirm'),
    t('Banner.common.deleteTitle'),
    {
      confirmButtonText: t('Banner.common.confirm'),
      cancelButtonText: t('Banner.common.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    deleteLoading.value = true
    try {
      const response = await http.delete(`/banner/group/${row.id}`)
      if (response.data && response.data.code === 200) {
        ElMessage.success(t('Banner.common.deleteSuccess'))
        getBannerList()
      } else {
        ElMessage.error(response.data?.message || t('Banner.common.deleteError'))
      }
    } catch (error) {
      console.error('删除广告组失败:', error)
      ElMessage.error(t('Banner.common.deleteError'))
    } finally {
      deleteLoading.value = false
    }
  }).catch(() => {
    // 用户取消删除，不执行任何操作
  })
}

// 确认删除
const confirmDelete = async () => {
  if (!currentBanner.value) return
  
  deleteLoading.value = true
  
  try {
    // 调用删除接口 - 修改为正确的接口地址
    const response = await http.delete(`/banner/group/${currentBanner.value.id}`)
    
    if (response.data && response.data.code === 200) {
      ElMessage.success(t('Banner.common.deleteSuccess'))
      getBannerList()
    } else {
      ElMessage.error(response.data?.message || t('Banner.common.deleteError'))
    }
  } catch (error) {
    console.error('删除广告组失败:', error)
    ElMessage.error(t('Banner.common.deleteError'))
  } finally {
    deleteDialog.value = false
    deleteLoading.value = false
  }
}

// 保存广告组
const saveBanner = () => {
  bannerFormRef.value?.validate(async (valid: boolean) => {
    if (!valid) return
    
    submitLoading.value = true // 设置按钮loading状态
    
    try {
      // 准备提交的数据，只保留需要的字段
      const data: any = {
        title: bannerForm.title,
        // 如果是编辑模式，添加id
        ...(isEdit.value ? { id: bannerForm.id } : {})
      }
      
      // 统一调用保存接口
      const response = await http.post('/banner/group/save', data)
      
      if (response.data && response.data.code === 200) {
        ElMessage.success(isEdit.value ? t('Banner.common.updateSuccess') : t('Banner.common.createSuccess'))
        bannerDialog.value = false
        getBannerList()
      } else {
        ElMessage.error(response.data?.message || t('Banner.common.saveError'))
      }
    } catch (error) {
      console.error('保存广告组失败:', error)
      ElMessage.error(t('Banner.common.saveError'))
    } finally {
      submitLoading.value = false // 重置按钮loading状态
    }
  })
}

// 上传成功回调
const handleUploadSuccess = (response: any) => {
  bannerForm.image = response.url
}

// 重置表单
const resetForm = () => {
  bannerFormRef.value?.resetFields()
}

// 页面初始化
onMounted(() => {
  getBannerList()
})
</script>

<style scoped lang="scss">
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

/* 表单布局样式 */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 4px;
}

.form-item {
  flex: 1;
  min-width: 0;
}

.avatar-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
