<template>
  <div>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane :label="$t('CFM.detail.general')" name="first"><GeneralTab :fieldData="FieldRow" :defaultValues="defaultValues" @blur="getGenval" /></el-tab-pane>
      <el-tab-pane :label="$t('CFM.detail.validation')" name="second"><ValidationTab :fieldData="FieldRow" :defaultValues="defaultValues" @change="getVal" /></el-tab-pane>
      <el-tab-pane :label="$t('CFM.detail.presentation')" name="third"><PresentationTab :fieldData="FieldRow" :defaultValues="defaultValues" @getPre="getPre" /></el-tab-pane>
      <!-- <el-tab-pane label="条件逻辑" name="fourth"><ConditionalLogicTab :fieldData="FieldRow" @returnConlogic="getCon" /></el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits, onMounted } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import ValidationTab from './FieldTabs/ValidationTab.vue'
import PresentationTab from './FieldTabs/PresentationTab.vue'
import GeneralTab from './FieldTabs/GeneralTab.vue'
import ConditionalLogicTab from './FieldTabs/ConditionalLogicTab.vue'

const emit = defineEmits(['tab-click'])
const activeName = ref('first')
const generalValue = ref<Object>({})
const preValue = ref<Object>({})
const valValue = ref<Object>({})
const { props, defaultValues } = defineProps({
  props: Object,
  defaultValues: Object,
})
const FieldRow = props.row
const handleClick = (tab: TabsPaneContext, event: Event) => {
  const fieldGroupForm = { ...generalValue.value, ...preValue.value, ...valValue.value }
  emit('tab-click', fieldGroupForm)
}

const getGenval = (val: any) => {
  generalValue.value = { ...val }
  if (generalValue.value['type'] === 'RepeaterTable') {
  }
  const fieldGroupForm = { ...generalValue.value, ...preValue.value, ...valValue.value }
  emit('tab-click', fieldGroupForm)
}

const getPre = (val: any) => {
  preValue.value = { ...val }
  const fieldGroupForm = { ...generalValue.value, ...preValue.value, ...valValue.value }
  emit('tab-click', fieldGroupForm)
}

const getVal = (val: any) => {
  valValue.value = { ...val }
  const fieldGroupForm = { ...generalValue.value, ...preValue.value, ...valValue.value }
  emit('tab-click', fieldGroupForm)
}

const getCon = (val: any) => {
  const fieldGroupForm = { ...generalValue.value, ...preValue.value, ...valValue.value, ...val }
  emit('tab-click', fieldGroupForm)
}
onMounted(() => {
  const fieldGroupForm = { ...generalValue.value, ...preValue.value, ...valValue.value }
  emit('tab-click', fieldGroupForm)
})
</script>

<style lang="scss" scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
