<template>
  <div class="table-page bwms-module">
    <div class="module-header">
       <!-- 筛选 -->
       <FilterPopover 
        v-model="showFilterDropdown"
      >
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
            <el-icon>
              <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
            </el-icon>
            <span>{{ $t('CFM.detail.search') }}</span>
          </el-button>
        </template>
        
        <el-form :model="search" label-position="top">
          <el-form-item :label="$t('CFM.detail.content_tit')" class="filter-form-item">
            <el-input v-model="search.name" :placeholder="$t('CFM.detail.content_tit_placeholder')" size="default" />
          </el-form-item>
        </el-form>
        
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="refreshList">
              <el-icon><Refresh /></el-icon>
              <span>{{ $t('CFM.detail.refresh') }}</span>
            </el-button>
            <el-button class="button-no-border" type="primary" @click="getSearchList">
              <el-icon><Filter /></el-icon>
              <span>{{ $t('CFM.detail.filter') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>

      <el-button @click="router.push('/cfm/addFiled')" type="primary">
        <el-icon><Plus /></el-icon>
        <span>{{ $t('CFM.detail.add_field') }}</span>
      </el-button>

    </div>

    <div class="module-con">
      <div class="box">
        <!-- 表格区域 -->
        <el-table ref="multipleTableRef" :data="tableData" style="width: 100%; height: 100%">
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" width="100" />
          <el-table-column :label="$t('CFM.detail.content_tit')" min-width="300">
            <template #default="{ row }">
              <div class="field-name">{{ row.name }}</div>
            </template>
          </el-table-column>
          <el-table-column property="description" :label="$t('CFM.detail.description')" width="200" />
          <el-table-column property="key" :label="$t('CFM.detail.key')" width="300" />
          <el-table-column property="location" :label="$t('CFM.detail.position')" width="200" />
          <el-table-column property="Columns" :label="$t('CFM.detail.category')"  width="200"/>
          <el-table-column fixed="right" :label="$t('CFM.detail.operate')" width="160">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" link @click="editRow(row.id, row.key)">
                  <el-icon size="16"><img src="/resources/admin/assets/icon/EditIcon.png" ></el-icon>
                </el-button>
                <el-button class="bwms-operate-btn" link @click="moveToTrash(row)">
                  <el-icon size="16"><img src="/resources/admin/assets/icon/DeleteIcon.png" ></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页器 -->
      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="handleSizeChange"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  {{ $t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import http from '/admin/support/http'
import { ElMessage } from 'element-plus'
import { Search, Plus, Filter, Refresh } from '@element-plus/icons-vue'
// import FilterPopover from '/admin/components/FilterPopover.vue'

interface TableItem {
  id: number
  name: string
  description: string
  key: string
  location: string
  Columns: string
  showActions: boolean
}

const router = useRouter()
const route = useRoute()
const searchName = ref('')
const tableData = ref<TableItem[]>([])
const rubbishData = ref<TableItem[]>([])

// 分页相关
const page = ref(1)
const limit = ref(20)
const total = ref(0)

// 添加筛选相关的响应式变量
const showFilterDropdown = ref(false)
const search = reactive({
  name: ''
})

const handleCurrentChange = (newPage: number) => {
  page.value = newPage
  getFieldGroupsList()
}

const handleSizeChange = (newSize: number) => {
  limit.value = newSize
  page.value = 1
  getFieldGroupsList()
}

const options = [
  {
    value: '批次操作',
    label: '批次操作',
  },
  {
    value: '编辑',
    label: '编辑',
  },
  {
    value: '移至回收桶',
    label: '移至回收桶',
  },
  {
    value: 'Duplicate',
    label: '重复',
  },
  {
    value: 'Activate',
    label: '激活',
  },
  {
    value: 'Deactivate',
    label: '关闭',
  },
]

const editRow = (id: number | string, key: string) => {
  router.push({
    path: '/cfm/addFiled',
    query: {
      id: id.toString(),
      key,
    },
  })
}

const moveToTrash = (row: TableItem) => {
  try {
    http.delete(`/cfm/field-group/${row.id}`)
    ElMessage({
      message: '删除成功',
      type: 'success',
      plain: true,
    })
    getFieldGroupsList()
  } catch (error) {
    console.log('error', error)
  }
  rubbishData.value.push(row)
}

const processFieldGroupsList = (data: any[]) => {
  return data.map(item => ({
    id: item.id,
    name: item.title,
    description: item.description || '-',
    key: item.key,
    location: item.location_rules?.group_0?.rule_0?.value || '-',
    Columns: item.fields.length.toString(),
    showActions: false,
  }))
}

const refreshList = () => {
  search.name = ''
  page.value = 1
  showFilterDropdown.value = false
  getFieldGroupsList()
}

const getSearchList = async () => {
  if (search.name === '') {
    getFieldGroupsList()
  } else {
    const res = await http.get(`/cfm/field-search/`, { name: search.name })
    tableData.value = processFieldGroupsList(res.data.data)
    total.value = tableData.value.length
  }
  showFilterDropdown.value = false
}

const getFieldGroupsList = async () => {
  const params = {
    page: page.value,
    limit: limit.value,
  }
  const res = await http.get(`/cfm/field-group/`, params)
  tableData.value = processFieldGroupsList(res.data.data)
  total.value = res.data.total || tableData.value.length // 更新总数
}

onMounted(() => {
  getFieldGroupsList()
})

watch(route, () => {
  getFieldGroupsList()
})
</script>

<style lang="scss" scoped>
.bwms-module{
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}


</style>
