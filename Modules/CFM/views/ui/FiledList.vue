<template>
  <div class="bwms-module">
    <div class="module-header">
      <div class="btn-list">
        <el-input v-model="title" style="width: 220px; height: 40px" :placeholder="$t('CFM.detail.field_group_title')" @blur="getFieldGroup" />
        <el-button @click="addFiled">
          <el-icon><Plus /></el-icon>
          <span>{{ $t('CFM.detail.add_field') }}</span>
        </el-button>
        <el-button @click="saveChange" type="primary">
          <el-icon><Plus /></el-icon>
          <span>{{ $t('CFM.detail.save') }}</span>
        </el-button>
      </div>
    </div>

    <div class="module-con">
      <div class="box scroll-box">
        <el-card>
          <template #header>
            <div class="card-header">{{ $t('CFM.detail.category') }}</div>
          </template>

          <el-table :data="tableData" style="width: 100%" @expand-change="handleExpandChange" :row-key="row => row.key" :expand-row-keys="[expandedRowKey]">
            <template #empty>
              <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
            </template>
            <el-table-column type="index" />
            <el-table-column type="expand">
              <template #default="props">
                <div>
                  <FieldGroupForm :props="props" :defaultValues="props.row.values" @tab-click="getFieldGroup(props.row.key, $event)" />
                  <el-button @click="closeFiled">{{ $t('CFM.detail.close_field') }}</el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('CFM.detail.label')" width="300">
              <template v-slot="{ row }">
                <div class="fieldTit">
                  <div class="fieldName">
                    {{ row.label ? row.label : '(无标题)' }}
                  </div>
                  <div v-if="row.showActions" class="filedBtn">
                    <el-button link type="primary" @click="toggleExpand(row.key)">{{ $t('CFM.detail.edit') }} </el-button>
                    <!-- <el-button link type="primary" @click="duplicateRow(row)">{{ $t('CFM.detail.duplicate') }}</el-button>
                                <el-button link type="primary" @click="deactivateRow(row)">{{ $t('CFM.detail.move') }}</el-button> -->
                    <el-popconfirm title="确定删除?" @confirm="deleteField(row)">
                      <template #reference>
                        <el-button link type="danger">{{ $t('CFM.detail.del') }}</el-button>
                      </template>
                    </el-popconfirm>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('CFM.detail.name')" prop="name" />
            <el-table-column :label="$t('CFM.detail.key')" prop="key" />
            <el-table-column :label="$t('CFM.detail.type')" prop="type" />
          </el-table>

          <div class="table-footer">
            <el-button style="width: 130px; height: 40px; color: white; background-color: #5585c8" @click="addFiled">
              <el-icon><Plus /></el-icon>
              {{ $t('CFM.detail.add_field') }}
            </el-button>
          </div>
        </el-card>

        <Suspense>
          <template #default>
            <SettingCard v-if="settingsLoaded" @settings="getSettings" @settings2="getSettingsGroup" :resultSettings="resultSettings" :key="settingsKey" />
          </template>
          <template #fallback>
            <div>Loading...</div>
          </template>
        </Suspense>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import http from '/admin/support/http'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import FieldGroupForm from '../components/FieldGroupForm.vue'
import SettingCard from '../components/SettingCard/index.vue'

interface table {
  id: number | null
  label: string
  name: string
  key: string
  type: string
  showActions: boolean
  isGetData: boolean
  isModified: boolean
  values?: Record<string, any>
}

const route = useRoute()
const router = useRouter()
const queryId = route.query.id
const groupKey = route.query.key
const id = ref(0)
const tableName = ref(0)
const title = ref('')
const expandedRowKey = ref<number | null | String>(null)
const changeData = ref<Array<any>>([])
const tableData = ref<table[]>([])
const settingsLoaded = ref(false)
const resultSettings = ref<Settings>([])
const settingsKey = ref(0)

const addFiled = () => {
  const newFieldId = generateFieldId()
  tableData.value.push({
    id: null,
    label: '',
    name: '',
    key: newFieldId,
    type: 'Text',
    showActions: true,
    isModified: true,
    isGetData: false,
    values: {},
  })
  expandedRowKey.value = newFieldId
}

const deleteField = async (row: table) => {
  if (row.isGetData) {
    try {
      await http.delete(`/cfm/fields/${parseInt(row.id as unknown as string, 10)}`)
      ElMessage({
        message: '删除成功',
        type: 'success',
        plain: true,
      })
      getFieldGroupsList()
    } catch (error) {
      ElMessage({
        message: `删除失败${error}`,
        type: 'error',
        plain: true,
      })
      console.error('删除失败', error)
    }
  } else {
    tableData.value = tableData.value.filter(item => item.key !== row.key)
  }
}

const handleExpandChange = (row: table, expanded: boolean) => {
  if (expanded) {
    expandedRowKey.value = row.key
  } else {
    expandedRowKey.value = null
  }
}

const toggleExpand = (id: number) => {
  expandedRowKey.value = expandedRowKey.value === id ? null : id
}

const closeFiled = () => {
  expandedRowKey.value = null
}

const subData = ref<table[]>([])
const getFieldGroup = (key: string, val: any) => {
  const rowIndex = tableData.value.findIndex(item => item.key === key)
  if (rowIndex !== -1) {
    if (val.type === 'RepeaterTable' && val.tableData) {
      subData.value = [...val.tableData]
    }
    if (val.type === 'Group' && val.tableData) {
      subData.value = [...val.tableData]
    }
    if (val.type === 'FlexibleContent' && val.cards) {
      subData.value = [...val.cards]
    }
    tableData.value[rowIndex].label = val.label
    tableData.value[rowIndex].type = val.type
    tableData.value[rowIndex].name = val.name
    tableData.value[rowIndex].values = { ...val }
    tableData.value[rowIndex].isModified = true
  }
}

const settingsData = ref<Record<string, string>>({})
const settingGroup = ref<Record<string, string>>({})
const getSettings = (val: any) => {
  if (val) {
    settingsData.value = transformArrayToObject(val)
  }
}
const getSettingsGroup = (val: any) => {
  settingGroup.value = val
}

const transformGroupSettings = (settings: Record<string, any>): Record<string, any> => {
  const transformedSettings: Record<string, any> = {}
  Object.keys(settings).forEach(key => {
    transformedSettings[`field_group[${key}]`] = key === 'active' ? (settings[key] ? 1 : 0) : settings[key].toString()
  })
  return transformedSettings
}

function transformArrayToObject(arr: Condition[][]): Record<string, string> {
  const result: Record<string, string> = {}
  arr.forEach((group, groupIndex) => {
    group.forEach((item, conditionIndex) => {
      if (item.settingSelect1 && item.settingSelect2) {
        result[`field_group[location][group_${groupIndex}][rule_${conditionIndex}][param]`] = item.settingSelect1
        result[`field_group[location][group_${groupIndex}][rule_${conditionIndex}][operator]`] = item.isequal
        result[`field_group[location][group_${groupIndex}][rule_${conditionIndex}][value]`] = item.settingSelect2
      }
    })
  })
  return result
}

function generateFieldId() {
  const timestampPart = Date.now().toString(16).slice(-7)
  const randomPart = Math.floor(Math.random() * 0x10000000)
    .toString(16)
    .padStart(7, '0')
  return `field_${timestampPart}${randomPart}`
}

function getGroupId() {
  const timestampPart = Date.now().toString(16).slice(-7)
  const randomPart = Math.floor(Math.random() * 0x10000000)
    .toString(16)
    .padStart(7, '0')
  return `group_${timestampPart}${randomPart}`
}

const saveChange = async () => {
  if (title.value === '') {
    ElMessage({
      message: '请输入标题',
      type: 'error',
      plain: true,
    })
    return
  }

  const form = new FormData()
  form.append('post_title', title.value)
  form.append('post_type', 'field-group')
  if (queryId) {
    form.append('post_ID', queryId)
  }

  const processFields = (fields: table[], isSub: Boolean) => {
    fields.forEach((row, rowIndex) => {
      if (queryId && !row.isModified) {
        return
      }

      let fieldId

      fieldId = row.key

      const changeValue = row.values || {}

      if (row.type === 'FlexibleContent') {
        // 处理FlexibleContent类型字段
        if (row.values && row.values.cards) {
          row.values.cards.forEach((card: any) => {
            const cardId = card.key
            form.append(`fields[${fieldId}][key]`, cardId)
            form.append(`fields[${fieldId}][label]`, card.label)
            form.append(`fields[${fieldId}][name]`, card.name)
            form.append(`fields[${fieldId}][display]`, card.display)
            form.append(`fields[${fieldId}][min]`, card.min)
            form.append(`fields[${fieldId}][max]`, card.max)
            form.append(`fields[${fieldId}][parent_key]`, card.parent_key)

            if (card.fields) {
              card.fields.forEach((subField: any) => {
                const subFieldId = subField.key
                form.append(`fields[${subFieldId}][key]`, subFieldId)
                form.append(`fields[${subFieldId}][label]`, subField.label)
                form.append(`fields[${subFieldId}][name]`, subField.name)
                form.append(`fields[${subFieldId}][type]`, subField.type)
                form.append(`fields[${subFieldId}][parent_key]`, subField.parent_key)
                form.append(`fields[${subFieldId}][default_value]`, subField.values.default_value)
                form.append(`fields[${subFieldId}][instructions]`, subField.values.instructions)
                form.append(`fields[${subFieldId}][placeholder]`, subField.values.placeholder)
                form.append(`fields[${subFieldId}][prepend]`, subField.values.prepend)
                form.append(`fields[${subFieldId}][append]`, subField.values.append)
                form.append(`fields[${subFieldId}][width]`, subField.values.width)
                form.append(`fields[${subFieldId}][class]`, subField.values.class)
                form.append(`fields[${subFieldId}][id]`, subField.values.id)
                form.append(`fields[${subFieldId}][required]`, subField.values.required)
                form.append(`fields[${subFieldId}][maxlength]`, subField.values.maxlength)
              })
            }
          })
        }
      }
      if (isSub) {
        form.append(`fields[${fieldId}][ID]`, row.id ? row.id.toString() : '0')
        form.append(`fields[${fieldId}][key]`, row.key ? row.key.toString() : '')
        form.append(`fields[${fieldId}][group_id]`, queryId ? queryId : '90')
        form.append(`fields[${fieldId}][type]`, row.type)
        form.append(`fields[${fieldId}][parent_key]`, row.parent ? row.parent.toString() : '')
        form.append(`fields[${fieldId}][menu_older]`, row.menu_older ? row.menu_older.toString() : '0')
      } else {
        form.append(`fields[${fieldId}][menu_order]`, row.id ? row.id.toString() : '0')
        form.append(`fields[${fieldId}][ID]`, row.id ? row.id.toString() : '0')
        form.append(`fields[${fieldId}][key]`, row.key ? row.key.toString() : '')
        form.append(`fields[${fieldId}][group_id]`, queryId ? queryId : '90')
        form.append(`fields[${fieldId}][type]`, row.type)
      }
      form.append(`fields[${fieldId}][save]`, 'settings')
      const uniqueKeys = new Set()
      Object.keys(changeValue).forEach(key => {
        if (!uniqueKeys.has(key) && !['showActions', 'isModified', 'values', 'key', 'type', 'wrapper', 'cards', 'save', 'tableData', 'sub_fields'].includes(key)) {
          const value = changeValue[key]
          if (['width', 'class', 'id'].includes(key)) {
            if (!form.has(`fields[${fieldId}][wrapper][${key}]`)) {
              form.append(`fields[${fieldId}][wrapper][${key}]`, value?.toString() || '')
            }
          }
          if (['filters'].includes(key)) {
            if (!form.has(`fields[${fieldId}][filters][]`)) {
              let filters = changeValue[key]
              Object.keys(filters).forEach(key => {
                if (filters[key] !== '') {
                  form.append(`fields[${fieldId}][filters][]`, filters[key].toString())
                }
              })
            }
          }
          if (['clone'].includes(key)) {
            if (!form.has(`fields[${fieldId}][clone][]`)) {
              let filters = changeValue[key]
              Object.keys(filters).forEach(key => {
                if (filters[key] !== '') {
                  form.append(`fields[${fieldId}][clone][]`, filters[key].toString())
                }
              })
            }
          }
          if (['showActions', 'isModified', 'values', 'key', 'type', 'wrapper', 'cards', 'save', 'tableData', 'width', 'class', 'id', 'filters', 'clone'].includes(key)) {
            return
          } else {
            form.append(`fields[${fieldId}][${key}]`, value || '')
          }
          uniqueKeys.add(key)
        }
      })
      let groupId
      if (groupKey && !isSub) {
        groupId = groupKey
        form.append('post_name', groupId.toString())
      } else if (!isSub) {
        groupId = getGroupId()
        form.append('post_name', groupId.toString())
      }

      if (!isSub) {
        form.append('field_group[key]', groupId.toString())
      }
    })
  }

  processFields(tableData.value, false)
  if (subData.value.length > 0) {
    processFields(subData.value, true)
  }

  const settings = settingsData.value
  for (const key in settings) {
    if (settings.hasOwnProperty(key)) {
      form.append(key, settings[key])
    }
  }

  const transformedGroupSettings = transformGroupSettings(settingGroup.value)
  for (const key in transformedGroupSettings) {
    if (transformedGroupSettings.hasOwnProperty(key)) {
      form.append(key, transformedGroupSettings[key])
    }
  }

  try {
    await http.post('/cfm/field-group', form)
    ElMessage({
      message: '保存成功',
      type: 'success',
      plain: true,
    })
    router.back()
  } catch (error) {
    ElMessage({
      message: `保存失败${error}`,
      type: 'error',
      plain: true,
    })
    console.error('保存失败', error)
  }
}

const processFieldGroupDetail = (data: Record<string, any>): { tableFields: TableField[]; settings: Settings[] } => {
  const fields: Record<string, TableField> = {}
  const settings: Settings[] = []

  const cleanSubFields = (subFields: any[]) => {
    return subFields
      .filter(subField => subField !== null && subField !== undefined)
      .map(subField => {
        const subFieldId = subField.id.toString()
        const parsedSubSettings = { ...subField.settings, ...subField.settings?.wrapper }

        const subValues = {
          default_value: subField.default_value || '',
          required: parsedSubSettings.required || '0',
          ...parsedSubSettings,
          sub_fields: cleanSubFields(subField.sub_fields || []), // 递归清理子字段
        }

        return {
          id: subField.id,
          label: subField.label || '',
          name: subField.name || '',
          key: subField.key,
          type: subField.type || '',
          showActions: true,
          isModified: false,
          isGetData: true,
          values: subValues,
        }
      })
  }

  data.fields.forEach((field: any) => {
    const fieldId = field.id.toString()
    const parsedSettings = { ...field.settings, ...field.settings?.wrapper }

    const values = {
      default_value: field.default_value || '',
      required: parsedSettings.required || '0',
      ...parsedSettings,
      sub_fields: cleanSubFields(field.sub_fields || []),
    }

    fields[fieldId] = {
      id: field.id,
      label: field.label || '',
      name: field.name || '',
      key: field.key,
      type: field.type || '',
      showActions: true,
      isModified: false,
      isGetData: true,
      values,
    }
  })

  const locationRules = data.location_rules || {}

  Object.keys(locationRules).forEach(groupKey => {
    const group = locationRules[groupKey]
    const groupArray: Settings[] = []

    Object.keys(group).forEach(ruleKey => {
      const rule = group[ruleKey]
      groupArray.push({
        select1: rule.param || '',
        select2: rule.value || '',
        isequal: rule.operator || '',
      })
    })

    settings.push(groupArray)
  })

  const tableFields = Object.values(fields).map(field => ({
    ...field,
    label: field.label || '',
    name: field.name || '',
    type: field.type || '',
  }))

  return { tableFields, settings }
}

const getFieldGroupsList = async () => {
  const res = await http.get(`/cfm/field-group/${queryId}`)
  title.value = res.data.data.title
  const { tableFields, settings } = processFieldGroupDetail(res.data.data)
  tableData.value = tableFields
  resultSettings.value = [...settings]
  settingsLoaded.value = true
}

onMounted(() => {
  if (queryId) {
    getFieldGroupsList()
  } else {
    resultSettings.value = []
    setTimeout(() => {
      settingsLoaded.value = true
    }, 1000)
  }
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.el-card {
  border-radius: 8px;

  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid #e5e6eb;

    .card-header {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
    }
  }

  .el-table {
    --el-table-border-color: #e5e6eb;
    --el-table-text-color: #4e5969;
    --el-table-header-text-color: #1d2129;
    --el-table-header-bg-color: #f8fcff;

    th {
      font-weight: 500;
      height: 40px;
      background: #f8fcff;
    }

    td {
      height: 52px;
    }
  }

  .table-footer {
    padding: 16px 20px;
    text-align: right;
    border-top: 1px solid #e5e6eb;
  }
}

.fieldTit {
  padding: 10px 0;

  .fieldName {
    color: #1d2129;
    font-size: 14px;
    margin-bottom: 8px;
    cursor: pointer;

    &:hover {
      color: var(--el-color-primary);
    }
  }

  .filedBtn {
    display: flex;
    gap: 8px;

    .el-button {
      padding: 4px 0;
      height: 32px;
      font-size: 14px;

      &.el-button--primary {
        color: #165dff;

        &:hover {
          color: #0e42d2;
        }
      }

      &.el-button--danger {
        color: #f53f3f;

        &:hover {
          color: #cb272d;
        }
      }
    }
  }
}

// 顶部按钮样式
.module-header {
  .btn-list {
    display: flex;
    gap: 10px;

    .btn-box {
      height: 40px;
      padding: 0 12px;
      font-size: 14px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:not(:first-child) {
        background-color: #5585c8;
        border: none;
        color: #fff;
        cursor: pointer;

        .el-icon {
          color: #fff;
          font-size: 16px;
          margin-right: 4px;
        }

        &:hover {
          background-color: #4a75b3;
        }
      }
    }
  }
}
</style>
