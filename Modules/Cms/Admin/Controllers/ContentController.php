<?php

declare(strict_types=1);

namespace Modules\Cms\Admin\Controllers;

use Bingo\Amis\Components\Form;
use <PERSON>o\Amis\Components\Grid;
use Bingo\Base\AdminController;
use Bingo\Exceptions\BizException;
use Illuminate\Http\Request;
use Modules\Cms\Admin\AmisRender\Form\ContentForm;
use Modules\Cms\Admin\AmisRender\Grid\ContentGrid;
use Modules\Cms\Admin\Requests\ContentRequest;
use Modules\Cms\Models\CmsContent;
use Modules\Cms\Services\CmsContentService;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class ContentController extends AdminController
{
    private int $modelId = 1;

    public function __construct(
        protected readonly CmsContent        $model,
        protected readonly CmsContentService $service,
        protected readonly ContentGrid       $grid,
        protected readonly ContentForm       $form,
    ) {
        $this->setFormRequestClass(ContentRequest::class);
    }

    /**
     * @param Request $request
     * @return Grid
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function index(Request $request): Grid
    {

        $this->modelId = intval($request->get('model_id', 1));

        return $this->grid();
    }

    public function list(int $model_id): Grid
    {
        $this->modelId = $model_id;

        return $this->grid();
    }

    /**
     * @return Grid
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function grid(): Grid
    {
        $dataLang = request()->get('data_lang', T_locale());
        $categoryId = (int) request()->query('category');
        $status = (int) request()->get('status', 1);

        $extraParams = ['model_id' => $this->modelId];

        if ($categoryId > 0) {
            $extraParams['category_id'] = $categoryId;
        }
        $query = $this->service->getContentListQuery($this->modelId, $dataLang, $categoryId, $status);

        return $this->grid->Config($query, $dataLang, $extraParams, $this->modelId);
    }

    protected function form(): Form
    {
        $id = $this->resourceKey;
        $model_id = intval(request()->get('model_id'));
        $lang = request()->get('locale');
        $extraParams = ['model_id' => $model_id];
        $query = CmsContent::query();
        return $this->form->Config($query, $id, $model_id, $lang, $extraParams);
    }

    public function show(Request $request)
    {
        $id = $request->get('id');
        return $this->service->getContentById($id);
    }

    /**
     * 支持多语言详情输出
     * @param Request $request
     * @return array|string|null
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface|BizException
     */
    public function details(Request $request): array|string|null
    {
        $contentGroupId = $request->get('group_id');
        return $this->service->getMultilingualContentByGroupId($contentGroupId);
    }

    public function tags(): array
    {
        return $this->service->getCommonTags();
    }

    // 获取分类统计
    public function total(Request $request): array
    {
        $model_id = intval($request->get('model_id'));
        $lang = $request->get('locale', T_locale());
        return $this->service->getContentTotal($model_id,$lang);
    }


    /**
     * post 创建数据重写
     * @param ContentRequest $request
     * @return int
     * @throws BizException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function storeContent(ContentRequest $request): int
    {
        $data = $request->all();
        $modelId = intval($request->get("model_id"));
        
        return $this->service->saveContent($data, $modelId);
    }

    /**
     * 修改CMS内容数据
     * @param int $id
     * @param ContentRequest $request
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws BizException
     */
    public function saveContent(int $id, ContentRequest $request): int
    {
        $data = $request->all();
        $modelId = intval($request->get("model_id"));
        return $this->service->saveContent($data, $modelId, $id);
    }


    /**
     * 修改多语言CMS内容数据
     * @param int $groupId
     * @param ContentRequest $request
     * @return int
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function saveMultipleContent(int $groupId, ContentRequest $request): int
    {
        $data = $request->all();
        $modelId = intval($request->get("model_id"));
        return $this->service->saveMultipleContent($data, $modelId, $groupId);
    }

    /**
     * 版本列表
     * @param Request $request
     * @return mixed
     */
    public function versionList(Request $request, int $group_id)
    {
        $request->validate([
            'model_id' => 'required|integer',
        ]);
        $model_id = $request->get('model_id');
        $lang = $request->get('lang', 'zh_CN');

        return $this->service->getVersionList($model_id, $group_id, $lang);
    }

    /**
     * 应用版本
     * @param Request $request
     * @return int
     */
    public function applyVersion(Request $request, $group_id)
    {
        $request->validate([
            'version' => 'required|integer',
        ]);
        $version = $request->get('version');
        return $this->service->applyVersion($group_id, $version);
    }
}
