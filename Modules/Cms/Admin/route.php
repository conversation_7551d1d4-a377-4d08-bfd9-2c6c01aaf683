<?php

use AlibabaCloud\Cms\Cms;
use Illuminate\Support\Facades\Route;
use Modules\Cms\Admin\Controllers\CategoryController;
use Modules\Cms\Admin\Controllers\CmsSettingController;
use Modules\Cms\Admin\Controllers\ContactController;
use Modules\Cms\Admin\Controllers\ContentController;
use Modules\Cms\Admin\Controllers\CmsModelController;
use Modules\Cms\Admin\Controllers\ModelFieldController;
use Modules\Cms\Admin\Controllers\SyncController;
use Modules\Cms\Admin\Controllers\SitemapController;

use Modules\Cms\Admin\Controllers\V3\CmsController;

Route::prefix('cms')->group(function () {
    Route::get('cmsModel/create', [CmsModelController::class, 'create'])->name('bingo-admin.cmsModel.create');
    Route::get('cmsModel/{id}/edit', [CmsModelController::class, 'edit'])->name('bingo-admin.cmsModel.edit');
    Route::delete('cmsModel/{id}', [CmsModelController::class, 'deleteModel']);
    Route::put('cmsModel/{id}', [CmsModelController::class, 'saveModel'])->name('bingo-admin.cmsModel.save'); //重写路由

    Route::apiResource('cmsModel', CmsModelController::class)->names('bingo-admin.cmsModel');

    Route::get('cmsModelField/create', [ModelFieldController::class, 'create'])->name('bingo-admin.cmsModelField.create');
    Route::get('cmsModelField/{id}/edit', [ModelFieldController::class, 'edit'])->name('bingo-admin.cmsModelField.edit');
    Route::put('cmsModelField/{id}', [ModelFieldController::class, 'saveContent'])->name('bingo-admin.cmsModelField.save');
    Route::delete('cmsModelField/{id}', [ModelFieldController::class, 'deleteField']); //创建重写路由
    Route::apiResource('cmsModelField', ModelFieldController::class)->names('bingo-admin.cmsModelField');
    Route::post('cmsModelField', [ModelFieldController::class, 'storeContent'])->name('bingo-admin.cmsModelField.store'); //创建重写路由


    Route::get('categories/create', [CategoryController::class, 'create'])->name('cms-admin.categories.create');
    Route::get('categories/categoryList', [CategoryController::class, 'getCategoryListByModelId']);
    Route::get('categories/categoryLists', [CategoryController::class, 'getCategoryLists']);

    Route::get('categories/{id}/edit', [CategoryController::class, 'edit'])->name('cms-admin.categories.edit');
    Route::apiResource('categories', CategoryController::class)->names('cms-admin.categories');

    Route::get('content/total', [ContentController::class, 'total'])->name('bingo-admin.cmsContent.total');
    Route::get('content/create', [ContentController::class, 'create'])->name('bingo-admin.cmsContent.create');
    Route::get('content/{id}/edit', [ContentController::class, 'edit'])->name('bingo-admin.cmsContent.edit');
    Route::get('content/{model_id}', [ContentController::class, 'list'])->name('bingo-admin.cmsContent.list')->where('model_id', '[0-9]+');
    Route::put('content/{id}', [ContentController::class, 'saveContent'])->name('bingo-admin.cmsContent.save')->where('model_id', '[0-9]+');
    Route::get('detail', [ContentController::class, 'show']);

    Route::get('tags', [ContentController::class, 'tags']);
    Route::apiResource('content', ContentController::class)->names('bingo-admin.cmsContent');
    Route::post('content', [ContentController::class, 'storeContent'])->name('bingo-admin.cmsContent.store'); //重写路由

    

    Route::get('cmsSync', [SyncController::class, 'create'])->name('bingo-admin.cmsSync.create'); //翻译弹框
    Route::post('cmsSync/store', [SyncController::class, 'syncSave'])->name('bingo-admin.cmsSync.store'); //翻译弹框


    Route::get('settings', [CmsSettingController::class, 'index']);
    Route::get('settings/{module}/{id}', [CmsSettingController::class, 'show']);
    Route::post('settings', [CmsSettingController::class, 'store']);
    Route::put('settings/{module}/{id}', [CmsSettingController::class, 'update']);
    Route::delete('settings/{module}/{id}', [CmsSettingController::class, 'destroy']);

    Route::get('contact/list', [ContactController::class, 'list'])->name('bingo-admin.cmsContact.list');
    Route::post('contact/save', [ContactController::class, 'save'])->name('bingo-admin.cmsContact.edit');
    Route::post('contact/save/{id}', [ContactController::class, 'save'])->name('bingo-admin.cmsContact.edit');
    Route::get('contact/{id}/edit', [ContactController::class, 'edit'])->name('bingo-admin.cmsContact.edit');
    // Route::apiResource('contact', ContactController::class)->names('bingo-admin.cmsContact');

    Route::prefix('v2')->group(function () {
        Route::get('categories/categoryLists/{model_id}', [CategoryController::class, 'getCategoryListsV2']);

        Route::put('content/{group_id}', [ContentController::class, 'saveMultipleContent']); //多语言保存接口
        Route::get('details', [ContentController::class, 'details']); //多语言详情输出接口


    });

    Route::post('sitemap/generate', [SitemapController::class, 'generate'])->name('admin.sitemap.generate');

    Route::prefix('v3')->group(function () {
        Route::get('/list', [CmsController::class, 'list']);
       
        Route::get('/{id}', [CmsController::class, 'detail']);

        Route::get('/model/list', [CmsController::class, 'modelList']);


        Route::get('/category/list', [CmsController::class, 'categoryList']);
        Route::get('/category/{id}', [CmsController::class, 'categoryDetail']);
        Route::post('/category/save', [CmsController::class, 'categorySave']);

        Route::get('/contact/list', [CmsController::class, 'contactList']);
        Route::post('/contact/save', [CmsController::class, 'contactSave']);

        Route::get('/create', [CmsController::class, 'create']);

        Route::post('/', [CmsController::class, 'store']);
    });

    // 版本列表
    Route::get('/version/{group_id}', [ContentController::class, 'versionList']);
    Route::post('/apply/version/{group_id}', [ContentController::class, 'applyVersion']);
});
