<?php

namespace Modules\Cms\Domain;

use Bingo\Amis\Components\Form;
use Bingo\Amis\Components\Grid;
use Bingo\Core\Dao\ModelUtil;
use Bingo\Core\Util\ArrayUtil;
use Bingo\Core\Util\CRUDUtil;
use Bingo\Core\Util\TagUtil;
use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Modules\Cms\Domain\Field\CmsField;
use Modules\Cms\Enums\CmsContentVerifyStatus;
use Modules\Cms\Enums\CmsErrorCode;
use Modules\Cms\Models\CmsCategory;
use Modules\Cms\Models\CmsContent;
use Modules\Cms\Providers\CmsRecommendBiz;
use Modules\Common\Models\ContentGroups;
use Modules\TagManager\Domain\TagManagerUtil;
use Modules\TagManager\Models\Tags;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Modules\Approval\Events\ApprovalCreated;
use Modules\SEO\Domain\Services\SeoAnalysisService;

class Content
{
    private $model;
    private $modelId;
    private $modelTable;
    private $modelDataTable;


    public function __construct(
        protected readonly CmsContent $contentModel,
    ) {}

    public function init($modelId): void
    {
        $this->modelId = $modelId;
        $this->model = CmsModelUtil::get($modelId);
        $this->modelTable = 'cms_content';
        $this->modelDataTable = "cms_m_" . $this->model['name'];
    }

    /**
     * 保存内容
     * @param array $data
     * @param int $modelId
     * @param int $id
     * @return int
     * @throws BizException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function saveContent(array $data, int $modelId, int $id = 0): int
    {
        $this->init($modelId);
        $copyId = $data['_copyId'] ?? "";
        $del_id = $data['_del_id'] ?? 0;

        // 检查别名是否存在
        $content_group_id = $data['content_group_id'] ?? 0;
        $alias = $data['alias'] ?? "";
        $alias_exists = CmsContent::where('alias', $alias)->when($content_group_id > 0, function ($query) use ($content_group_id) {
            $query->where('content_group_id', '!=', $content_group_id);
        })->exists();
        if ($alias_exists) {
            throw new BizException(Code::FAILED, 'alias already exists:' . $alias);
        }

        $record = 0;
        if ($id > 0) {
            $record = ModelUtil::get($this->modelTable, $id);
            BizException::throwsIfEmpty($record, Code::FAILED, '记录不存在');
            $record['_tags'] = TagUtil::string2Array($record['tags']);
            $recordData = ModelUtil::get($this->modelDataTable, $id);
            if (! empty($recordData)) {
                foreach ($recordData as $k => $v) {
                    if (in_array($k, ['id', 'created_at', 'updated_at'])) {
                        continue;
                    }
                    $record[$k] = $v;
                }
            }
        } elseif ($copyId) {
            $record = ModelUtil::get($this->modelTable, $copyId);
            if ($record) {
                $record['_tags'] = TagUtil::string2Array($record['tags']);
                $recordData = ModelUtil::get($this->modelDataTable, $copyId);
                if (! empty($recordData)) {
                    foreach ($recordData as $k => $v) {
                        if (in_array($k, ['id', 'created_at', 'updated_at'])) {
                            continue;
                        }
                        $record[$k] = $v;
                    }
                }
                $record['id'] = null;
            }
        }

        $recordValue = ArrayUtil::keepKeys($data, [
            'content_group_id',
            'category_id',
            'title',
            'alias',
            'title',
            'summary',
            'cover',
            'post_time',
            'status',
            'is_recommend',
            'is_top',
            'tags',
            'author',
            'source',
            'seo_title',
            'seo_description',
            'seo_keywords',
            'detail_template',
            'lang',
            'version',
        ]);

        // 格式化 post_time
        if (! empty($recordValue['post_time'])) {
            $recordValue['post_time'] = Carbon::parse($recordValue['post_time'])->format('Y-m-d H:i:s');
        }

        if (bingostart_config('CmsUrlMix_Enable', false)) {
            $recordValue['fullUrl'] = (empty($data['fullUrl']) ? null : $data['fullUrl']);
        }

        if (empty($recordValue['verify_status'])) {
            $recordValue['verify_status'] = CmsContentVerifyStatus::VERIFY_PASS->value;
        }

        if (empty($recordValue['alias'])) {
            $recordValue['alias'] = null;
        }

        $recordDataValue = ArrayUtil::keepKeys($data, [
            'content',
            'lang',
            'content_id'
        ]);
        if (! empty($this->model['_customFields'])) {
            $fields = $this->model['_customFields'];
            foreach ($fields as $field) {
                $recordDataValue[$field['key']] = $data[$field['key']] ?? null;
            }
        }
        $recommendTags = [];
        if (isset($recordValue['category_id'])) {
            $cat = CmsCatUtil::get($recordValue['category_id']);
            if ($cat) {
                $recommendTags[] = $cat['title'];
            }
        }

        ModelUtil::transactionBegin();
        try {
            if (! empty($record['id'])) {

                ModelUtil::update($this->modelTable, $record['id'], $recordValue);

                // 更新数据表记录
                $recordDataValue['updated_at'] = Carbon::now()->timestamp;
                $existingRecordData = ModelUtil::get($this->modelDataTable, [
                    ['content_id', '=', $record['id']],
                    ['lang', '=', $record['lang']]
                ]);
                if ($existingRecordData) {
                    ModelUtil::update($this->modelDataTable, [
                        ['content_id', '=', $record['id']],
                        ['lang', '=', $record['lang']]
                    ], $recordDataValue);
                } else {
                    ModelUtil::insert($this->modelDataTable, array_merge($recordDataValue, [
                        'content_id' => $record['id'],
                        'lang' => $record['lang']
                    ]));
                }

                if (bingostart_module_enabled('TagManager')) {
                    (new TagManagerUtil())->updateTags($record['id'], 'cms', TagUtil::string2Array($record['tags']), TagUtil::string2Array($recordValue['tags']), $recordValue['lang']);
                }

                CmsRecommendBiz::itemUpdate(
                    $record['id'],
                    $record['model_id'],
                    $recommendTags
                );
            } else {

                $recordValue['model_id'] = $this->model['id'];
                $recordValue['created_at'] = Carbon::now()->timestamp;
                $recordValue['updated_at'] = Carbon::now()->timestamp;
                // $recordValue = ModelUtil::insert($this->modelTable, $recordValue);
                $recordValue = CmsContent::create($recordValue)?->toArray();
                $recordDataValue['content_id'] = $recordValue['id'];
                $recordDataValue['lang'] = $recordValue['lang'];
                $recordDataValue['created_at'] = Carbon::now()->timestamp;
                $recordDataValue['updated_at'] = Carbon::now()->timestamp;
                ModelUtil::insert($this->modelDataTable, $recordDataValue);

                if (bingostart_module_enabled('TagManager')) {
                    (new TagManagerUtil())->putTags($recordValue['id'], 'cms', TagUtil::string2Array($recordValue['tags']), $recordValue['lang']);
                }

                CmsRecommendBiz::itemUpdate(
                    $recordValue['id'],
                    $recordValue['model_id'],
                    $recommendTags
                );

                $record = $recordValue;
            }

            // 保存自定义字段
            if (bingostart_module_enabled('CFM') && isset($data['fields'])) {
                $cmsModel = DB::table('cms_model')->where('id', $modelId)->select('name')->first();
                if ($cmsModel) {
                    $location = "cms_" . $cmsModel->name;
                    $fieldService = new \Modules\CFM\Services\FieldService(new \Modules\CFM\Repositories\Eloquent\EloquentFieldRepository());
                    $customFieldData = [
                        'location' => $location,
                        'content_id' => $record['id'],
                        'lang' => $record['lang'],
                        'fields' => json_encode($data['fields'] ?? [])
                    ];

                    $fieldService->saveCustomFieldData($customFieldData);
                }
            }

            ModelUtil::transactionCommit();
        } catch (Exception $e) {
            ModelUtil::transactionRollback();
            BizException::throws(Code::FAILED, $e->getMessage());
        }
        // 触发审核事件
        $content_id = $record['id'];

        return $content_id;
    }


    /**
     * 删除内容
     * @param $modelId
     * @return true
     * @throws BizException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function delete($modelId): true
    {
        $this->init($modelId);
        $ids = CRUDUtil::ids();
        foreach ($ids as $id) {
            $record = ModelUtil::get($this->modelTable, $id);
            $record['_tags'] = TagUtil::string2Array($record['tags']);
            BizException::throwsIfEmpty($id, Code::FAILED, '记录不存在');
            ModelUtil::transactionBegin();
            ModelUtil::delete($this->modelTable, $record['id']);
            ModelUtil::delete($this->modelDataTable, $record['id']);
            if (bingostart_module_enabled('TagManager')) {
                (new TagManagerUtil())->detachTagsFromContent($record['id'], 'cms', $record['_tags'], $record['lang']);
            }
            CmsRecommendBiz::itemDelete($record['id']);
            ModelUtil::transactionCommit();
        }
        return true;
    }

    /**
     * 获取内容详情
     * @param $id
     * @return string
     */
    public function getContentQuery($id): string
    {
        $model = new CmsContent();
        $cmsContent = $model->where('id', $id)->first();
        if (empty($cmsContent)) {
            return "";
        }
        $this->init($cmsContent->model_id);
        $recordData = ModelUtil::get($this->modelDataTable, [
            ['content_id', '=', $id],
            ['lang', '=', $cmsContent['lang']]
        ]);
        return $recordData['content'] ?? "";
    }

    /**
     * 获取内容详情
     * @param $id
     * @return array|string|null
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getContentById($id): array|string|null
    {
        $model = new CmsContent();
        $cmsContent = $model->where('id', $id)->first();

        if (empty($cmsContent)) {
            return "";
        }
        $this->init($cmsContent->model_id);

        $contentArr = ModelUtil::get($this->modelDataTable, [
            ['content_id', '=', $id],
            ['lang', '=', $cmsContent['lang']]
        ]);

        //渲染CFM 自定义字段
        if (bingostart_module_enabled('CFM')) {
            $modelId = $cmsContent->model_id;
            $cmsModel = DB::table('cms_model')->where('id', $modelId)->select('name')->first();
            if ($cmsModel) {
                $location = "cms_" . $cmsModel->name;
                $fieldService = new \Modules\CFM\Services\FieldService(new \Modules\CFM\Repositories\Eloquent\EloquentFieldRepository());
                $customFields = $fieldService->getCustomFieldData($location, $id);
                $contentArr['custom_fields'] = $customFields;
            }
        }
        return array_merge($cmsContent->toArray(), $contentArr);
    }

    /**
     * 获取自定义字段
     * @param Form $form
     * @param $modelId
     * @return array
     */
    public function getCustomFields(Form $form, $modelId): array
    {
        $this->init($modelId);
        $items = [];
        if (! empty($this->model['_customFields'])) {
            $fields = $this->model['_customFields'];

            foreach ($fields as $field) {
                $cmsF = CmsField::getByNameOrFail($field['field_type']);
                $f = $cmsF->renderForForm($form, $field);
                if ($field['is_required']) {
                    $f->required(true)->disabled(true);
                }
                $items[] = $f;
            }
        }
        return $items;
    }

    public function getCustomGrids(Grid $grid, $modelId): array
    {
        $this->init($modelId);
        $grids = [];
        if (! empty($this->model['_customFields'])) {
            $fields = $this->model['_customFields'];

            foreach ($fields as $field) {
                if (! $field['is_list']) {
                    continue;
                }
                $cmsF = CmsField::getByNameOrFail($field['field_type']);
                $f = $cmsF->renderForGrid($grid, $field);
                $grids[] = $f;
            }
        }
        return $grids;
    }

    public function getContentListQuery($modelId, $lang, $categoryId = 0, $status = 1): Builder
    {
        $this->init($modelId);

        $modelDataTable = $this->modelDataTable;
        return CmsContent::query()
            ->where('cms_content.model_id', $this->modelId)
            ->when($status > 0, function ($query) use ($status) {
                $query->where('cms_content.status', $status);
            })
            ->where('cms_content.lang', $lang)
            // ->where('cms_content.deleted_at', '=', 0)
            ->when($categoryId > 0, function ($query) use ($categoryId) {
                return $query->where('cms_content.category_id', $categoryId);;
            })
            ->join($modelDataTable, function ($join) use ($modelDataTable, $lang) {
                $join->on('cms_content.id', '=', "$modelDataTable.content_id")->on('cms_content.lang', '=', "$modelDataTable.lang");
            })
            ->select(
                'cms_content.*',
                "$modelDataTable.*",
                "$modelDataTable.id as model_data_table_id", // 为动态表的id字段设置别名
                'cms_content.id',
            )->orderBy('cms_content.id', 'desc');
    }

    public function getContentListAside(int $modelId, string $lang): array
    {
        // 获取模型ID对应的CMS类别，并按sort排序
        $lists = CmsCategory::query()->where(['model_id' => $modelId, 'lang' => $lang])
            ->orderBy('sort')->get()->toArray();

        // 使用arr2tree函数将列表转换为树形结构
        $lists = arr2tree($lists, 'id', 'pid');

        // 递归函数来格式化树形结构
        $formatTree = function ($items) use (&$formatTree) {
            $result = [];
            foreach ($items as $item) {
                $formattedItem = [
                    'label' => $item['title'],
                    'value' => $item['id'],
                ];
                // 如果存在子类别，递归调用来格式化子类别
                if (! empty($item['children'])) {
                    $formattedItem['children'] = $formatTree($item['children']);
                } else {
                    $formattedItem['children'] = [];
                }
                $result[] = $formattedItem;
            }
            return $result;
        };

        // 对最顶层的列表使用格式化函数
        return $formatTree($lists);
    }

    /**
     * 获取内容详情
     * @return array
     * @throws BizException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getCommonTags(): array
    {
        if (bingostart_module_enabled('TagManager')) {
            $tags = Tags::query()->where('module', 'cms')->get()->toArray();
            return array_map(function ($tag) {
                return [
                    'label' => $tag['name'],
                    'value' => $tag['name'],
                ];
            }, $tags);
        }
        return [];
    }

    /**
     * 根据内容组ID获取多语言内容
     * @param int $contentGroupId 内容组ID
     * @return array
     * @throws BizException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getMultilingualContentByGroupId(int $contentGroupId): array
    {
        $contents = CmsContent::where('content_group_id', $contentGroupId)->get();

        if ($contents->isEmpty()) {
            BizException::throws(CmsErrorCode::CONTENT_NOT_FOUND);
        }

        $result = [];

        foreach ($contents as $content) {
            $this->init($content->model_id);

            $contentArr = ModelUtil::get($this->modelDataTable, [
                ['content_id', '=', $content->id],
                ['lang', '=', $content->lang]
            ]);
            if (empty($contentArr)) {
                $contentArr = [];
            }
            unset($contentArr['id']);

            // 获取分类名称
            $category = CmsCategory::find($content->category_id);
            $contentArr['category_name'] = $category ? $category->title : null;

            // 渲染CFM 自定义字段
            $customFields = [];
            if (bingostart_module_enabled('CFM')) {
                $cmsModel = DB::table('cms_model')->where('id', $content->model_id)->select('name')->first();
                if ($cmsModel) {
                    $location = "cms_" . $cmsModel->name;
                    $fieldService = new \Modules\CFM\Services\FieldService(new \Modules\CFM\Repositories\Eloquent\EloquentFieldRepository());
                    $customFields = $fieldService->getCustomFieldData($location, $content->id);
                }
            }
            $contentArr['custom_fields'] = $customFields;

            // 合并内容数据
            $mergedContent = array_merge($content->toArray(), $contentArr);

            $result[$content->lang] = $mergedContent;
        }
        return $result;
    }

    /**
     * 获取版本列表
     * @param int $model_id 模型ID
     * @param int $group_id 内容组ID
     * @return array 版本列表
     */
    public function getVersionList($model_id, $group_id): array
    {
        if ($model_id <= 0 || $group_id <= 0) {
            return [];
        }

        $versionList = CmsContent::withTrashed()
            ->where('model_id', $model_id)
            ->where('content_group_id', $group_id)
            ->select('version')
            ->groupBy('version')
            ->orderBy('version', 'desc')
            ->get();

        if ($versionList->isEmpty()) {
            return [];
        }

        $version_list = [];
        foreach ($versionList as $item) {
            $version = $item->version;

            // 获取该版本的内容信息
            $versionContent = CmsContent::withTrashed()
                ->where('model_id', $model_id)
                ->where('content_group_id', $group_id)
                ->where('version', $version)
                ->first();

            // 检查是否有已审核的内容
            $is_approve = CmsContent::withTrashed()->where('model_id', $model_id)
                ->where('content_group_id', $group_id)
                ->where('status', 2)
                ->where('version', $version)
                ->exists();
            $status = $versionContent ? $versionContent->status : 2;
            $is_show = ($versionContent->deleted_at && $status == 1) ? 1 : 0;

            $approve = [
                'status' => 1,
                'status_text' => '审批中',
                'step' => [
                    "id" => 9,
                    "name" => "财务审批",
                    "sort" => 1,
                    "status" => "completed",
                    "approver_name" => "1111",
                    "group_name" => "test",
                    "created_at" => "2025-03-04 10:20:46"
                ]
            ];
            $version_list[] = [
                'version' => $version,
                'is_approve' => $is_approve ? 1 : 0,
                'created_at' => $versionContent ? $versionContent->created_at->toDateTimeString() : null,
                'updated_at' => $versionContent ? $versionContent->updated_at->toDateTimeString() : null,
                'creator' => $versionContent ? $versionContent->creator_id : null,
                'status' => $status,
                'title' => $versionContent ? $versionContent->title : null,
                'is_show' => $is_show,
                'approve' => $approve,
            ];
        }

        return $version_list;
    }

    /**
     * 应用版本
     * @param int $group_id 内容组ID
     * @param int $version 版本
     * @return int
     */
    public function applyVersion($group_id, $version)
    {
        $contents = CmsContent::withTrashed()->where('content_group_id', $group_id)->where('version', $version)->where('status', 1)->get();
        if ($contents->isEmpty()) {
            BizException::throws(CmsErrorCode::CONTENT_NOT_FOUND);
        }
        foreach ($contents as $content) {
            $content->update(['deleted_at' => 0]);
        }
        CmsContent::withTrashed()->where('content_group_id', $group_id)->where('status', 1)->where('version', '!=', $version)->delete();
        return $group_id;
    }

    /**
     * 创建多语言内容
     * @param array $data
     * @param int $modelId
     * @param int $groupId
     * @return int
     * @throws BizException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function saveMultipleContent(array $data, int $modelId, int $groupId = 0): int
    {
        if ($modelId <= 0) {
            throw new BizException(Code::FAILED, 'error model_id');
        }

        $this->init($modelId);

        DB::beginTransaction();
        $copyId = $data['_copyId'] ?? 0;
        try {
            // 使用 Model 创建或获取 content_group
            if ($groupId) {
                $contentGroup = ContentGroups::findOrFail($groupId);
            } else {
                $contentGroup = ContentGroups::create([
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
            }

            $contentGroupId = $contentGroup->id;

            // 获取可用的语言列表
            $availableLanguages = array_column(T_list(), 'value');
            $max_version = CmsContent::withTrashed()->where('model_id', $modelId)
                ->where('content_group_id', $contentGroupId)
                ->max('version');
            $max_version = $max_version ?? 0;

            // 使用 PHP 内置函数代替 bcadd
            $now_version = (int)$max_version + 1;

            foreach ($availableLanguages as $lang) {
                if ((! isset($data[$lang]) || ! is_array($data[$lang])) && $copyId == 0) {
                    continue; // 如果某种语言的数据不存在或不是数组，跳过处理
                }

                $langData = $data[$lang] ?? [];

                if ($copyId > 0) {
                    $copy_id = CmsContent::where('content_group_id', $copyId)->where('lang', $lang)->first();
                    if ($copy_id) {
                        $langData['_copyId'] = $copy_id->id;
                    }
                }
                $langData['content_group_id'] = $contentGroupId;
                $langData['lang'] = $lang;
                $langData['model_id'] = $modelId; // 确保每种语言的数据都使用正确的 model_id
                $langData['version'] = $now_version;
                $langData['status'] = 2;

                $id = $langData['id'] ?? 0;
                if ($id > 0) {
                    $approve_status = CmsContent::find($id)->status;
                    if ($approve_status == 2) {
                        $langData['_del_id'] = $id;
                        // 记录版本
                        CmsContent::where('id', $id)->delete();
                    }
                    $id = 0;
                }

                // 调用 saveContent 方法保存每种语言的内容
                $contentId = $this->saveContent($langData, $modelId, $id);
                // 如果是新创建的内容，确保 content_group_id 被正确设置
                if ($id == 0) {
                    CmsContent::where('id', $contentId)->update(['content_group_id' => $contentGroupId]);
                }

                $seo_analysis_id = $langData['seo_analysis_id'] ?? 0;
                if ($seo_analysis_id > 0) {
                    app(SeoAnalysisService::class)->setAnalysisByContentId($contentId, $seo_analysis_id);
                }
                try {

                    // setAnalysisByContentId($contentId, $contentId);
                    app('log')->info('触发cms审核事件', ['model_name' => $this->model['name'], 'contentId' => $contentId, 'group_id' => $contentGroupId]);
                    event(new ApprovalCreated($this->model['name'], $contentId));
                } catch (\Throwable $th) {
                    //throw $th;
                }
            }

            DB::commit();
            return $contentGroupId;
        } catch (Exception $e) {
            DB::rollBack();
            throw new BizException(Code::FAILED, 'save content failed：' . $e->getMessage());
        }
    }

    /**
     * 更新内容状态
     * @param int $id
     * @param int $status
     * @return int
     */
    public function updateStatusById(int $id, int $status)
    {
        $content = CmsContent::find($id);
        if ($status == 1) {
            $version = $content->version;
            $group_id = $content->content_group_id;
            $lang = $content->lang;
            $model_id = $content->model_id;
            CmsContent::where('version', '<', $version)->where('lang', $lang)->where('content_group_id', $group_id)->where('model_id', $model_id)->delete();
        }
        ModelUtil::update($this->modelTable, $id, ['status' => $status]);
        return $id;
    }

    public function getContentTotal($model_id, $lang)
    {
        $this->init($model_id);
        $modelDataTable = $this->modelDataTable;

        // 创建基础查询
        $baseQuery = function() use ($model_id, $lang, $modelDataTable) {
            return CmsContent::query()
                ->where('cms_content.model_id', $model_id)
                ->where('cms_content.lang', $lang)
                ->where('cms_content.status', '>=', 0)
                ->join($modelDataTable, function ($join) use ($modelDataTable) {
                    $join->on('cms_content.id', '=', "$modelDataTable.content_id")
                         ->on('cms_content.lang', '=', "$modelDataTable.lang");
                });
        };

        // 为每个状态创建独立的查询
        $displaying = $baseQuery()->where('cms_content.status', 1)->count();
        $awaiting = $baseQuery()->where('cms_content.status', 2)->count();
        $draft = $baseQuery()->where('cms_content.status', 3)->count();
        $approved = $baseQuery()->count();

        return ['approved' => $approved, 'displaying' => $displaying, 'awaiting' => $awaiting, 'draft' => $draft];
    }
}
