<?php

namespace Modules\Cms\Services;

use Bingo\Amis\Components\Form;
use Bingo\Amis\Components\Grid;
use Bingo\Exceptions\BizException;
use Illuminate\Database\Eloquent\Builder;
use Modules\Cms\Domain\Content;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Modules\Cms\Domain\CmsModelUtil;
use Modules\Approval\Events\ApprovalCreated;

class CmsContentService
{
    public function __construct(
        protected readonly Content $content
    ) {

    }

    public function getContentListQuery(int $modelId, string $lang, int $categoryId, int $status): Builder
    {
        return $this->content->getContentListQuery($modelId, $lang, $categoryId, $status);
    }

    /**
     * 保存内容
     * @param array $data
     * @param int $modelId
     * @param int $id
     * @return int
     * @throws BizException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function saveContent(array $data, int $modelId, int $id = 0): int
    {
        return $this->content->saveContent($data, $modelId, $id);
    }

    public function deleteContentId($id): int
    {

        return $this->content->deleteContent($id);
    }

    public function getContentQuery($id): string
    {
        if ($id > 0) {
            return $this->content->getContentQuery($id);
        }
        return "";
    }
    /**
     * 更新内容状态
     * @param int $id
     * @param int $status
     * @return int
     */
    public function updateStatusById(int $id,int $status) : void {
        $this->content->updateStatusById($id,$status);
    }

    public function getCustomFields(Form $form, $modelId): array
    {
        return $this->content->getCustomFields($form, $modelId);
    }

    public function getCustomGrids(Grid $grid, $modelId): array
    {
        return $this->content->getCustomGrids($grid, $modelId);
    }

    public function getContentListAside($modelId, $lang): array
    {
        return $this->content->getContentListAside($modelId, $lang);

    }

    /**
     * 获取通用标签
     * @return array
     * @throws BizException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getCommonTags(): array
    {
        return $this->content->getCommonTags();
    }

    /**
     * 根据ID获取内容
     * @param mixed $id
     * @return array|string|null
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getContentById(mixed $id): array|string|null
    {
        if ($id > 0) {
            return $this->content->getContentById($id);
        }
        return "";
    }


    /**
     * 保存内容
     * @param array $data
     * @param int $modelId
     * @param int $groupId
     * @return int
     * @throws NotFoundExceptionInterface
     */
    public function saveMultipleContent(array $data, int $modelId, int $groupId = 0): int
    {
        $data_id =$this->content->saveMultipleContent($data, $modelId, $groupId);
        return $data_id;
    }

    public function getContentTotal($model_id,$lang) {
        return $this->content->getContentTotal($model_id,$lang);
    }

    /**
     * 根据ID获取多语言内容
     * @param mixed $contentGroupId 语言组ID
     * @return array|string|null
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface|BizException
     */
    public function getMultilingualContentByGroupId(mixed $contentGroupId): array|string|null
    {
        if ($contentGroupId > 0) {
            return $this->content->getMultilingualContentByGroupId($contentGroupId);
        }
        return "";
    }

    public function getVersionList($model_id,$group_id) {
        return $this->content->getVersionList($model_id,$group_id);
    }

    public function applyVersion($group_id,$version) {
        return $this->content->applyVersion($group_id,$version);
    }
    
}
