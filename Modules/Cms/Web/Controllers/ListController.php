<?php

namespace Modules\Cms\Web\Controllers;

use Bingo\Core\Input\InputPackage;
use Bingo\Core\Input\Request;
use Bingo\Core\Util\PageHtmlUtil;
use Bingo\Core\View\ModuleResponsiveViewTrait;
use Modules\Cms\Api\Controller\BaseCatController;
use Modules\Cms\Domain\CmsContentUtil;
use Modules\Cms\Domain\CmsTemplateUtil;
use Modules\SEO\Services\CmsSeoService;

class ListController extends BaseCatController
{
    use ModuleResponsiveViewTrait;

    public function index(CmsSeoService $cmsSeoService, $id = 0)
    {
        $data = parent::setup($id);
        $view = $this->getView($data, 'list_template');
        $cat = $data['cat'];
        $model = $data['model'];
        $input = InputPackage::buildFromInput();
        $page = $input->getPage();
        if (empty($cat['pageSize']) || $cat['pageSize'] < 0) {
            $cat['pageSize'] = 12;
        }
        $pageSize = $input->getPageSize('pageSize', null, null, $cat['pageSize']);

        $option = [];
        $option = CmsContentUtil::buildFilter($option, $model);
        $paginateData = CmsContentUtil::paginateCat($cat['id'], $page, $pageSize, $option);
        CmsContentUtil::mergeRecordsData($paginateData['records'], [
            //            'canVisit' => CmsMemberPermitUtil::canVisitCat($cat),
        ]);

        $viewData = $data;
        $viewData['page'] = $page;
        $viewData['pageSize'] = $pageSize;
        $viewData['records'] = $paginateData['records'];
        $viewData['total'] = $paginateData['total'];
        $pageTemplate = '?'.Request::mergeQueries(['page' => ['{page}']]);
        if (! empty($cat['pageFullUrl'])) {
            $pageTemplate = bingostart_web_url($cat['pageFullUrl']);
        }
        $viewData['pageTemplate'] = $pageTemplate;
        $viewData['pageNextUrl'] = PageHtmlUtil::nextPageUrl($paginateData['total'], $pageSize, $page, $pageTemplate);
        $viewData['pagePrevUrl'] = PageHtmlUtil::prevPageUrl($paginateData['total'], $pageSize, $page, $pageTemplate);
        $viewData['pageHtml'] = PageHtmlUtil::render($paginateData['total'], $pageSize, $page, $pageTemplate);

        $viewData = $this->getSeo($viewData, $cat);

        // 应用SEO设置
        $lang = app()->getLocale();
        $pageData = [
            'title' => $cat['title'] ?? '',
            'description' => $cat['description'] ?? '',
            'keywords' => [],
            'seo_title' => $cat['seo_title'] ?? '',
            'seo_description' => $cat['seo_description'] ?? '',
            'seo_keywords' => $cat['seo_keywords'] ?? '',
            'content_id' => 0,
        ];

        // 获取SEO数据并合并到视图数据中
        $seoData = $cmsSeoService->getCmsPageSeoData($lang, $pageData);
        $viewData['seoData'] = $seoData;

        return $this->view('cms.list.'.CmsTemplateUtil::toBladeView($view), $viewData);
    }
}
