<?php

namespace Modules\Cms\Web\Controllers;

use Bingo\Core\Input\InputPackage;
use Bingo\Core\Input\Request;
use Bingo\Core\Util\PageHtmlUtil;
use Bingo\Core\View\ModuleResponsiveViewTrait;
use Modules\Cms\Api\Controller\BaseCatController;
use Modules\Cms\Domain\CmsContentUtil;
use Modules\Cms\Domain\CmsTemplateUtil;
use Modules\SEO\Services\CmsSeoService;

class PaymentController extends BaseCatController
{
    use ModuleResponsiveViewTrait;

    public function index(CmsSeoService $cmsSeoService, $id = 0)
    {
        return $this->view('cms.list.payment', [
            'pageTitle'=>'支付管理 | bingo香港網頁公司',
            "pageKeywords" => "支付管理",
            "pageDescription" => "支付管理",
            "cat" => [
                "title" => "支付管理",
                "description" => "支付管理",
                "seo_title" => "支付管理",
                "seo_keywords" => "支付管理",
                "seo_description" => "支付管理",
            ]
        ]);
    }
}
