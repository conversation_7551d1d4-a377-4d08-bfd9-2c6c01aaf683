<?php

/* @var Router $router */

use Illuminate\Routing\Router;
use Modules\Cms\Domain\CmsCatUtil;
use Modules\Cms\Enums\CmsMode;
use Modules\Cms\Enums\ContentUrlMode;
use Illuminate\Support\Facades\Route;
use Modules\Cms\Web\Controllers\DetailController;
use Modules\Cms\Web\Controllers\FormController;
use Modules\Cms\Web\Controllers\IndexController;
use Modules\Cms\Web\Controllers\ListController;
use Modules\Cms\Web\Controllers\PageController;
use Modules\Cms\Web\Controllers\TagController;
use Modules\Cms\Web\Controllers\PaymentController;

Route::group([
    'middleware' => [
        //        \Modules\Member\Middleware\WebAuthMiddleware::class,
        'seo.apply',
    ],
], function () use ($router) {
    Route::match(['get', 'post'], 'success', [IndexController::class, 'result']);
    Route::match(['get', 'post'], 'cancel', [IndexController::class, 'result']);
    Route::match(['get', 'post'], 'cms', [IndexController::class, 'index']);
    Route::match(['get', 'post'], 'tag/{tag}', [TagController::class, 'index']);
    Route::match(['get', 'post'], 'a/{alias_url}', [DetailController::class, 'index']);
    Route::match(['get', 'post'], 'c/{id}', [ListController::class, 'index']);
    Route::match(['get', 'post'], 'setLang', [IndexController::class, 'setLang']);
    $cats = CmsCatUtil::allSafelyHavingUrl();

    foreach ($cats as $item) {
        switch ($item['_model']['mode']) {
            case CmsMode::LIST_DETAIL->value:
                Route::match(['get'], $item['url'], [ListController::class, 'index']);
                break;
            case CmsMode::FORM->value:
                Route::match(['get'], $item['url'], [FormController::class, 'index']);
                Route::match(['post'], $item['url'], [FormController::class, 'submit']);
                break;
            case CmsMode::PAGE->value:
                Route::match(['get'], $item['url'], [PageController::class, 'index']);
                break;
        }
    }
    Route::match(['get'], 'payment', [PaymentController::class, 'index']);
    if (bingostart_config('Cms_ContentUrlMode') == ContentUrlMode::CAT->value) {
        foreach ($cats as $item) {
            Route::match(['get'], $item['url'].'/{alias_url}', [DetailController::class, 'index']);
        }
    }

});
