@extends($_viewFrame)
@php
    $view = new \Bingo\Core\Assets\View();
   \Modules\Cms\views\pc\bundles\PublicBundle::register($view);
@endphp
@section('pageTitleMain')
    {{$cat['seo_title']?:$cat['title']}}
@endsection
@section('pageKeywords')
    {{$cat['seo_keywords']?:$cat['title']}}
@endsection
@section('pageDescription')
    {{$cat['seo_description']?:$cat['title']}}
@endsection

@section('bodyContent')

    <div class="breadcrumbs">
        <div class="container">
            <ul>
                <li class="iconfont icon-home"></li>
                <li>首页</li>
                    <li class="iconfont icon-arrow-right"></li>
                    <li>支付管理</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="payment-section">
            <h3 class="payment-title">选择支付方式</h3>
            
            <!-- 香港支付方式区域 -->
            <div class="payment-category">
                <div class="payment-category-title">香港支付方式</div>
                <div class="payment-methods">
                    <div class="payment-method" data-method="fps_qrcode">
                        <div class="payment-method-inner">
                            <div class="payment-icon">
                                <img src="{{ asset('pay/images/fps.png') }}" alt="FPS" class="payment-logo">
                            </div>
                            <div class="payment-info">
                                <div class="payment-name">FPS</div>
                                <div class="payment-description">香港快速支付系統</div>
                            </div>
                        </div>
                    </div>
                    <div class="payment-method" data-method="payme">
                        <div class="payment-method-inner">
                            <div class="payment-icon">
                                <img src="{{ asset('pay/images/payme.png') }}" alt="PayMe" class="payment-logo">
                            </div>
                            <div class="payment-info">
                                <div class="payment-name">PayMe</div>
                                <div class="payment-description">香港流行支付方式</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 内地支付方式区域 -->
            <div class="payment-category">
                <div class="payment-category-title">內地支付方式</div>
                <div class="payment-methods">
                    <div class="payment-method" data-method="wechat_hk">
                        <div class="payment-method-inner">
                            <div class="payment-icon">
                                <img src="{{ asset('pay/images/wechat.png') }}" alt="WeChat Pay" class="payment-logo">
                            </div>
                            <div class="payment-info">
                                <div class="payment-name">微信支付</div>
                                <div class="payment-description">微信香港錢包</div>
                            </div>
                        </div>
                    </div>
                    <div class="payment-method" data-method="alipay_hk">
                        <div class="payment-method-inner">
                            <div class="payment-icon">
                                <img src="{{ asset('pay/images/alipay.png') }}" alt="Alipay" class="payment-logo">
                            </div>
                            <div class="payment-info">
                                <div class="payment-name">支付寶</div>
                                <div class="payment-description">支付寶香港錢包</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 国际支付方式区域 -->
            <div class="payment-category">
                <div class="payment-category-title">國際支付方式</div>
                <div class="payment-methods">
                    <div class="payment-method" data-method="paypal">
                        <div class="payment-method-inner">
                            <div class="payment-icon international">
                                <img src="{{ asset('pay/images/paypal.png') }}" alt="PayPal" class="payment-logo">
                            </div>
                            <div class="payment-info">
                                <div class="payment-name">PayPal</div>
                                <div class="payment-description">國際流行支付方式</div>
                            </div>
                        </div>
                    </div>
                    <div class="payment-method" data-method="stripe">
                        <div class="payment-method-inner">
                            <div class="payment-icon international">
                                <img src="{{ asset('pay/images/stripe.png') }}" alt="Stripe" class="payment-logo">
                            </div>
                            <div class="payment-info">
                                <div class="payment-name">Stripe</div>
                                <div class="payment-description">國際信用卡支付</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支付信息区域 -->
            <div class="payment-info">
                <div class="payment-info-row">
                    <div class="payment-info-label">訂單編號：</div>
                    <div class="payment-info-value" id="order-no">M17459263775921895</div>
                </div>
                <div class="payment-info-row">
                    <div class="payment-info-label">商品名稱：</div>
                    <div class="payment-info-value">支付接口演示</div>
                </div>
                <div class="payment-info-row">
                    <div class="payment-info-label">支付金額(HKD)：</div>
                    <div class="payment-info-value">
                        <div class="payment-amount-options">
                            <div class="amount-option">
                                <input type="radio" id="amount-10" name="amount" value="10.00" class="amount-radio" checked>
                                <label for="amount-10" class="amount-label">HK$10.00</label>
                            </div>
                            <div class="amount-option">
                                <input type="radio" id="amount-50" name="amount" value="50.00" class="amount-radio">
                                <label for="amount-50" class="amount-label">HK$50.00</label>
                            </div>
                            <div class="amount-option">
                                <input type="radio" id="amount-100" name="amount" value="100.00" class="amount-radio">
                                <label for="amount-100" class="amount-label">HK$100.00</label>
                            </div>
                            <div class="amount-option">
                                <input type="radio" id="amount-200" name="amount" value="200.00" class="amount-radio">
                                <label for="amount-200" class="amount-label">HK$200.00</label>
                            </div>
                            <div class="amount-option">
                                <input type="radio" id="amount-500" name="amount" value="500.00" class="amount-radio">
                                <label for="amount-500" class="amount-label">HK$500.00</label>
                            </div>
                            <div class="amount-option">
                                <input type="radio" id="amount-custom" name="amount" value="custom" class="amount-radio">
                                <label for="amount-custom" class="amount-label">自定義金額</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FPS上传收据区域 -->
            <div class="upload-section" id="fps-upload-section">
                <div class="upload-title">上傳FPS轉賬收據</div>
                <div class="upload-description">請將您的FPS轉賬收據截圖上傳，我們將在確認後完成訂單處理。</div>
                <div class="upload-button">
                    <i class="fa fa-upload"></i> 點擊上傳收據
                </div>
            </div>

            <!-- 二维码显示区域 -->
            <div class="qrcode-section" id="qrcode-section">
                <div class="qrcode-title">掃描二維碼完成支付</div>
                <div class="qrcode-image">
                    <!-- 这里放二维码图片 -->
                    <img src="data:image/png;base64,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
                </div>
                <div class="qrcode-description">請使用手機掃描上方二維碼進行支付，支付完成後頁面將自動跳轉</div>
            </div>

            <div class="payment-submit">
                <span class="payment-price">HK$ 10.00</span>
                <button class="payment-button">立即支付</button>
                <div class="payment-hint">請確認支付方式和金額後再點擊支付按鈕</div>
            </div>
        </div>

        <!-- 最新支付记录 -->
        <div class="payment-section">
            <h3 class="payment-title">最新支付記錄 <span id="refresh-records" style="font-size:12px;font-weight:normal;color:#999;cursor:pointer;">刷新</span></h3>
            
            <table class="payment-table">
                <thead>
                    <tr>
                        <th>訂單編號</th>
                        <th>支付金額</th>
                        <th>支付方式</th>
                        <th>支付狀態</th>
                        <th>客戶IP</th>
                        <th>創建時間</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="payment-records">
                    <!-- 数据会通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Stripe嵌入式结账容器 -->
    <div class="stripe-container" id="stripe-container" style="display:none;">
        <div class="stripe-wrapper">
            <div class="stripe-header">
                <h3>Stripe支付</h3>
                <span class="stripe-close">&times;</span>
            </div>
            <div id="stripe-checkout-container"></div>
        </div>
    </div>
    <script src="https://js.stripe.com/v3/"></script>
    <script src="https://www.paypal.com/sdk/js?client-id={{$config['client_id'] ?? 'test'}}&currency=HKD&intent=capture&enable-funding=venmo,card&disable-funding=credit"></script>
    <script>
        // 定义全局 stripe 变量
        let stripe = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 定义加载状态管理函数 - 确保最先定义
            function showLoading(message) {
                const loadingEl = document.createElement('div');
                loadingEl.className = 'loading-overlay';
                loadingEl.innerHTML = `
                    <div class="loading-spinner"></div>
                    <div class="loading-message">${message || '处理中...'}</div>
                `;
                document.body.appendChild(loadingEl);
            }
            
            function hideLoading() {
                const loadingEl = document.querySelector('.loading-overlay');
                if (loadingEl) {
                    document.body.removeChild(loadingEl);
                }
            }
            
            // 检查URL中的Stripe session_id并处理支付状态
            async function checkStripeSessionId() {
                const urlParams = new URLSearchParams(window.location.search);
                const sessionId = urlParams.get('session_id');
                
                if (sessionId) {
                    console.log('检测到Stripe session_id:', sessionId);
                    
                    // 显示加载状态
                    showLoading('正在验证支付状态...');
                    
                    try {
                        // 直接调用查询接口，后端会处理状态更新
                        const response = await fetch(`/api/pay/v2/query?session_id=${sessionId}`);
                        
                        if (!response.ok) {
                            throw new Error(`查询支付状态失败: ${response.status}`);
                        }
                        
                        const result = await response.json();
                        console.log('Stripe会话查询结果:', result);
                        
                        // 显示结果
                        hideLoading();
                        
                        if (result.code === 200) {
                            if (result.data.state === 2) {
                                alert('支付成功！');
                            } else if (result.data.state === 0 || result.data.state === 1) {
                                alert('支付处理中，请稍后查看支付记录');
                            } else {
                                alert(`支付结果: ${getStatusText(result.data.state)}`);
                            }
                        } else {
                            alert(`查询支付结果: ${result.message || '未知状态'}`);
                        }
                        
                        // 刷新支付记录
                        loadPaymentRecords();
                        
                        // 清除URL参数
                        window.history.replaceState({}, document.title, window.location.pathname);
                    } catch (error) {
                        hideLoading();
                        console.error('查询Stripe支付状态错误:', error);
                        alert(`查询支付状态异常: ${error.message}`);
                        
                        // 刷新支付记录
                        loadPaymentRecords();
                        
                        // 清除URL参数
                        window.history.replaceState({}, document.title, window.location.pathname);
                    }
                }
            }
            
            // 支付方式选择
            const paymentMethods = document.querySelectorAll('.payment-method');
            const fpsUploadSection = document.getElementById('fps-upload-section');
            const qrcodeSection = document.getElementById('qrcode-section');
            const stripeContainer = document.getElementById('stripe-container');
            
            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    // 清除之前的选中状态
                    paymentMethods.forEach(m => m.classList.remove('active'));
                    
                    // 设置当前选中
                    this.classList.add('active');
                    
                    // 检查选择的支付方式
                    const methodType = this.getAttribute('data-method');
                    
                    // 隐藏所有特殊区域
                    fpsUploadSection.classList.remove('active');
                    qrcodeSection.classList.remove('active');
                    
                    // 显示对应的特殊区域
                    if (methodType === 'fps_upload') {
                        fpsUploadSection.classList.add('active');
                    }
                });
            });
            
            // 金额选择
            const amountRadios = document.querySelectorAll('.amount-radio');
            const priceDisplay = document.querySelector('.payment-price');
            
            amountRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value !== 'custom') {
                        priceDisplay.textContent = 'HK$ ' + this.value;
                    } else {
                        const customAmount = prompt('請輸入自定義金額（HKD）：', '100.00');
                        if (customAmount && !isNaN(customAmount)) {
                            priceDisplay.textContent = 'HK$ ' + parseFloat(customAmount).toFixed(2);
                        } else {
                            // 如果输入无效，重新选择第一个选项
                            document.getElementById('amount-10').checked = true;
                            priceDisplay.textContent = 'HK$ 10.00';
                        }
                    }
                });
            });
            
            // 上传按钮功能
            const uploadButton = document.querySelector('.upload-button');
            if (uploadButton) {
                uploadButton.addEventListener('click', function() {
                    alert('模擬上傳收據功能');
                    // 在实际项目中，这里会触发文件选择框
                });
            }
            
            // 加载支付记录
            function loadPaymentRecords() {
                showLoading('加载支付记录...');
                
                fetch('/api/pay/v2/orders?limit=5')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('获取支付记录失败');
                        }
                        return response.json();
                    })
                    .then(result => {
                        hideLoading();
                        
                        if (result.code !== 200) {
                            console.error('获取支付记录失败:', result.msg);
                            return;
                        }
                        
                        const records = result.data.items || [];
                        const recordsContainer = document.getElementById('payment-records');
                        
                        if (records.length === 0) {
                            recordsContainer.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无支付记录</td></tr>';
                            return;
                        }
                        console.log("records=====>",records);
                        let html = '';
                        records.forEach(record => {
                            const statusClass = getStatusClass(record.state);
                            const statusText = getStatusText(record.state);
                            
                            html += `
                                <tr>
                                    <td>${record.pay_order_id}</td>
                                    <td>${record.currency} ${(record.amount/100).toFixed(2)}</td>
                                    <td>${record.if_code}</td>
                                    <td><span class="payment-status ${statusClass}">${statusText}</span></td>
                                    <td>${record.client_ip ? record.client_ip.replace(/(\d+\.\d+\.\d+\.)\d+/, '$1*') : '-'}</td>
                                    <td>${record.created_at}</td>
                                    <td><span class="payment-operate" data-id="${record.id}" data-pay-order-id="${record.pay_order_id}" data-payway="${record.if_code}">${record.state === 2 ? '退款' : '查詢'}</span></td>
                                </tr>
                            `;
                        });
                        
                        recordsContainer.innerHTML = html;
                        
                        // 绑定操作按钮点击事件
                        document.querySelectorAll('.payment-operate').forEach(button => {
                            button.addEventListener('click', function() {
                                const orderId = this.getAttribute('data-pay-order-id');
                                const action = this.textContent;
                                const payway = this.getAttribute('data-payway');
                             
                                if (action === '退款') {
                                    if(payway == 'payme' || payway == 'fps'){
                                        alert('Payme 和 FPS 不支持退款');
                                        return;
                                    }
                                    if (confirm('确定要对订单 ' + orderId + ' 进行退款吗？')) {
                                        // 显示加载状态
                                        showLoading('正在处理退款请求...');
                                        
                                        // 调用退款API
                                        fetch('/api/pay/v2/refund/create', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json',
                                            },
                                            body: JSON.stringify({
                                                pay_order_id: orderId
                                            })
                                        })
                                        .then(response => {
                                            if (!response.ok) {
                                                throw new Error(`退款请求失败: ${response.status} ${response.statusText}`);
                                            }
                                            return response.json();
                                        })
                                        .then(result => {
                                            hideLoading();
                                            console.log('退款结果:', result);
                                            
                                            if (result.code === 200) {
                                                alert(`退款申请成功!\n退款单号: ${result.data.refund_order_id}`);
                                                // 刷新支付记录
                                                loadPaymentRecords();
                                            } else {
                                                alert(`退款失败: ${result.message || '未知错误'}`);
                                            }
                                        })
                                        .catch(error => {
                                            hideLoading();
                                            console.error('退款请求异常:', error);
                                            alert(`退款异常: ${error.message}`);
                                        });
                                    }
                                } else {
                                    alert('查询订单: ' + orderId);
                                    // 实际项目中，这里会查询并显示订单详情
                                }
                            });
                        });
                    })
                    .catch(error => {
                        hideLoading();
                        console.error('获取支付记录异常:', error);
                        alert(`获取支付记录失败: ${error.message}`);
                    });
            }
            
            // 获取状态样式类
            function getStatusClass(state) {
                switch (state) {
                    case 2: return 'status-success';
                    case 0: case 1: return 'status-pending';
                    case 3: case 4: case 6: return 'status-failed';
                    case 5: return 'status-refund';
                    default: return 'status-pending';
                }
            }
            
            // 获取状态文本
            function getStatusText(state) {
                switch (state) {
                    case 0: return '订单生成';
                    case 1: return '支付中';
                    case 2: return '支付成功';
                    case 3: return '支付失败';
                    case 4: return '已撤销';
                    case 5: return '已退款';
                    case 6: return '订单关闭';
                    default: return '未知状态';
                }
            }
            
            // 刷新按钮点击事件
            document.getElementById('refresh-records').addEventListener('click', loadPaymentRecords);
            
            // 页面加载完成后自动加载支付记录
            loadPaymentRecords();
            
            // PayPal支付处理函数
            async function handlePaypalPayment(amount, orderNo) {
                try {
                    // 显示加载状态
                    showLoading('正在创建PayPal支付...');
                    
                    // 准备支付数据
                    const paymentData = {
                        mch_order_no: orderNo,
                        if_code: 'paypal',
                        way_code: 'web',
                        amount: parseFloat(amount) * 100, // 转换为分
                        currency: 'HKD',
                        subject: '支付接口演示',
                        body: '订单支付',
                        notify_url: window.location.origin + '/api/pay/webhook/paypal',
                        return_url: window.location.origin + '/payment/result'
                    };
                    
                    console.log('发送PayPal支付请求:', paymentData);
                    
                    // 调用支付API创建支付
                    const response = await fetch('/api/pay/v2/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(paymentData)
                    });
                    
                    if (!response.ok) {
                        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                    }
                    
                    const result = await response.json();
                    hideLoading();
                    console.log("PayPal API响应:", result);
                    
                    if (result.code !== 200) {
                        throw new Error(`创建PayPal支付失败: ${result.message || '未知错误'}`);
                    }
                    
                    // 获取支付URL，直接跳转到PayPal支付页面
                    if (result.data && result.data.pay_url) {
                        // 为用户提供说明
                        alert('即将跳转至PayPal支付页面。在付款页面请点击"用借记卡或信用卡支付"按钮，无需注册PayPal账户。');
                        
                        // 打开新窗口支付
                        window.open(result.data.pay_url, '_blank');
                    } else {
                        throw new Error('响应中缺少PayPal支付链接');
                    }
                    
                } catch (error) {
                    hideLoading();
                    console.error('PayPal支付处理错误:', error);
                    alert(`PayPal支付处理错误: ${error.message}`);
                }
            }
            
            // 检查PayPal返回
            function checkPayPalReturn() {
                const urlParams = new URLSearchParams(window.location.search);
                const token = urlParams.get('token');
                const payerId = urlParams.get('PayerID');
                
                // 如果有PayPal返回的参数
                if (token && payerId) {
                    showLoading('正在完成支付...');
                    
                    // 调用捕获支付API
                    fetch('/api/pay/v2/paypal/capture', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            token: token,
                            payer_id: payerId
                        })
                    })
                    .then(response => response.json())
                    .then(result => {
                        hideLoading();
                        
                        if (result.code === 200) {
                            alert('支付成功完成！');
                            // 刷新页面
                            window.location.reload();
                        } else {
                            alert(`支付处理失败: ${result.message || '未知错误'}`);
                        }
                        
                        // 清除URL参数
                        window.history.replaceState({}, document.title, window.location.pathname);
                    })
                    .catch(error => {
                        hideLoading();
                        console.error('支付捕获错误:', error);
                    });
                }
            }
            
            // Stripe支付处理函数
            async function handleStripePayment(amount, orderNo) {
                try {
                    // 显示加载状态
                    showLoading('正在创建支付会话...');
                    
                    // 准备支付数据
                    const paymentData = {
                        mch_order_no: orderNo,
                        if_code: 'stripe',
                        way_code: 'web',
                        amount: parseFloat(amount) * 100, // 转换为分
                        currency: 'HKD',
                        subject: '支付接口演示',
                        body: '订单支付',
                        notify_url: window.location.origin + '/api/pay/webhook/stripe',
                        return_url: window.location.origin + '/payment', // 返回当前页面，但不带任何其他参数
                        client_ip: '127.0.0.1'
                    };
                    
                    console.log('发送支付请求:', paymentData);
                    
                    // 调用支付API创建支付会话
                    const response = await fetch('/api/pay/v2/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(paymentData)
                    });
                    
                    if (!response.ok) {
                        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                    }
                    
                    const result = await response.json();
                    hideLoading();
                    console.log("API响应:", result);
                    
                    // 检查响应结构
                    if (result.code !== 200) {
                        throw new Error(`创建支付会话失败: ${result.message}`);
                    }
                    
                    // 从data中获取支付信息
                    const payData = result.data;
                    
                    // 检查必要字段
                    if (!payData) {
                        throw new Error('响应中缺少data字段');
                    }
                    
                    console.log('支付数据:', payData);
                    
                    // 从嵌套结构中获取client_secret
                    let clientSecret = null;
                    let pubKey = payData.pub_key || ''; // 获取 Stripe 公钥
                    
                    // 提取 client_secret
                    if (payData.client_secret) {
                        clientSecret = payData.client_secret;
                    } else if (payData.pay_data && payData.pay_data.client_secret) {
                        clientSecret = payData.pay_data.client_secret;
                    } else if (payData.channel_extra) {
                        try {
                            const extraData = typeof payData.channel_extra === 'string' 
                                ? JSON.parse(payData.channel_extra) 
                                : payData.channel_extra;
                            
                            if (extraData && extraData.client_secret) {
                                clientSecret = extraData.client_secret;
                            }
                        } catch (e) {
                            console.error('解析channel_extra失败:', e);
                        }
                    }
                    
                    // 确保我们有客户端密钥
                    if (!clientSecret) {
                        throw new Error('无法获取支付会话密钥 (client_secret)');
                    }
                    
                    // 确保我们有公钥
                    if (!pubKey) {
                        throw new Error('无法获取 Stripe 公钥');
                    }
                    
                    // 初始化 Stripe 对象
                    stripe = Stripe(pubKey);
                    
                    // 显示Stripe容器
                    stripeContainer.style.display = 'flex';
                    
                    // 初始化嵌入式结账
                    const checkout = await stripe.initEmbeddedCheckout({
                        clientSecret: clientSecret
                    });
                    
                    // 挂载到容器元素
                    checkout.mount('#stripe-checkout-container');
                    
                    // 监听关闭按钮
                    document.querySelector('.stripe-close').addEventListener('click', function() {
                        checkout.destroy();
                        stripeContainer.style.display = 'none';
                    });
                    
                } catch (error) {
                    hideLoading();
                    console.error('Stripe支付处理错误:', error);
                    alert(`支付处理错误: ${error.message}`);
                }
            }
            
            // 通用支付处理函数
            async function handlePayment(amount, orderNo, paymentMethod) {
                try {
                    // 显示加载状态
                    showLoading(`正在创建${getPaymentName(paymentMethod)}支付...`);
                    
                    // 确定支付配置
                    const paymentConfig = getPaymentConfig(paymentMethod, amount, orderNo);
                    
                    console.log(`发送${getPaymentName(paymentMethod)}支付请求:`, paymentConfig);
                    
                    // 调用支付API创建支付
                    const response = await fetch('/api/pay/v2/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(paymentConfig)
                    });
                    
                    if (!response.ok) {
                        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                    }
                    
                    const result = await response.json();
                    hideLoading();
                    console.log(`${getPaymentName(paymentMethod)}API响应:`, result);
                    
                    if (result.code !== 200) {
                        throw new Error(`创建${getPaymentName(paymentMethod)}支付失败: ${result.message || '未知错误'}`);
                    }
                    
                    // 获取支付数据
                    const payData = result.data;
                    
                    // 根据支付方式处理不同的展示逻辑
                    if (['payme', 'fps_qrcode', 'wechat_hk', 'alipay_hk'].includes(paymentMethod)) {
                        // 检查二维码URL
                        if (!payData || !payData.pay_data || !payData.pay_data.qrcode_url) {
                            throw new Error('响应中缺少二维码链接');
                        }
                        
                        // 显示二维码区域
                        qrcodeSection.classList.add('active');
                        
                        // 更新二维码图片
                        const qrcodeImage = qrcodeSection.querySelector('.qrcode-image img');
                        if (qrcodeImage) {
                            qrcodeImage.src = payData.pay_data.qrcode_url;
                        } else {
                            console.error('无法找到二维码图片元素');
                        }
                        
                        // 更新二维码标题和说明
                        const qrcodeTitle = qrcodeSection.querySelector('.qrcode-title');
                        const qrcodeDescription = qrcodeSection.querySelector('.qrcode-description');
                        
                        if (qrcodeTitle) {
                            qrcodeTitle.textContent = `${getPaymentName(paymentMethod)}付款`;
                        } else {
                            console.error('无法找到二维码标题元素');
                        }
                        
                        if (qrcodeDescription) {
                            qrcodeDescription.textContent = payData.pay_data.instructions || 
                                `請使用${getPaymentName(paymentMethod)}掃描上方二維碼進行支付`;
                        } else {
                            console.error('无法找到二维码说明元素');
                        }
                        
                        // 添加上传凭证按钮（只对特定支付方式添加）
                        if (['payme', 'fps_qrcode', 'wechat_hk', 'alipay_hk'].includes(paymentMethod) && !document.getElementById('receipt-upload-btn')) {
                            addUploadReceiptButton(payData);
                        }
                    } else if (paymentMethod === 'fps_upload') {
                        // 显示上传区域
                        fpsUploadSection.classList.add('active');
                        
                        // 更新上传区域信息
                        const uploadTitle = fpsUploadSection.querySelector('.upload-title');
                        const uploadDescription = fpsUploadSection.querySelector('.upload-description');
                        
                        if (uploadTitle) {
                            uploadTitle.textContent = 'FPS轉賬收據上傳';
                        } else {
                            console.error('无法找到上传标题元素');
                        }
                        
                        if (uploadDescription) {
                            uploadDescription.textContent = payData.pay_data?.instructions || 
                                '請將您的FPS轉賬收據截圖上傳，我們將在確認後完成訂單處理。';
                        } else {
                            console.error('无法找到上传说明元素');
                        }
                        
                        // 更新上传按钮事件
                        setupUploadButton(fpsUploadSection.querySelector('.upload-button'), payData);
                    }
                    
                } catch (error) {
                    hideLoading();
                    console.error(`${getPaymentName(paymentMethod)}支付处理错误:`, error);
                    alert(`${getPaymentName(paymentMethod)}支付处理错误: ${error.message}`);
                }
            }
            
            // 获取支付方式配置
            function getPaymentConfig(paymentMethod, amount, orderNo) {
                // 基础配置
                const baseConfig = {
                    mch_order_no: orderNo,
                    amount: parseFloat(amount) * 100, // 转换为分
                    currency: 'HKD',
                    subject: '支付接口演示',
                    body: '订单支付',
                    notify_url: window.location.origin + `/api/pay/webhook/${getPaymentWebhookPath(paymentMethod)}`,
                    return_url: window.location.origin + '/payment/result',
                    client_ip: '127.0.0.1'
                };
                
                // 根据支付方式添加特定配置
                switch (paymentMethod) {
                    case 'payme':
                        return {
                            ...baseConfig,
                            if_code: 'payme',
                            way_code: 'payme'
                        };
                    case 'fps_qrcode':
                        return {
                            ...baseConfig,
                            if_code: 'fps',
                            way_code: 'qrcode'
                        };
                    case 'fps_upload':
                        return {
                            ...baseConfig,
                            if_code: 'fps',
                            way_code: 'upload'
                        };
                    case 'wechat_hk':
                        return {
                            ...baseConfig,
                            if_code: 'wechat',
                            way_code: 'qrcode'
                        };
                    case 'alipay_hk':
                        return {
                            ...baseConfig,
                            if_code: 'alipay',
                            way_code: 'qrcode'
                        };
                    default:
                        return baseConfig;
                }
            }
            
            // 获取支付方式名称
            function getPaymentName(paymentMethod) {
                const names = {
                    'payme': 'PayMe',
                    'fps_qrcode': 'FPS',
                    'fps_upload': 'FPS上传',
                    'wechat_hk': '微信',
                    'alipay_hk': '支付宝'
                };
                
                return names[paymentMethod] || paymentMethod;
            }
            
            // 获取webhook路径
            function getPaymentWebhookPath(paymentMethod) {
                const paths = {
                    'payme': 'payme',
                    'fps_qrcode': 'fps',
                    'fps_upload': 'fps',
                    'wechat_hk': 'wechat',
                    'alipay_hk': 'alipay'
                };
                
                return paths[paymentMethod] || paymentMethod;
            }
            
            // 添加上传凭证按钮
            function addUploadReceiptButton(payData) {
                const uploadButton = document.createElement('div');
                uploadButton.id = 'receipt-upload-btn';
                uploadButton.className = 'receipt-upload-btn';
                uploadButton.innerHTML = '<i class="fa fa-upload"></i> 上傳付款憑證';
                
                // 添加点击事件
                uploadButton.addEventListener('click', () => {
                    // 创建文件上传输入框
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'image/*';
                    fileInput.style.display = 'none';
                    
                    // 添加到DOM
                    document.body.appendChild(fileInput);
                    
                    // 点击文件选择
                    fileInput.click();
                    
                    // 文件选择事件
                    fileInput.addEventListener('change', async (e) => {
                        if (e.target.files && e.target.files[0]) {
                            handleFileUpload(e.target.files[0], payData, fileInput);
                        }
                    });
                });
                
                // 添加到二维码区域
                qrcodeSection.appendChild(uploadButton);
            }
            
            // 设置上传按钮事件
            function setupUploadButton(uploadButton, payData) {
                if (!uploadButton) {
                    console.error('无法找到上传按钮元素');
                    return;
                }
                
                // 移除旧的事件监听器
                const newUploadButton = uploadButton.cloneNode(true);
                uploadButton.parentNode.replaceChild(newUploadButton, uploadButton);
                
                // 添加新的事件监听器
                newUploadButton.addEventListener('click', () => {
                    // 创建文件上传输入框
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'image/*';
                    fileInput.style.display = 'none';
                    
                    // 添加到DOM
                    document.body.appendChild(fileInput);
                    
                    // 点击文件选择
                    fileInput.click();
                    
                    // 文件选择事件
                    fileInput.addEventListener('change', async (e) => {
                        if (e.target.files && e.target.files[0]) {
                            handleFileUpload(e.target.files[0], payData, fileInput);
                        }
                    });
                });
            }
            
            // 处理文件上传
            async function handleFileUpload(file, payData, fileInput) {
                // 显示加载状态
                showLoading('正在上传付款凭证...');
                
                try {
                    // 准备FormData对象
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('pay_order_id', payData.pay_order_id);
                    formData.append('mch_order_no', payData.mch_order_no);
                    
                    // 发送上传请求
                    const uploadResponse = await fetch('/api/pay/v2/receipt/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (!uploadResponse.ok) {
                        throw new Error(`上传请求失败: ${uploadResponse.status}`);
                    }
                    
                    const uploadResult = await uploadResponse.json();
                    
                    if (uploadResult.code !== 200) {
                        throw new Error(uploadResult.message || '上传凭证失败');
                    }
                    
                    // 上传成功
                    hideLoading();
                    alert('支付凭证上传成功！系统将在审核后完成支付。');
                    
                    // 刷新页面
                    window.location.reload();
                    
                } catch (error) {
                    hideLoading();
                    console.error('上传凭证错误:', error);
                    alert(`上传凭证失败: ${error.message}`);
                } finally {
                    // 移除文件输入框
                    if (fileInput && fileInput.parentNode) {
                        document.body.removeChild(fileInput);
                    }
                }
            }
            
            // 立即支付按钮
            document.querySelector('.payment-button').addEventListener('click', function() {
                // 获取选中的支付方式
                const selectedMethod = document.querySelector('.payment-method.active');
                if (!selectedMethod) {
                    alert('請選擇支付方式');
                    return;
                }
                
                const methodType = selectedMethod.getAttribute('data-method');
                const amount = document.querySelector('.payment-price').textContent.replace('HK$ ', '');
                const orderNo = document.getElementById('order-no').textContent;
                
                // 根据不同支付方式采取不同行动
                switch (methodType) {
                    case 'fps_qrcode':
                    case 'fps_upload':
                    case 'payme':
                    case 'wechat_hk':
                    case 'alipay_hk':
                        // 使用统一的支付处理函数
                        handlePayment(amount, orderNo, methodType);
                        break;
                    case 'paypal':
                        // 使用PayPal处理函数
                        handlePaypalPayment(amount, orderNo);
                        break;
                    case 'stripe':
                        // 使用Stripe嵌入式结账处理
                        handleStripePayment(amount, orderNo);
                        break;
                    default:
                        alert(`模擬支付請求:\n支付方式: ${methodType}\n支付金額: HK$${amount}\n訂單編號: ${orderNo}`);
                }
            });

            // 检查PayPal返回
            checkPayPalReturn();

            // 检查URL中的Stripe session_id并处理支付状态
            checkStripeSessionId();
        });
    </script>
@endsection
