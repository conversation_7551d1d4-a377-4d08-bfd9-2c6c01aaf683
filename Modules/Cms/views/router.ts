import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
    {
        path: '/cms',
        component: () => import('/admin/layout/index.vue'),
        meta: { title: 'CMS 管理', icon: 'Tickets' },
        children: [
            {
                path: 'cmsDetail',
                name: 'cmsDetail',
                meta: { title: 'CMS 管理', icon: 'Tickets' },
                component: () => import('./ui/conetnt/detail.vue'),
            },
            {
                path: 'cmsCreate',
                name: 'cmsCreate',
                meta: { title: '新增', icon: 'Tickets' },
                component: () => import('./ui/conetnt/detail.vue'),
            },
            {
                path: 'cmsList',
                name: 'cmsList',
                meta: { title: 'CMS 列表', icon: 'Tickets' },
                component: () => import('./ui/conetnt/list.vue'),
            },
            {
                path: 'sitemap',
                name: 'sitemap',
                meta: { title: 'Cms.Router.map_management', icon: 'Tickets', i18n: true },
                component: () => import('./ui/conetnt/sitemap.vue'),
                 
            },
            {
                path:"categories",
                name:"categories",
                meta:{title:'CMS分类',icon:"Tickets"},
                component:()=>import ('./ui/conetnt/categories.vue')
            },
            {
                path:"contact/",
                name:"contact/",
                meta:{title:'CMS联系我们',icon:"Tickets"},
                component:()=>import ('./ui/conetnt/contact.vue')
            }
        ],
    },
]

export default router
