<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <FilterPopover 
        v-model="filterDialog"
      >
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = !filterDialog">
            <el-icon size="16">
              <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
            </el-icon>
            <span>{{ $t('Cms.contact.filter') }}</span>
          </el-button>
        </template>
        <el-form :model="search" label-position="top">
          <el-form-item label="ID">
            <el-input v-model="search.id" :placeholder="t('Cms.categories.input_placeholder')" size="large" />
          </el-form-item>
        
          <el-form-item :label="$t('Cms.contact.phone')">
            <el-input v-model="search.phone" :placeholder="t('Cms.categories.input_placeholder')+t('Cms.contact.phone')" size="large" />
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="refreshContact">
              <el-icon size="16">
                <Refresh />
              </el-icon>
              <span>{{ $t('Cms.contact.refresh') }}</span>
            </el-button>
            <el-button class="button-no-border" @click="searchContact" type="primary">
              <el-icon size="16">
                <Filter />
              </el-icon>
              <span>{{ $t('Cms.contact.filter') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button @click="addContact" type="primary">
        <el-icon>
          <Plus />
        </el-icon>
        <span>{{ $t('Cms.contact.add') }}</span>
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <el-table ref="tableRefs" :data="contactList" style="width: 100%; height: 100%" @selection-change="checkedContactHandle" v-loading="loading">
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="50" />
          <el-table-column prop="name" :label="$t('Cms.contact.name')" />
          <el-table-column prop="phone" :label="$t('Cms.contact.phone')" width="150" />
          <el-table-column prop="category_name" :label="$t('Cms.contact.category')" width="180" />
          <el-table-column prop="created_at" :label="$t('Cms.contact.created_time')" width="180" />
          <el-table-column fixed="right" :label="$t('Cms.contact.operate')" width="150">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" @click="upContactHandle(scope.row)">
                  <el-icon>
                    <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                  </el-icon>
                </el-button>
                <!-- <el-button class="bwms-btn del-btn" circle icon="Delete" @click="delContactHandle(scope.row)"></el-button> -->
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="changePage"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  {{ t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="changePage"
            />
          </div>
        </div>
      </div>
    </div>

    <el-dialog v-model="dialogVisible" :title="$t('Cms.contact.dialog_tit1')" width="500" @close="dialogCancle">
      <div>{{ $t('Cms.contact.dialog_con1') }}</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button plain size="large" class="bwms-btn" @click="dialogCancle">{{ $t('Cms.contact.cancel') }}</el-button>
          <el-button plain size="large" class="bwms-btn del-btn" @click="dialogConfirm">{{ $t('Cms.contact.confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog class="el-dialog-common-cls" v-model="addDialog" :title="$t('Cms.contact.add')" width="700">
      <el-form :model="form" label-position="top">
        <div class="form-row">
          <el-form-item :label="$t('Cms.contact.name')" class="form-item">
            <el-input v-model="form.name" :placeholder="t('Cms.categories.input_placeholder')+t('Cms.contact.name')" size="large" />
          </el-form-item>
          <el-form-item :label="$t('Cms.contact.phone')" class="form-item">
            <el-input v-model="form.phone" :placeholder="t('Cms.categories.input_placeholder')+t('Cms.contact.phone')" size="large" />
          </el-form-item>
        </div>
        
        <div class="form-row">
          <el-form-item :label="t('Cms.contact.category')" class="form-item">
            <div class="relative w-full">
              <el-cascader 
                size="large" 
                class="w-full" 
                v-model="form.category" 
                :options="categoryList" 
                :props="defaultProps" 
                :clearable="true" 
                :filterable="true"
                :loading="categoryLoading"
                :disabled="categoryLoading"
                :placeholder="t('Cms.categories.select_placeholder')+t('Cms.contact.category')"
              >
                <template #empty>
                  <div style="text-align: center; padding: 8px 0; ">
                    {{ t('Cms.list.no_data') }}
                  </div>
                </template>
              </el-cascader>
              <div v-if="categoryLoading" class="category-loading-overlay">
                <el-icon class="btn-loading"><Loading /></el-icon>
              </div>
            </div>
          </el-form-item>
          <el-form-item :label="t('Cms.contact.lang')" class="form-item">
            <el-select v-model="form.data_lang">
              <el-option :label="langItem.label" :value="langItem.value" v-for="langItem in langList" :key="langItem.value" />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="el-button-default" @click="addDialog = false" :disabled="submitLoading">{{ $t('Cms.contact.cancel') }}</el-button>
          <el-button 
            class="button-no-border" 
            type="primary" 
            :loading="submitLoading"
            @click="submitForm"
          >
            {{ $t('Cms.contact.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import http from '/admin/support/http'
import { Filter, Plus, Loading, Refresh } from '@element-plus/icons-vue'
import FilterPopover from '/resources/admin/components/popover/index.vue'

const { t } = useI18n()

// 添加接口定义
interface Category {
  id: number
  title: string
  children?: Category[]
}

interface Contact {
  id: number
  name: string
  phone: string
  category_name: string
  created_at: string
  data_lang: string
}

// 添加接口定义修复类型错误
interface ContactFormData {
  name: string
  phone: string
  category_id?: number
  lang: string
  model_id: number
  id?: number  // 添加可选的id字段
}

const api = 'cms'
const router = useRouter()
const route = useRoute()
let lang = localStorage.getItem('bwms_language') || 'zh_HK'
const langList = ref([
  {
    label: t('Cms.contact.select_opt1'),
    value: 'zh_CN',
  },
  {
    label: t('Cms.contact.select_opt2'),
    value: 'en',
  },
  {
    label: t('Cms.contact.select_opt3'),
    value: 'zh_HK',
  },
])
let model_id = Number(route.query.model_id) || 0

// 分类列表
const categoryList = ref<Category[]>([])
const defaultProps = reactive({
  expandTrigger: 'hover' as const,
  emitPath: false,
  value: 'title',
  label: 'title',
  children: 'children',
})

// 添加分类加载状态变量
const categoryLoading = ref(false)

// 更新获取分类列表的方法，添加 loading 状态
async function getCategoryList(): Promise<void> {
  categoryLoading.value = true // 设置加载状态为 true
  
  try {
    // 构建请求参数，使用类型断言解决类型错误
    const params: any = {
      page: 1,
      model_id: model_id,
      lang: form.data_lang || lang,
      perPage: 999 // 获取所有分类
    }
    
    // 调用获取分类列表接口
    const response = await http.get('/cms/v3/category/list', params)
    
    if (response.data.code === 200) {
      categoryList.value = response.data.data || []
    } else {
      console.error('获取分类列表失败:', response.data.message)
      ElMessage.warning(t('Cms.contact.category_load_failed'))
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.warning(t('Cms.contact.category_load_failed'))
  } finally {
    categoryLoading.value = false // 设置加载状态为 false
  }
}

// 联系人数据
const tableRefs = ref<any>(null)
const contactList = ref<Contact[]>([])
const loading = ref(false)
const checkedContacts = ref<Contact[]>([])

// 修改获取联系人列表的方法，使用正确的类型
async function getContactList() {
  loading.value = true
  
  try {
    // 构建请求参数，使用类型断言解决类型错误
    const params: any = {
      page: page.value,
      limit: limit.value,
      model_id: model_id
    }
    
    // 添加搜索条件
    if (search.id) {
      params.id = search.id
    }
    
    if (search.phone) {
      params.phone = search.phone
    }
    
    if (search.data_lang) {
      params.lang = search.data_lang
    }
    
    // 调用获取联系人列表接口
    const response = await http.get('/cms/contact/list', params)
   
    if (response.data && response.data.code === 200) {
      contactList.value = response.data.data
      total.value = response.data.data.length || 0
    } else {
      // 错误处理
      ElMessage.error(response.data?.message || t('Cms.pagination.get_data_failed'))
      // 如果请求失败但状态码不是200，使用空数据
      contactList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取联系人列表失败:', error)
    ElMessage.error(t('Cms.pagination.get_data_failed'))
    contactList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 分页器
let page = ref(1)
let limit = ref(20)
let total = ref(0)
const changePage = () => {
  getContactList()
}

// 对话框
const dialogVisible = ref(false)
const dialogCancle = () => {
  dialogVisible.value = false
  loading.value = false
}
const dialogConfirm = async () => {
  if (checkedContacts.value.length === 0) {
    ElMessage.warning(t('Cms.pagination.select_items'))
    dialogCancle()
    return
  }
  
  try {
    const ids = checkedContacts.value.map(item => item.id)
    
    // 调用删除接口
    const response = await http.post('/cms/v3/contact/delete', { ids })
    
    if (response.data && response.data.code === 200) {
      ElMessage.success(t('Cms.pagination.delete_success'))
      checkedContacts.value = []
      
      // 刷新列表
      getContactList()
    } else {
      ElMessage.error(response.data?.message || t('Cms.pagination.delete_failed'))
    }
  } catch (error) {
    console.error('删除联系人失败:', error)
    ElMessage.error(t('Cms.pagination.delete_failed'))
  } finally {
    dialogCancle()
  }
}

// 搜索
const filterDialog = ref(false)
const search = reactive({
  id: '',
  phone: '',
  data_lang: lang,
})
const searchContact = async () => {
  page.value = 1 // 搜索时重置到第一页
  await getContactList()
  filterDialog.value = false
}
const refreshContact = () => {
  // 重置搜索条件
  search.id = ''
  search.phone = ''
  search.data_lang = lang
  
  // 重置分页
  page.value = 1
  limit.value = 20
  
  // 重新获取数据
  getContactList()
  filterDialog.value = false
}

// 语言切换
const changeLang = (newLang: string) => {
  if (lang == newLang) return
  lang = newLang
  localStorage.setItem('data_language', lang)
}

// 页面初始化
const pageInit = async () => {
  
  await getCategoryList()
  await getContactList()
}

// 新增表单相关
const addDialog = ref(false)
const form = reactive({
  name: '',
  phone: '',
  category: undefined as string | undefined,
  data_lang: lang
})

// 编辑状态标记
const currentEditId = ref<number | null>(null)

// 提交表单方法
const submitLoading = ref(false)
const submitForm = async () => {
  if (!form.name || !form.phone) {
    ElMessage.warning(t('Cms.pagination.form_validation'))
    return
  }
  
  submitLoading.value = true
  
  try {
    // 准备提交的数据
    const data: any = {
      name: form.name,
      phone: form.phone,
      category: form.category,
      category_id: categoryList.value.find(item => item.title === form.category)?.id,
      lang: form.data_lang,
      model_id: model_id
    }
    
    // 如果是编辑模式，添加id字段
    if (currentEditId.value) {
      data.id = currentEditId.value
    }
    
    // 调用保存接口
    const response = await http.post('/cms/v3/contact/save', data)
    
    if (response.data && response.data.code === 200) {
      // 成功处理
      ElMessage.success(currentEditId.value ? t('Cms.pagination.edit_success') : t('Cms.pagination.add_success'))
      addDialog.value = false
      // 重置状态
      currentEditId.value = null
      // 刷新列表
      getContactList()
    } else {
      // 错误处理
      const errorMsg = response.data && response.data.message ? response.data.message : t('Cms.pagination.operation_failed')
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('保存联系人失败:', error)
    ElMessage.error(t('Cms.pagination.operation_failed'))
  } finally {
    submitLoading.value = false
  }
}

const checkedContactHandle = (val: Contact[]) => {
  checkedContacts.value = val
}

const upContactHandle = async (contact: Contact) => {
  // 如果联系人的语言与当前语言不同，先加载对应语言的分类
  if (contact.data_lang && contact.data_lang !== form.data_lang) {
    form.data_lang = contact.data_lang
    
    // 先更新语言，等待分类数据加载完成
    await getCategoryList()
  }
  
  // 回填表单数据
  form.name = contact.name
  form.phone = contact.phone
  form.category = contact.category_name
  
  // 标记当前是编辑模式
  currentEditId.value = contact.id
  addDialog.value = true
}

const delContactHandle = (contact?: Contact) => {
  dialogVisible.value = true
  loading.value = true
  if (contact) checkedContacts.value = [{ ...contact }]
}

const addContact = () => {
  form.name = ''
  form.phone = ''
  form.category = undefined
  form.data_lang = lang // 使用当前语言作为默认值
  currentEditId.value = null
  
  // 确保获取当前语言的分类数据
  getCategoryList()
  
  addDialog.value = true
}

// 监听弹窗中的语言变化，添加 loading 状态
watch(() => form.data_lang, async (newLang) => {
  if (newLang) {
    categoryLoading.value = true // 设置加载状态为 true
    
    try {
      // 构建请求参数
      const params: any = {
        page: 1,
        model_id: model_id,
        lang: newLang,
        perPage: 999 // 获取所有分类
      }
      
      // 调用获取分类列表接口
      const response = await http.get('/cms/v3/category/list', params)
      
      if (response.data && response.data.code === 200) {
        // 更新分类列表
        categoryList.value = response.data.data || []
        
        // 如果当前选择的分类在新语言下不存在，可能需要清空选择
        if (form.category) {
          const categoryExists = checkCategoryExists(form.category, categoryList.value)
          if (!categoryExists) {
            form.category = undefined
          }
        }
      } else {
        console.error('获取分类列表失败:', response.data.message)
        ElMessage.warning(t('Cms.contact.category_load_failed'))
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      ElMessage.warning(t('Cms.contact.category_load_failed'))
    } finally {
      categoryLoading.value = false // 设置加载状态为 false
    }
  }
}, { immediate: false })

// 检查分类是否存在于分类列表中的辅助函数
const checkCategoryExists = (categoryTitle: string, categories: any[]): boolean => {
  for (const category of categories) {
    if (category.title === categoryTitle) {
      return true
    }
    
    if (category.children && category.children.length > 0) {
      const existsInChildren = checkCategoryExists(categoryTitle, category.children)
      if (existsInChildren) return true
    }
  }
  
  return false
}

// 添加路由监听，当model_id变化时重新加载数据
watch(
  () => route.query.model_id,
  (newModelId) => {
    if (newModelId) {
      model_id = Number(newModelId) || 0
      pageInit()
    }
  },
  { immediate: true }
)

onMounted(() => {
  pageInit()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}

.flex {
  display: flex;
}

.w-1\/2 {
  width: 50%;
}

.w-full {
  width: 100%;
}

.gap-4 {
  gap: 1rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.category-select-container {
  position: relative;
}

.category-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  z-index: 10;
}

.btn-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 新增表单布局样式 */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 4px;
}

.form-item {
  flex: 1;
  min-width: 0;
}
</style>
