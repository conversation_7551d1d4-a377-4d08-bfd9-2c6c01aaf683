<template>
  <div class="bwms-module table-page">
    <div class="module-header">
      <el-button class="button-no-border back-btn" @click="router.go(-1)">
        <el-icon><ArrowLeft /></el-icon>
        <span>{{ t('Cms.detail.back') }}</span>
      </el-button>
    </div>
    <div class="module-con-flat">
      <div class="sitemap-row">
        <div class="sitemap-icon">
          <el-icon style="color: #418ae2" :size="32">
            <component :is="'StarFilled'" />
          </el-icon>
        </div>
        <div class="sitemap-info">
          <div class="sitemap-title">{{ t('Cms.detail.sitemap_title') }}</div>
          <div class="sitemap-desc">{{ t('Cms.detail.sitemap_desc') }}</div>
        </div>
        <div class="sitemap-action" @click="generateSitemapHandle">
          <el-icon size="16"><Check /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'
import { ElMessage } from 'element-plus'

const router = useRouter()
const { t } = useI18n()

const generateSitemapHandle = () => {
  http.post(`/cms/sitemap/generate`).then(res => {
    const { code, message } = res.data
    if (code === 200) {
      ElMessage.success(message)
    } else {
      ElMessage.error(message)
    }
  })
}
</script>

<style lang="scss" scoped>

.module-con-flat {
  background: #fff;
  padding: 30px 20px;
  border-radius: 0;
  box-shadow: 0px 1px 1px #00000029;
  border-radius: 10px;
}
.sitemap-row {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}
.sitemap-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #eaf4ff;
  border-radius: 12px;
  .el-icon {
    font-size: 32px;
  }
}
.sitemap-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.sitemap-title {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-bottom: 6px;
}
.sitemap-desc {
  font-size: 14px;
  color: #888;
  line-height: 1.6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 320px;
}
.sitemap-action {
  position: absolute;
  top: 8px;
  right: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f2f3f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 1px 4px #0001;

  &:hover {
    background: #e6e8eb;
  }

  .el-icon {
    color: #007ee5;
  }
}
</style>