<template>
  <div class="config-bg">
    <div class="config-module" v-for="(item, index) in list" :key="item.name">
      <div class="module-tit">
        <h2>{{ item.label }}</h2>
        <div class="btn-list">
          <div class="btn-box" @click="foldHandle(item, index)">
            <el-icon size="16" color="#333"><ArrowDown /></el-icon>
          </div>
        </div>
      </div>
      <div class="module-con" :class="{ open: item.isFold }" ref="contentRefs">
        <div class="hei-box">
          <div class="row" v-if="loading">
            <!-- 骨架屏 -->
            <div class="item skeleton" v-for="box in item.list" :key="box.url">
              <div class="box">
                <div class="box-con">
                  <div class="icons skeleton-icon"></div>
                  <div class="box-tit skeleton-text"></div>
                  <div class="info-dom">
                    <span class="num skeleton-text"></span>
                    <span class="label skeleton-text"></span>
                  </div>
                </div>
                <div class="box-footer skeleton-text"></div>
                <div class="arrow-icons skeleton-arrow"></div>
              </div>
            </div>
          </div>
          <div class="row" v-else>
            <div class="item" v-for="box in item.list" :key="box.url">
              <div class="box">
                <div class="box-con">
                  <div class="icons">
                    <el-icon style="color: rgb(0, 126, 229)" :size="22">
                      <component :is="box.icon" />
                    </el-icon>
                  </div>
                  <div class="box-tit">{{ box.label }}</div>
                </div>
                <div class="box-footer">{{ box.description }}</div>
                <div class="arrow-icons" @click="goPage(box.url, item.name)">
                  <el-icon size="20" color="#747474"><Right /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import http from '/admin/support/http'

const api = 'config'
const router = useRouter()

interface Config {
  name: string
  label: string
  list: ConfigItem[]
  isFold: boolean
}
interface ConfigItem {
  label: string
  description: string
  icon: string
  url: string
  permissions: string[]
  order: number
}

const loading = ref(true)
const list = ref([] as Config[])
const contentRefs = ref()

const goPage = (url: string, name: string) => {
  router.push({
    path: url,
    query: {
      name,
    },
  })
}
const foldHandle = (item: Config, index: number) => {
  if(!contentRefs.value) return
  item.isFold = !item.isFold

  const contentDom = contentRefs.value[index]
  if(item.isFold) {
    const height = contentDom.children[0].getBoundingClientRect().height
    contentDom.style.height = 'auto'
  }else {
    contentDom.style.height = 0
  }
}

onMounted(() => {
  http
    .get(`/${api}/list`)
    .then((res: any) => {
      const data = res.data.data
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        item.isFold = true
      }
      list.value = data
    })
    .finally(() => {
      loading.value = false
    })
})
</script>

<style lang="scss" scoped>
.config-bg {
  background: #f5f6fa;
  padding: 12px 34px 20px 34px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.config-module {
  width: 100%;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 1px 1px #00000029;
  margin-bottom: 20px;
  transition: box-shadow 0.2s;
  position: relative;
  padding: 20px;
  &:last-child {
    margin-bottom: 0;
  }

  .module-tit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    h2 {
      color: #000000;
      font-size: 16px;
      font-weight: 500;
      line-height: 1.4;
      margin: 0;
    }
    .btn-list {
      display: flex;
      align-items: center;
      .btn-box {
        display: flex;
        align-items: center;
        height: 30px;
        width: 30px;
        justify-content: center;
        border-radius: 50%;
        background: #f2f3f5;
        cursor: pointer;
        transition: background 0.2s;
        &:hover {
          background: #e6e8eb;
        }
        .el-icon {
          font-size: 18px;
        }
      }
    }
  }
  
  .module-con {
    overflow: hidden;

    &.open {
      // 保持原有折叠逻辑
    }
    .hei-box {
      margin-top: 12px;
      transition: height .35s ease-in-out;
    }
  }
}

.row {
  margin: 0 -6px;
  display: flex;
  flex-wrap: wrap;
  .item {
    padding: 0 10px 16px;
    box-sizing: border-box;
    width: 16.66%;
    @media screen and (max-width: 1680px) {
      width: 20%;
    }
    @media screen and (max-width: 1420px) {
      width: 25%;
    }
    @media screen and (max-width: 1100px) {
      width: 33.33%;
    }
    @media screen and (max-width: 800px) {
      width: 50%;
    }
    .box {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
      border: 1px solid #f0f0f0;
      padding: 12px 10px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      transition: box-shadow 0.2s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }
      .box-con {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .icons {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #eaf4ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;

        .el-icon {
          font-size: 18px;
          color: #418ae2;
        }
      }

      .box-tit {
        font-size: 14px;
        color: #000;
        font-weight: 400;
      }

      .box-footer {
        width: 100%;
        font-size: 12px;
        color: #666666;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .arrow-icons {
        position: absolute;
        top: 12px;
        right: 10px;
        width: 26px;
        height: 26px;
        border-radius: 50%;
        background: #f2f3f5;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        cursor: pointer;
        transition: background 0.2s;
        box-shadow: 0 1px 4px #0001;

        &:hover {
          background: #e6e8eb;
        }

        .el-icon {
          font-size: 14px;
          color: #418ae2;
        }
      }
    }
    &.arrow-bottom {
      .box {
        .box-footer {
          width: calc(100% - 78px);
        }
        .arrow-icons {
          top: auto;
          bottom: 10px;
        }
      }
    }
  }
  &.skeleton {
    .skeleton-icon {
      background-color: #e0e0e0;
      width: 42px;
      height: 42px;
      border-radius: 50%;
    }
    .skeleton-text {
      background-color: #e0e0e0;
      height: 20px;
      border-radius: 4px;
      margin-bottom: 10px;
      width: 70%;
    }
    .skeleton-arrow {
      background-color: #e0e0e0;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-top: 20px;
    }
  }
}
</style>
