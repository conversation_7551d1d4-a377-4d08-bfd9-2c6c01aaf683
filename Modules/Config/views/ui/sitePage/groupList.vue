<template>
  <div class="bwms-module table-page">
    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 搜索区域 -->
        <div class="module-header flex items-center justify-between">
          <FilterPopover v-model="popoverVisible">
            <template #reference>
              <el-button class="filter-trigger" @click="popoverVisible = !popoverVisible">
                <el-icon><Filter /></el-icon>
                <span>{{ t('Config.Sites.filter') }}</span>
              </el-button>
            </template>
            <el-form :model="searchForm" label-position="top" @submit.prevent.stop>
              <el-form-item :label="t('Config.SiteGroup.name')">
                <el-input v-model="searchForm.keyword" :placeholder="t('Config.SiteGroup.keyword')+t('Config.SiteGroup.name')" size="large" clearable />
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="flex justify-center">
                <el-button class="el-button-default" @click="handleReset">
                  <el-icon size="16"><Refresh /></el-icon>
                  <span>{{ t('Config.SiteGroup.reset') }}</span>
                </el-button>
                <el-button class="button-no-border" type="primary" @click="handleSearch">
                  <el-icon size="16"><Filter /></el-icon>
                  <span>{{ t('Config.Sites.filter') }}</span>
                </el-button>
              </div>
            </template>
          </FilterPopover>
          <el-button type="primary" @click="handleCreateGroup">
            <el-icon><Plus /></el-icon>
            <span>{{ t('Config.SiteGroup.create') }}</span>
          </el-button>
        </div>
        
        <!-- 表格数据 -->
        <el-table 
          :data="groupList" 
          style="width: 100%" 
          v-loading="loading">
          <el-table-column prop="name" :label="$t('Config.SiteGroup.name')" min-width="120" />
          <el-table-column prop="code" :label="$t('Config.SiteGroup.code')" min-width="120" />
          <el-table-column prop="created_at" :label="$t('Config.SiteGroup.createdAt')" width="300" />
          <el-table-column :label="$t('Config.SiteGroup.operations')" width="150" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope.row)">
                <el-icon>
                  <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                </el-icon>
              </el-button>
              <el-button link type="danger" @click="handleDelete(scope.row)">
                <el-icon>
                  <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                </el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">每页显示</span>
            <el-select
              v-model="pagination.limit"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 15, 20, 50]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">共 {{pagination.total}} 条</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              background
              layout="prev, pager, next"
              :page-size="pagination.limit"
              :total="pagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑站点组对话框 -->
    <el-dialog
      class="el-dialog-common-cls"
      v-model="dialogVisible"
      :title="isEdit ? $t('Config.SiteGroup.editGroup') : $t('Config.SiteGroup.createGroup')"
      width="500px"
    >
      <el-form
        ref="groupFormRef"
        :model="groupForm"
        :rules="rules"
        label-position="top"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="groupForm.name" :placeholder="$t('Config.SiteGroup.nameRequired')" />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="groupForm.code" :placeholder="$t('Config.SiteGroup.codeRequired')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="flex justify-center">
          <el-button @click="dialogVisible = false">{{ $t('Config.SiteGroup.cancel') }}</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">{{ $t('Config.SiteGroup.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Filter, Refresh } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import http from '/resources/admin/support/http'
import { useI18n } from 'vue-i18n'
import FilterPopover from '/resources/admin/components/popover/index.vue'

// 定义接口
interface SiteGroup {
  id: number
  name: string
  code: string
  description: string | null
  status: number
  created_at: string
  updated_at: string
  deleted_at: string | null
  created_by: number
  updated_by: number
}

interface ApiResponse {
  code: number
  message: string
  data: SiteGroup[]
  total: number
  limit: number
  page: number
}

// 页面状态
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const groupFormRef = ref<FormInstance>()
const { t } = useI18n() // 使用多语言

const popoverVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
})

// 站点组列表数据
const groupList = ref<SiteGroup[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  limit: 15,
  total: 0
})

// 表单数据
const groupForm = reactive({
  id: 0,
  name: '',
  code: '',
})

// 表单验证规则
const rules = computed<FormRules>(() => ({
  name: [
    { required: true, message: t('Config.SiteGroup.nameRequired'), trigger: 'blur' },
    { max: 50, message: t('Config.SiteGroup.nameRequired'), trigger: 'blur' }
  ],
  code: [
    { required: true, message: t('Config.SiteGroup.codeRequired'), trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: t('Config.SiteGroup.codeRequired'), trigger: 'blur' },
    { max: 50, message: t('Config.SiteGroup.codeRequired'), trigger: 'blur' }
  ]
}))

// 获取站点组列表
const fetchGroups = async () => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.page,
      limit: pagination.limit
    }
    
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword.trim()
    }
    
    const response = await http.get('/config/site-groups', params )
    
    if (response.data && response.data.data) {
      groupList.value = response.data.data
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    ElMessage.error(t('Config.SiteGroup.getListFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  popoverVisible.value = false
  fetchGroups()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  pagination.page = 1
  popoverVisible.value = false
  fetchGroups()
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pagination.limit = size
  fetchGroups()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchGroups()
}

// 打开创建站点组对话框
const handleCreateGroup = () => {
  resetForm()
  isEdit.value = false
  dialogVisible.value = true
}

// 打开编辑站点组对话框
const handleEdit = (row: SiteGroup) => {
  resetForm()
  isEdit.value = true
  groupForm.id = row.id
  groupForm.name = row.name
  groupForm.code = row.code
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  groupForm.id = 0
  groupForm.name = ''
  groupForm.code = ''
  
  // 清除表单验证
  if (groupFormRef.value) {
    groupFormRef.value.resetFields()
  }
}

// 删除站点组
const handleDelete = (row: SiteGroup) => {
  ElMessageBox.confirm(
    t('Config.SiteGroup.confirmDelete', { name: row.name }),
    t('Config.SiteGroup.tip'),
    {
      confirmButtonText: t('Config.SiteGroup.confirm'),
      cancelButtonText: t('Config.SiteGroup.cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const response = await http.delete(`/config/site-groups/${row.id}`)
        if (response.data && response.data.code === 200) {
          ElMessage.success(t('Config.SiteGroup.deleteSuccess'))
          fetchGroups() // 重新获取列表
        } else {
          ElMessage.error(response.data?.message || t('Config.SiteGroup.deleteFailed'))
        }
      } catch (error) {
        ElMessage.error(t('Config.SiteGroup.deleteFailed'))
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 提交表单
const submitForm = async () => {
  if (!groupFormRef.value) return
  
  await groupFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        const formData = {
          name: groupForm.name,
          code: groupForm.code
        }
        
        let response
        if (isEdit.value) {
          // 编辑模式：更新站点组
          response = await http.put(`/config/site-groups/${groupForm.id}`, formData)
        } else {
          // 创建模式：新增站点组
          response = await http.post('/config/site-groups', formData)
        }
        
        if (response.data && response.data.code === 200) {
          ElMessage.success(isEdit.value ? t('Config.SiteGroup.updateSuccess') : t('Config.SiteGroup.createSuccess'))
          dialogVisible.value = false
          fetchGroups() // 重新获取列表
        } else {
          ElMessage.error(response.data?.message || (isEdit.value ? t('Config.SiteGroup.updateFailed') : t('Config.SiteGroup.createFailed')))
        }
      } catch (error) {
        ElMessage.error(isEdit.value ? t('Config.SiteGroup.updateFailed') : t('Config.SiteGroup.createFailed'))
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 页面加载时获取站点组列表
onMounted(() => {
  fetchGroups()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 0;
  padding: 0;
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    
    .box {
      padding-top: 12px;
      padding-bottom: 0;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;

      .module-header {
        margin-bottom: 20px;
        flex-shrink: 0;
      }
    }

    .box-footer {
      background: #fff;
      border-radius: 0 0 4px 4px;
      padding: 10px 20px;
      margin-top: 1px;
    }
  }

  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .pagination-left {
      display: flex;
      align-items: center;
      
      .page-size-text {
        margin-right: 8px;
        color: #606266;
      }
      
      .page-size-select {
        width: 80px;
        margin-right: 8px;
      }
      
      .total-text {
        color: #606266;
      }
    }
  }

}
</style>
