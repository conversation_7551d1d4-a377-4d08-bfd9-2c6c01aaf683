<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
     <el-button class="button-no-border back-btn" @click="router.push(`/config/settings`)">
      <el-icon><ArrowLeft /></el-icon>
      <span>{{ t('Cms.detail.back') }}</span>
     </el-button>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <ConfigLayout 
          :menuGroups="filteredGroups"
          :activeMenuName="activeMenuName"
          @onMenuItemClick="onMenuItem"
        >
          
        </ConfigLayout>
      </div>
      
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { 
  Setting, ArrowRight
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import http from '/resources/admin/support/http'
// 通过相对路径导入ConfigLayout组件
import ConfigLayout from '/admin/components/configLayout/index.vue'

const router = useRouter()
const route = useRoute()
const { t, locale } = useI18n()

// 加载状态
const loading = ref(false)

// 搜索关键词
const searchKeyword = ref('')

// 当前活动的菜单名称
const activeMenuName = ref('')

// 菜单数据
interface MenuItem {
  name: string
  label: string
  description: string
  icon: string
  url: string
}

interface MenuGroup {
  name: string
  label: string
  list: MenuItem[]
}

const menuGroups = ref<MenuGroup[]>([])

// 获取配置菜单数据（只保留站点、站点组）
const fetchConfigMenu = async () => {
  try {
    loading.value = true
    const res = await http.get('/config/list')
    if (res.data && Array.isArray(res.data.data)) {
      // 只保留站点、站点组相关数据
      const configGroup = res.data.data.find((g: any) => g.name === 'Config')
      if (configGroup) {
        // 只保留name为site和site_group的菜单项
        const filteredList = (configGroup.list || []).filter((item: any) =>
          item.name === 'site' || item.name === 'site_group'
        )
        menuGroups.value = [{
          name: configGroup.name,
          label: configGroup.label,
          list: filteredList
        }]
      } else {
        menuGroups.value = []
      }
    }
  } catch (error) {
    ElMessage.error(t('Config.SiteLayout.getMenuFailed'))
  } finally {
    loading.value = false
  }
}

// 根据搜索关键词过滤菜单
const filteredGroups = computed(() => {
  if (!searchKeyword.value) {
    return menuGroups.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  
  return menuGroups.value.map(group => {
    // 过滤每个组中的列表项
    const filteredList = group.list.filter(item => {
      return item.label.toLowerCase().includes(keyword) || 
             item.description.toLowerCase().includes(keyword)
    })
    
    // 返回新的组对象，带有过滤后的列表
    return {
      ...group,
      list: filteredList
    }
  }).filter(group => group.list.length > 0) // 只保留有列表项的组
})

// 根据当前路由设置活动菜单
const setActiveMenuByRoute = () => {
  const currentPath = route.path
  
  // 遍历菜单项找到匹配的路径
  for (const group of menuGroups.value) {
    for (const item of group.list) {
      if (currentPath === item.url) {
        activeMenuName.value = item.name
        return
      }
    }
  }
  
  // 如果没有匹配的路径，默认使用第一个菜单项
  if (menuGroups.value.length > 0 && menuGroups.value[0].list.length > 0) {
    activeMenuName.value = menuGroups.value[0].list[0].name
  }
}

// 导航到指定URL - 响应ConfigLayout组件的导航事件
const onMenuItem = (item: MenuItem) => {
  if (item.url) {
    router.push(item.url)
  }
  activeMenuName.value = item.name
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    setActiveMenuByRoute()
  }
)

// 监听语言变化
watch(
  () => locale.value,
  () => {
    fetchConfigMenu()
  }
)

onMounted(() => {
  fetchConfigMenu()
  setActiveMenuByRoute()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  .module-header {
    flex-shrink: 0; // 防止头部被压缩
  }
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      background: #fff;
      flex: 1; // 让盒子占据剩余空间
      display: flex;
      flex-direction: column;
      overflow: hidden; // 防止溢出
      padding: 0; // 移除内边距，让ConfigLayout组件占据全部空间
    }

    .loading-container {
      padding: 20px;
    }
  }

  .el-dropdown {
    margin-left: 8px;
  }

  .el-button [class*=el-icon] + span {
    margin-left: 0;
  }
}

/* 过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style> 