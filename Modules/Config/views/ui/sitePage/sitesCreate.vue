<template>
  <div class="table-page site-page">
    <div class="site-content">
      <div class="site-form">
        <el-form 
          ref="siteFormRef"
          label-position="top"
          :model="siteForm"
          :rules="rules">
          <div class="form-row">
            <el-form-item :label="$t('Config.SiteForm.name')" prop="name">
              <el-input v-model="siteForm.name" :placeholder="$t('Config.SiteForm.namePlaceholder')"></el-input>
            </el-form-item>
            
            <el-form-item :label="$t('Config.SiteForm.code')" prop="code">
              <el-input v-model="siteForm.code" :placeholder="$t('Config.SiteForm.codePlaceholder')"></el-input>
            </el-form-item>
          </div>
          
          <div class="form-row">
            <el-form-item :label="$t('Config.SiteForm.domain')" prop="domain">
              <el-input v-model="siteForm.domain" :placeholder="$t('Config.SiteForm.domainPlaceholder')"></el-input>
            </el-form-item>
            
            <el-form-item :label="$t('Config.SiteForm.siteGroup')" prop="group_id">
              <el-select v-model="siteForm.group_id" :placeholder="$t('Config.SiteForm.siteGroupPlaceholder')">
                <el-option 
                  v-for="group in siteGroups" 
                  :key="group.value" 
                  :label="group.label" 
                  :value="group.value">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          
          <div>
            <el-form-item :label="$t('Config.SiteForm.aliases')" prop="aliases">
              <el-tag
                v-for="(alias, index) in siteForm.aliases"
                :key="index"
                closable
                @close="handleRemoveAlias(index)"
                class="alias-tag"
              >
                {{ alias }}
              </el-tag>
              <el-input
                v-if="inputVisible"
                ref="aliasInputRef"
                v-model="aliasInputValue"
                class="alias-input"
                size="small"
                @keyup.enter="handleAddAlias"
                @blur="handleAddAlias"
              />
              <el-button v-else class="button-new-tag" size="small" @click="showAliasInput">
                <el-icon size="16"><Plus /></el-icon>
                <span>{{ t('Config.SiteForm.addAlias') }}</span>
              </el-button>
            </el-form-item>
            
            <div class="form-options">
              <el-form-item :label="$t('Config.SiteForm.status')" prop="status">
                <el-radio-group v-model="siteForm.status">
                  <div class="option-item">
                    <el-radio :label="1">{{ $t('Config.SiteForm.statusEnabledLabel') }}</el-radio>
                    <div class="option-desc">{{ $t('Config.SiteForm.statusEnabledDesc') }}</div>
                  </div>
                  
                  <div class="option-item">
                    <el-radio :label="0">{{ $t('Config.SiteForm.statusDisabledLabel') }}</el-radio>
                    <div class="option-desc">{{ $t('Config.SiteForm.statusDisabledDesc') }}</div>
                  </div>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item :label="$t('Config.SiteForm.defaultSite')" prop="is_default">
                <el-switch v-model="siteForm.is_default"></el-switch>
                <div class="option-desc">{{ $t('Config.SiteForm.defaultSiteDesc') }}</div>
              </el-form-item>
            </div>
            
            <div class="form-group">
              <el-form-item :label="$t('Config.SiteForm.defaultLanguage')" prop="default_locale">
                <el-select v-model="siteForm.default_locale" :placeholder="$t('Config.SiteForm.defaultLanguagePlaceholder')">
                  <el-option 
                    v-for="locale in availableLocales" 
                    :key="locale.value" 
                    :label="locale.label" 
                    :value="locale.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            
            <div class="form-group">
              <el-form-item :label="$t('Config.SiteForm.enabledLanguages')" prop="enabled_locales">
                <el-select 
                  v-model="siteForm.enabled_locales" 
                  :placeholder="$t('Config.SiteForm.enabledLanguagesPlaceholder')" 
                  multiple>
                  <el-option 
                    v-for="locale in availableLocales" 
                    :key="locale.value" 
                    :label="locale.label" 
                    :value="locale.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            
            <div class="form-group">
              <el-form-item :label="$t('Config.SiteForm.timezone')" prop="timezone">
                <el-select v-model="siteForm.timezone" :placeholder="$t('Config.SiteForm.timezonePlaceholder')" filterable>
                  <el-option 
                    v-for="tz in timezones" 
                    :key="tz.value" 
                    :label="tz.label" 
                    :value="tz.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            
            <div class="form-group">
              <el-form-item :label="$t('Config.SiteForm.siteSettings')" prop="settings">
                <el-form-item style="margin-right: 10px;" :label="$t('Config.SiteForm.theme')" prop="settings.theme">
                  <el-input v-model="siteForm.settings.theme" :placeholder="$t('Config.SiteForm.themePlaceholder')"></el-input>
                </el-form-item>
                <el-form-item :label="$t('Config.SiteForm.logo')" prop="settings.logo">
                  <el-input v-model="siteForm.settings.logo" :placeholder="$t('Config.SiteForm.logoPlaceholder')"></el-input>
                </el-form-item>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    
    <div class="flex justify-center">
      <el-button class="button-cancel" @click="router.back()">{{ $t('Config.SiteForm.cancel') }}</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitLoading">{{ $t('Config.SiteForm.submit') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import http from '/admin/support/http'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n'

// 使用多语言
const { t } = useI18n()

// 定义接口
interface SiteForm {
  code: string
  name: string
  domain: string
  aliases: string[]
  group_id: number | null
  default_locale: string
  enabled_locales: string[]
  timezone: string
  settings: {
    theme: string
    logo: string
  }
  is_default: boolean
  status: number
}

interface SiteGroup {
  value: number
  label: string
}

interface Locale {
  value: string
  label: string
}

interface Timezone {
  value: string
  label: string
}

const router = useRouter()
const route = useRoute()

// 是否为编辑模式
const isEditMode = ref(!!route.params.id)
const siteId = route.params.id as string

// 页面加载状态
const pageLoading = ref(false)
const submitLoading = ref(false)
// 表单引用
const siteFormRef = ref()

// 表单数据
const siteForm = reactive<SiteForm>({
  code: '',
  name: '',
  domain: '',
  aliases: [],
  group_id: null,
  default_locale: 'zh-CN',
  enabled_locales: ['zh-CN'],
  timezone: 'Asia/Shanghai',
  settings: {
    theme: '',
    logo: ''
  },
  is_default: false,
  status: 1
})

// 域名别名相关
const inputVisible = ref(false)
const aliasInputValue = ref('')
const aliasInputRef = ref()

// 站点组列表
const siteGroups = ref<SiteGroup[]>([])

// 可用语言列表
const availableLocales = ref<Locale[]>([
  { value: 'zh-CN', label: '简体中文' },
  { value: 'zh-HK', label: '繁體中文' },
  { value: 'en-US', label: 'English' },
])

// 时区列表
const timezones = ref<Timezone[]>([
  { value: 'Asia/Shanghai', label: '亚洲/上海' },
  { value: 'Asia/Hong_Kong', label: '亚洲/香港' },
  { value: 'America/New_York', label: '美国/纽约' },
  { value: 'Europe/London', label: '欧洲/伦敦' },
  { value: 'UTC', label: 'UTC' }
])

// 表单验证规则
const rules = computed(() => ({
  code: [
    { required: true, message: t('Config.SiteForm.validation.codeRequired'), trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: t('Config.SiteForm.validation.codePattern'), trigger: 'blur' },
    { max: 50, message: t('Config.SiteForm.validation.codeLength'), trigger: 'blur' }
  ],
  name: [
    { required: true, message: t('Config.SiteForm.validation.nameRequired'), trigger: 'blur' },
    { max: 100, message: t('Config.SiteForm.validation.nameLength'), trigger: 'blur' }
  ],
  domain: [
    { required: true, message: t('Config.SiteForm.validation.domainRequired'), trigger: 'blur' },
    { pattern: /^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/, message: t('Config.SiteForm.validation.domainPattern'), trigger: 'blur' },
    { max: 255, message: t('Config.SiteForm.validation.domainLength'), trigger: 'blur' }
  ],
  group_id: [
    { required: true, message: t('Config.SiteForm.validation.groupRequired'), trigger: 'change' }
  ],
  default_locale: [
    { required: true, message: t('Config.SiteForm.validation.defaultLocaleRequired'), trigger: 'change' },
    { max: 10, message: t('Config.SiteForm.validation.defaultLocaleLength'), trigger: 'blur' }
  ],
  enabled_locales: [
    { required: true, message: t('Config.SiteForm.validation.enabledLocalesRequired'), trigger: 'change' },
    { type: 'array', min: 1, message: t('Config.SiteForm.validation.enabledLocalesMin'), trigger: 'change' }
  ],
  timezone: [
    { required: true, message: t('Config.SiteForm.validation.timezoneRequired'), trigger: 'change' },
    { max: 50, message: t('Config.SiteForm.validation.timezoneLength'), trigger: 'blur' }
  ]
}))

// 获取站点组列表
const fetchSiteGroups = async () => {
  try {
    const response = await http.get('/config/site-groups')
    if (response.data && response.data.data) {
      siteGroups.value = response.data.data.map((group: any) => ({
        value: group.id,
        label: group.name
      }))
    }
  } catch (error) {
    ElMessage.error(t('Config.SiteForm.getSiteGroupsFailed'))
  }
}

// 获取站点详情
const fetchSiteDetail = async () => {
  if (!isEditMode.value) return
  
  pageLoading.value = true
  try {
    const response = await http.get(`/config/sites/${siteId}`)
    if (response.data && response.data.data) {
      const siteData = response.data.data
      // 填充表单数据
      siteForm.code = siteData.code
      siteForm.name = siteData.name
      siteForm.domain = siteData.domain
      siteForm.aliases = siteData.aliases || []
      siteForm.group_id = siteData.group_id
      siteForm.default_locale = siteData.default_locale
      siteForm.enabled_locales = siteData.enabled_locales || []
      siteForm.timezone = siteData.timezone
      siteForm.settings = {
        theme: siteData.settings?.theme || '',
        logo: siteData.settings?.logo || ''
      }
      siteForm.is_default = siteData.is_default
      siteForm.status = siteData.status
    }
  } catch (error) {
    ElMessage.error(t('Config.SiteForm.getSiteDetailFailed'))
  } finally {
    pageLoading.value = false
  }
}

// 显示添加域名别名输入框
const showAliasInput = () => {
  inputVisible.value = true
  nextTick(() => {
    aliasInputRef.value?.focus()
  })
}

// 添加域名别名
const handleAddAlias = () => {
  if (aliasInputValue.value) {
    const domainPattern = /^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/
    if (!domainPattern.test(aliasInputValue.value)) {
      ElMessage.warning(t('Config.SiteForm.validation.domainPattern'))
      return
    }
    
    if (!siteForm.aliases.includes(aliasInputValue.value)) {
      siteForm.aliases.push(aliasInputValue.value)
    }
  }
  inputVisible.value = false
  aliasInputValue.value = ''
}

// 移除域名别名
const handleRemoveAlias = (index: number) => {
  siteForm.aliases.splice(index, 1)
}

// 提交表单
const submitForm = async () => {
  if (!siteFormRef.value) return
  try {
    await siteFormRef.value.validate()
    
    submitLoading.value = true
    const requestData = { ...siteForm }
    
    let response
    if (isEditMode.value) {
      // 编辑模式：更新站点
      response = await http.put(`/config/sites/${siteId}`, requestData)
    } else {
      // 创建模式：新增站点
      response = await http.post('/config/sites', requestData)
    }
    
    if (response.data && response.data.code === 200) {
      ElMessage.success(isEditMode.value ? t('Config.SiteForm.updateSuccess') : t('Config.SiteForm.createSuccess'))
      router.push('/config/sites/site-list')
    } else {
      ElMessage.error(response.data?.message || (isEditMode.value ? t('Config.SiteForm.updateFailed') : t('Config.SiteForm.createFailed')))
    }
  } catch (error) {

  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取站点组列表
onMounted(() => {
  fetchSiteGroups()
  fetchSiteDetail()
})
</script>

<style lang="scss" scoped>
.site-page {
  padding: 0 20px;
}

.site-content {
  background-color: white;
  border-radius: 4px;
  margin-bottom: 20px;
}

.site-header {
  border-bottom: 1px solid #ebeef5;
}

.site-form {
  padding: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-options {
  margin: 20px 0;
}

.option-item {
  margin-bottom: 16px;
}

.option-desc {
  margin-left: 26px;
  color: #909399;
  font-size: 14px;
}

.form-group {
  margin-bottom: 20px;
}

.form-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.select-desc {
  margin-top: 5px;
  color: #909399;
  font-size: 14px;
}

.or-text {
  margin: 0 10px;
  color: #909399;
}

.alias-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}

.alias-input {
  width: 200px;
  margin-right: 10px;
  vertical-align: bottom;
}

.button-new-tag {
  margin-bottom: 10px;
}
</style>