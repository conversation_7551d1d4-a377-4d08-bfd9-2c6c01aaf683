<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header flex items-center justify-between">
      <FilterPopover v-model="popoverVisible">
        <template #reference>
          <el-button class="filter-trigger" @click="popoverVisible = !popoverVisible">
            <el-icon><img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" /></el-icon>
            <span>{{ t('Config.Sites.filter') }}</span>
          </el-button>
        </template>
        <el-form :model="searchForm" label-position="top" @submit.prevent.stop>
          <el-form-item :label="t('Config.Sites.name')">
            <el-input v-model="searchForm.keyword" :placeholder="t('Config.Sites.keyword')+t('Config.Sites.name')" size="large" clearable />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="handleReset">
              <el-icon size="16"><Refresh /></el-icon>
              <span>{{ t('Config.Sites.reset') }}</span>
            </el-button>
            <el-button class="button-no-border" type="primary" @click="handleSearch">
              <el-icon size="16"><Filter /></el-icon>
              <span>{{ t('Config.Sites.filter') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button type="primary" @click="goToCreate">
        <el-icon><Plus /></el-icon>
        <span>{{ t('Config.Sites.create') }}</span>
      </el-button>
    </div>
    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格数据 -->
        <el-table 
          :data="siteList" 
          style="width: 100%" 
          v-loading="loading">
          <template #empty>
            <el-empty :description="t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="name" :label="t('Config.Sites.name')" min-width="120" />
          <el-table-column prop="code" :label="t('Config.Sites.code')" min-width="120" />
          <el-table-column prop="domain" :label="t('Config.Sites.domain')" min-width="220" />
          <el-table-column :label="t('Config.Sites.siteGroup')" min-width="120">
            <template #default="scope">
              {{ scope.row.group ? scope.row.group.name : '-' }}
            </template>
          </el-table-column>
          <el-table-column :label="t('Config.Sites.aliases')" min-width="220">
            <template #default="scope">
              <div v-if="scope.row.aliases && scope.row.aliases.length">
                <el-tooltip
                  v-for="(alias, index) in scope.row.aliases"
                  :key="index"
                  :content="alias"
                  placement="top"
                >
                  <el-tag size="small" class="alias-tag">{{ alias }}</el-tag>
                </el-tooltip>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('Config.Sites.languages')" min-width="120">
            <template #default="scope">
              <div>{{ t('Config.Sites.defaultLanguage') }}: {{ getLocaleName(scope.row.default_locale) }}</div>
              <div>
                {{ t('Config.Sites.enabledLanguages') }}: 
                <el-tooltip 
                  :content="scope.row.enabled_locales.map(locale => getLocaleName(locale)).join(', ')"
                  placement="top"
                >
                  <span>{{ scope.row.enabled_locales.length }}{{ t('Config.Sites.languageCount') }}</span>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="t('Config.Sites.defaultSite')" width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.is_default" type="success">{{ t('Config.Sites.yes') }}</el-tag>
              <el-button 
                v-else 
                link 
                type="primary"
                style="font-size: 14px;"
                @click="handleSetDefault(scope.row)"
                :disabled="setDefaultLoading">
                {{ t('Config.Sites.setAsDefault') }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column :label="t('Config.Sites.status')" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? t('Config.Sites.enabled') : t('Config.Sites.disabled') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('Config.Sites.operations')" width="120" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope.row)">
                <el-icon>
                  <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                </el-icon>
              </el-button>
              <el-button link type="danger" @click="handleDelete(scope.row)">
                <el-icon>
                  <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                </el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ t('Config.Sites.perPage') }}</span>
            <el-select
              v-model="pagination.limit"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 15, 20, 50]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ t('Config.Sites.total', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              background
              layout="prev, pager, next"
              :page-size="pagination.limit"
              :total="pagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Filter, Refresh } from '@element-plus/icons-vue'
import http from '/admin/support/http'
import { useI18n } from 'vue-i18n'
import FilterPopover from '/resources/admin/components/popover/index.vue'

const router = useRouter()
const loading = ref(false)
const setDefaultLoading = ref(false)
const { t } = useI18n()
const popoverVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
})

// 定义接口
interface Site {
  id: number
  code: string
  name: string
  domain: string
  aliases: string[]
  group_id: number
  default_locale: string
  enabled_locales: string[]
  timezone: string
  settings: {
    theme: string | null
    logo: string | null
  }
  is_default: boolean
  status: number
  created_at: string
  updated_at: string
  deleted_at: string | null
  created_by: number
  updated_by: number
  group: {
    id: number
    name: string
    code: string
    description: string | null
    status: number
    created_at: string
    updated_at: string
    deleted_at: string | null
    created_by: number
    updated_by: number
  }
}

// 接口响应数据结构
interface ApiResponse {
  code: number
  message: string
  data: Site[]
  total: number
  limit: number
  page: number
}

// 站点列表数据
const siteList = ref<Site[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 可用语言列表
const locales = [
  { value: 'zh-CN', label: '简体中文' },
  { value: 'zh-HK', label: '繁體中文' },
  { value: 'en-US', label: 'English' },
]

// 根据语言代码获取语言名称
const getLocaleName = (locale: string): string => {
  const found = locales.find(item => item.value === locale)
  return found ? found.label : locale
}

// 获取站点列表
const fetchSites = async () => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.page,
      limit: pagination.limit
    }
    
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword.trim()
    }
    
    const response = await http.get('/config/sites', { params })
    
    if (response.data && response.data.data) {
      siteList.value = response.data.data
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    ElMessage.error(t('Config.Sites.getListFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  popoverVisible.value = false
  fetchSites()
}

// 重置
const handleReset = () => {
  // 重置搜索表单
  searchForm.keyword = ''
  
  // 重置分页
  pagination.page = 1
  pagination.limit = 10
  popoverVisible.value = false
  
  // 重新获取数据
  fetchSites()
}

// 跳转到创建站点页面
const goToCreate = () => {
  router.push('/config/sites/site-create')
}

// 编辑站点
const handleEdit = (row: Site) => {
  router.push(`/config/sites/site-edit/${row.id}`)
}

// 删除站点
const handleDelete = (row: Site) => {
  ElMessageBox.confirm(
    t('Config.Sites.confirmDelete', { name: row.name }),
    t('Config.Sites.deleteConfirmTitle'),
    {
      confirmButtonText: t('Config.Sites.confirm'),
      cancelButtonText: t('Config.Sites.cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await http.delete(`/config/sites/${row.id}`)
        ElMessage.success(t('Config.Sites.deleteSuccess'))
        fetchSites() // 重新获取列表
      } catch (error) {
        ElMessage.error(t('Config.Sites.deleteFailed'))
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pagination.limit = size
  fetchSites()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchSites()
}

// 设置默认站点
const handleSetDefault = async (row: Site) => {
  try {
    setDefaultLoading.value = true
    const response = await http.put(`/config/sites/${row.id}/default`)
    
    if (response.data && response.data.code === 200) {
      ElMessage.success(t('Config.Sites.setDefaultSuccess'))
      fetchSites() // 重新获取列表数据
    }
  } catch (error) {
    ElMessage.error(t('Config.Sites.setDefaultFailed'))
  } finally {
    setDefaultLoading.value = false
  }
}

// 页面加载时获取站点列表
onMounted(() => {
  fetchSites()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100%; // 使用视口高度
  margin-top: 0;
  padding: 0;
  
  .module-header {
    flex-shrink: 0; // 防止头部被压缩
    padding: 0 20px;
    margin-bottom: 12px;
  }
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    
    .box {
      padding-top: 12px;
      padding-bottom: 0;
      background: #fff;
      border-radius: 4px;
      flex: 1; // 让盒子占据剩余空间
      display: flex;
      flex-direction: column;
      overflow: auto; // 允许内容区域滚动

      .search-area {
        margin-bottom: 20px;
        flex-shrink: 0; // 防止搜索区域被压缩
        
        .flex-spacer {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }
        
        .create-button {
          margin-top: -20px;
        }
      }

    }

    .box-footer {
      background: #fff;
      border-radius: 0 0 4px 4px;
      padding: 10px 20px;
      margin-top: 1px;
    }
  }

  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .pagination-left {
      display: flex;
      align-items: center;
      
      .page-size-text {
        margin-right: 8px;
        color: #606266;
      }
      
      .page-size-select {
        width: 80px;
        margin-right: 8px;
      }
      
      .total-text {
        color: #606266;
      }
    }
  }
}

.alias-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>