<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <el-button class="button-no-border" :disabled="selects.length === 0" @click="onBatchChangeStatus('running')">
        {{ $t('CronJob.commons.button.enable') }}
      </el-button>
      <el-button class="button-no-border" :disabled="selects.length === 0" @click="onBatchChangeStatus('failed')">
        {{ $t('CronJob.commons.button.disable') }}
      </el-button>
      <el-button class="button-no-border" :disabled="selects.length === 0" @click="onDelete(null)">
        {{ $t('CronJob.commons.button.delete') }}
      </el-button>
      <TableSetting @search="search({ silent: true })" v-model:searchName="searchName" />
      
      <FilterPopover 
        v-model="showFilterDropdown"
      >
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
            <el-icon>
              <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
            </el-icon>
            <span>{{ $t('CronJob.commons.button.search') }}</span>
          </el-button>
        </template>
        
        <el-form :model="searchForm" label-position="top">
          <el-form-item :label="$t('CronJob.commons.search.keyword')" class="filter-form-item">
            <el-input v-model="searchForm.keyword" :placeholder="$t('CronJob.commons.search.keyword_placeholder')" size="default" />
          </el-form-item>
        </el-form>
        
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="refreshList">
              <el-icon><Refresh /></el-icon>
              <span>{{ $t('CronJob.commons.button.refresh') }}</span>
            </el-button>
            <el-button class="button-no-border" type="primary" @click="getSearchList">
              <el-icon><Filter /></el-icon>
              <span>{{ $t('CronJob.commons.button.filter') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      
      <el-button @click="onOpenDialog('create')" type="primary">
      <el-icon size="16"><Plus /></el-icon>
      <span>{{ $t('CronJob.commons.button.create') }}</span>
    </el-button>
    </div>

    <div class="module-con">
      <div class="box">

        <el-table ref="cronjobTableRef" style="width: 100%; height: 100%" :data="data">
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" fix />
          <el-table-column :label="$t('CronJob.cronjob.taskName')" :min-width="100" prop="name" sortable show-overflow-tooltip>
            <template #default="{ row }">
              <el-text type="primary" class="cursor-pointer" @click="loadDetail(row)">
                {{ row.name }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column :label="$t('CronJob.commons.table.status')" :min-width="80" prop="status" sortable>
            <template #default="{ row }">
              <el-dropdown @command="status => onChangeStatus(row.id, status)">
                <el-button link :type="row.status === JOB_STATUS.PENDING ? 'success' : 'danger'">
                  {{ t(`CronJob.commons.status.${row.status}`) }}
                  <el-icon class="el-icon--right">
                    <ArrowDown />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="option in statusOptions" :key="option.value" :command="option.value" :disabled="option.value === row.status">
                      {{ option.label }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
          <el-table-column :label="$t('CronJob.cronjob.cronSpec')" show-overflow-tooltip :min-width="120">
            <template #default="{ row }">
              <div v-for="(item, index) of row.spec.split(',')" :key="index">
                <div v-if="row.expand || (!row.expand && index < 3)">
                  <span>
                    {{ transSpecToStr(item) || '--' }}
                  </span>
                </div>
              </div>
              <div v-if="!row.expand && row.spec.split(',').length > 3">
                <el-button type="primary" link @click="row.expand = true"> {{ $t('CronJob.commons.button.expand') }}... </el-button>
              </div>
              <div v-if="row.expand && row.spec.split(',').length > 3">
                <el-button type="primary" link @click="row.expand = false">
                  {{ $t('CronJob.commons.button.collapse') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('CronJob.cronjob.retainCopies')" :min-width="90" prop="retainCopies">
            <template #default="{ row }">
              <el-button v-if="hasBackup(row.type)" @click="loadBackups(row)" plain size="small"> {{ row.retainCopies }}{{ $t('CronJob.cronjob.retainCopiesUnit') }} </el-button>
              <span v-else>{{ row.retainCopies }}</span>
            </template>
          </el-table-column> -->
          <el-table-column :label="$t('CronJob.cronjob.lastRecordTime')" :min-width="120" prop="last_executed_at">
            <template #default="{ row }">
              {{ row.last_executed_at || '--' }}
            </template>
          </el-table-column>
          <!-- <el-table-column :min-width="80" :label="$t('CronJob.setting.backupAccount')" prop="defaultDownload">
            <template #default="{ row }">
              <span v-if="!hasBackup(row.type)">-</span>
              <div v-else>
                <div v-for="(item, index) of row.backupAccounts?.split(',')" :key="index">
                  <div v-if="row.accountExpand || (!row.accountExpand && index < 3)">
                    <span v-if="row.backupAccounts">
                      <span>
                        {{ $t('CronJob.setting.' + item) }}
                      </span>
                      <el-icon size="12" v-if="item === row.defaultDownload" class="relative top-px left-1">
                        <Star />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </div>
                </div>
                <div v-if="!row.accountExpand && row.backupAccounts?.split(',').length > 3">
                  <el-button type="primary" link @click="row.accountExpand = true"> {{ $t('CronJob.commons.button.expand') }}... </el-button>
                </div>
                <div v-if="row.accountExpand && row.backupAccounts?.split(',').length > 3">
                  <el-button type="primary" link @click="row.accountExpand = false">
                    {{ $t('CronJob.commons.button.collapse') }}
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column :label="t('CronJob.commons.table.operate')" width="240" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <el-button @click="onHandle(row)" type="text" class="bwms-operate-btn" :loading="handleLoading[row.id]">
                  <el-tooltip :content="t('CronJob.commons.button.handle')" placement="top">
                    <el-icon color="#4a70b2" size="16">
                      <CircleCheck />
                    </el-icon>
                  </el-tooltip>
                </el-button>
                <el-button @click="onOpenDialog('edit', row)" type="text" class="bwms-operate-btn">
                  <el-tooltip :content="t('CronJob.commons.button.edit')" placement="top">
                    <el-icon size="16">
                      <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                    </el-icon>
                  </el-tooltip>
                </el-button>
                <el-button @click="loadDetail(row)" type="text" class="bwms-operate-btn">
                  <el-tooltip :content="t('CronJob.commons.table.records')" placement="top">
                    <el-icon color="#4a70b2" size="16">
                      <MessageBox />
                    </el-icon>
                  </el-tooltip>
                </el-button>
                <el-button @click="downloadBackup(row.last_record_id)" type="text" class="bwms-operate-btn" :loading="downloadLoading[row.id]">
                  <el-tooltip :content="t('CronJob.commons.button.download')" placement="top">
                    <el-icon color="#4a70b2" size="16">
                      <Download />
                    </el-icon>
                  </el-tooltip>
                </el-button>
                <el-button @click="onDelete(row)" type="text" class="bwms-operate-btn" :loading="deleteLoading[row.id]">
                  <el-tooltip :content="t('CronJob.commons.button.delete')" placement="top">
                    <el-icon size="16">
                      <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                    </el-icon>
                  </el-tooltip>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页器 -->
      <div class="box-footer" v-if="paginationConfig">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="paginationConfig.pageSize"
              class="page-size-select"
              @change="sizeChange"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  {{ t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="paginationConfig.currentPage"
              background
              layout="prev, pager, next"
              :page-size="paginationConfig.pageSize"
              :total="paginationConfig.total"
              @current-change="currentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <OpDialog ref="opRef" @search="search" @submit="onSubmitDelete()">
      <template #content>
        <el-form class="mt-4 mb-1" v-if="showClean" ref="deleteForm" label-position="left">
          <el-form-item>
            <el-checkbox v-model="cleanData" :label="$t('CronJob.cronjob.cleanData')" />
            <span class="input-help">
              {{ $t('CronJob.cronjob.cleanDataHelper') }}
            </span>
          </el-form-item>
        </el-form>
      </template>
    </OpDialog>
    <OperateDialog @search="search" ref="dialogRef" />
    <Records @search="search" @close="closeRecordView" ref="dialogRecordRef" />
    <Backups @search="search" ref="dialogBackupRef" />
  </div>
</template>

<script lang="ts" setup>
import { ArrowDown, Plus, Filter, Refresh } from '@element-plus/icons-vue'
import OperateDialog from './operate/index.vue'
import Records from './record/index.vue'
import Backups from './backup/index.vue'
import ComplexTable from '../components/complex-table/index.vue'
import LayoutContent from '../components/layout-content/index.vue'
import TableSearch from '../components/table-search/index.vue'
import TableSetting from '../components/table-setting/index.vue'
import { onMounted, reactive, ref } from 'vue'
import { Cronjob } from '../api/interface/cronjob'
import { ElMessageBox } from 'element-plus'
import { MsgSuccess } from '../utils/message'
import { transSpecToStr } from '../utils/cronJobUtils'
import http from '/admin/support/http'
import { useRouter } from 'vue-router'
const loading = ref()
const selects = ref<any>([])
const isRecordShow = ref()
const operateIDs = ref()
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const opRef = ref()
const showClean = ref()
const cleanData = ref()

const data = ref()
const paginationConfig = reactive({
  cacheSizeKey: 'cronjob-page-size',
  currentPage: 1,
  pageSize: 20,
  total: 0,
  orderBy: 'created_at',
  order: 'null',
})
const searchName = ref('')

interface SearchForm {
  keyword: string
}

const showFilterDropdown = ref(false)
const searchForm = reactive<SearchForm>({
  keyword: ''
})

const router = useRouter()

const refreshList = () => {
  searchForm.keyword = ''
  searchName.value = ''
  showFilterDropdown.value = false
  search({ silent: false })
}

const getSearchList = async () => {
  if (searchForm.keyword === '') {
    search({ silent: false })
  } else {
    searchName.value = searchForm.keyword
    search({ silent: false })
  }
  showFilterDropdown.value = false
}

const search = async (options: { silent?: boolean } = {}) => {
  if (!options.silent) {
    loading.value = true
  }

  try {
    const res = await http.post('/cronjob/list', {
      keyword: searchName.value,
      page: paginationConfig.currentPage,
      perPage: paginationConfig.pageSize,
    })
    data.value = res.data.data.data
    paginationConfig.total = res.data.data.total
  } catch (error) {
    console.error('Failed to fetch data:', error)
  } finally {
    if (!options.silent) {
      loading.value = false
    }
  }
}

const dialogRecordRef = ref()
const dialogBackupRef = ref()

const dialogRef = ref()
const onOpenDialog = async (
  title: string,
  rowData: Partial<Cronjob.CronjobInfo> = {
    specObjs: [
      {
        specType: 'perMonth',
        week: 1,
        day: 3,
        hour: 1,
        minute: 30,
        second: 30,
      },
    ],
    type: 'shell',
    retainCopies: 7,
  },
) => {
  let params = {
    title,
    rowData: { ...rowData },
  }
  dialogRef.value!.acceptParams(params)
}

const deleteLoading = ref<Record<number, boolean>>({})

const onDelete = async (row: Cronjob.CronjobInfo | null) => {
  let names = []
  let ids: any[] = []
  showClean.value = false
  cleanData.value = false

  if (row) {
    ids = [row.id]
    names = [row.name]
    if (hasBackup(row.type)) {
      showClean.value = true
    }
    deleteLoading.value[row.id] = true
  } else {
    ids = selects.value.map((item: any) => item.id)
  }

  operateIDs.value = ids
  ElMessageBox.confirm(t('CronJob.commons.msg.operatorHelper', [t('CronJob.cronjob.cronTask'), t('CronJob.commons.button.delete')]), t('CronJob.commons.button.delete'), {
    confirmButtonText: t('CronJob.commons.button.confirm'),
    cancelButtonText: t('CronJob.commons.button.cancel'),
    type: 'warning',
  })
    .then(async () => {
      try {
        await http.post('/cronjob/del', { cleanData: cleanData.value, ids: ids })
        MsgSuccess(t('CronJob.commons.msg.deleteSuccess'))
        search()
      } catch (error) {
        console.error('Delete failed:', error)
      } finally {
        if (row) {
          deleteLoading.value[row.id] = false
        }
      }
    })
    .catch(() => {
      if (row) {
        deleteLoading.value[row.id] = false
      }
    })
}

const onSubmitDelete = async () => {
  loading.value = true
  await onDelete(null)
    .then(() => {
      loading.value = false
      MsgSuccess(t('commons.msg.deleteSuccess'))
      search()
    })
    .catch(() => {
      loading.value = false
    })
}

const JOB_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  WAITING: 'waiting',
} as const

const statusOptions = [
  {
    label: t('CronJob.commons.status.pending'),
    value: JOB_STATUS.PENDING,
  },
  {
    label: t('CronJob.commons.status.running'),
    value: JOB_STATUS.RUNNING,
  },
  {
    label: t('CronJob.commons.status.completed'),
    value: JOB_STATUS.COMPLETED,
  },
  {
    label: t('CronJob.commons.status.failed'),
    value: JOB_STATUS.FAILED,
  },
  {
    label: t('CronJob.commons.status.waiting'),
    value: JOB_STATUS.WAITING,
  },
]

const onChangeStatus = async (id: number, newStatus: string) => {
  ElMessageBox.confirm(t(`CronJob.cronjob.${newStatus}Msg`), t('CronJob.cronjob.changeStatus'), {
    confirmButtonText: t('CronJob.commons.button.confirm'),
    cancelButtonText: t('CronJob.commons.button.cancel'),
  }).then(async () => {
    await http.post('/cronjob/status', { id, status: newStatus })
    MsgSuccess(t('CronJob.commons.msg.operationSuccess'))
    search()
  })
}

const onBatchChangeStatus = async (status: string) => {
  ElMessageBox.confirm(t(`CronJob.cronjob.${status}Msg`), t('CronJob.cronjob.changeStatus'), {
    confirmButtonText: t('CronJob.commons.button.confirm'),
    cancelButtonText: t('CronJob.commons.button.cancel'),
  }).then(async () => {
    for (const item of selects.value) {
      await http.post('/cronjob/status', { id: item.id, status: status })
    }
    MsgSuccess(t('CronJob.commons.msg.operationSuccess'))
    search()
  })
}

const loadBackups = async (row: any) => {
  dialogBackupRef.value!.acceptParams({ cronjobID: row.id, cronjob: row.name })
}

const handleLoading = ref<Record<number, boolean>>({})
const downloadLoading = ref<Record<number, boolean>>({})

const onHandle = async (row: Cronjob.CronjobInfo) => {
  handleLoading.value[row.id] = true
  try {
    await http.post('/cronjob/handle', { id: row.id })
    MsgSuccess(t('CronJob.commons.msg.operationSuccess'))
    search()
  } catch (error) {
    // 错误处理
  } finally {
    handleLoading.value[row.id] = false
  }
}

const hasBackup = (type: string) => {
  return type === 'app' || type === 'website' || type === 'database' || type === 'directory' || type === 'snapshot' || type === 'log'
}

const loadDetail = (row: any) => {
  isRecordShow.value = true
  let params = {
    rowData: { ...row },
  }
  dialogRecordRef.value!.acceptParams(params)
}
const closeRecordView = () => {
  isRecordShow.value = false
  search()
}
const downloadBackup = async (id: number) => {
  downloadLoading.value[id] = true
  try {
    await http.post('/cronjob/download', { id })
    MsgSuccess(t('CronJob.commons.msg.operationSuccess'))
  } catch (error) {
    console.error('Download failed:', error)
  } finally {
    downloadLoading.value[id] = false
  }
}

// 分页大小改变处理函数
const sizeChange = async (size: number) => {
  paginationConfig.pageSize = size
  paginationConfig.currentPage = 1 // 重置到第一页
  await search({ silent: false })
}

// 页码改变处理函数
const currentChange = async (page: number) => {
  paginationConfig.currentPage = page
  await search({ silent: false })
}

onMounted(() => {
  search({ silent: false })
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
    }
  }
}


</style>
