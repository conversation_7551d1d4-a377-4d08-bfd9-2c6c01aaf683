<?php

namespace Modules\Editor\Admin\Controllers;

use Bingo\Base\BingoController;
use Illuminate\Http\Request;
use Modules\Editor\Services\CmsEditorBridgeService;
use Bingo\Exceptions\BizException;

class CmsEditorController extends BingoController
{
    public function __construct(
        protected readonly CmsEditorBridgeService $bridgeService
    ) {
    }

    /**
     * 将CMS页面转换为可视化编辑器页面
     *
     * @param Request $request
     * @return array
     */
    public function convertCmsPage(Request $request): array
    {
        $validated = $request->validate([
            'cms_content_id' => 'required|integer|exists:cms_content,id',
        ]);

        try {
            $result = $this->bridgeService->convertCmsPageToEditorPage($validated['cms_content_id']);
            return [
                'code' => 0,
                'message' => '转换成功',
                'data' => $result
            ];
        } catch (BizException $e) {
            return [
                'code' => $e->getCode() ?: 400,
                'message' => $e->getMessage(),
                'data' => null
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '转换失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 同步编辑器页面到CMS页面
     *
     * @param Request $request
     * @return array
     */
    public function syncEditorPage(Request $request): array
    {
        $validated = $request->validate([
            'editor_page_id' => 'required|integer|exists:editor_pages,id',
        ]);

        try {
            $result = $this->bridgeService->syncEditorPageToCmsPage($validated['editor_page_id']);
            return [
                'code' => 0,
                'message' => '同步成功',
                'data' => $result
            ];
        } catch (BizException $e) {
            return [
                'code' => $e->getCode() ?: 400,
                'message' => $e->getMessage(),
                'data' => null
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '同步失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取支持可视化编辑的CMS模型列表
     *
     * @return array
     */
    public function getSupportedModels(): array
    {
        try {
            $models = \Modules\Cms\Models\CmsModel::where('visual_edit_enabled', 1)
                ->get()
                ->toArray();
                
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => $models
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取CMS内容的可视化编辑页面
     *
     * @param int $cmsContentId
     * @return array
     */
    public function getEditorPageByCmsContent(int $cmsContentId): array
    {
        try {
            $cmsContent = \Modules\Cms\Models\CmsContent::findOrFail($cmsContentId);
            
            if (!$cmsContent->editor_page_id) {
                return [
                    'code' => 404,
                    'message' => '该内容尚未创建可视化编辑页面',
                    'data' => null
                ];
            }
            
            $editorPage = \Modules\Editor\Models\EditorPage::findOrFail($cmsContent->editor_page_id);
            
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => $editorPage->toArray()
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 更新CMS模型的可视化编辑配置
     *
     * @param Request $request
     * @param int $modelId
     * @return array
     */
    public function updateModelConfig(Request $request, int $modelId): array
    {
        $validated = $request->validate([
            'visual_edit_enabled' => 'required|boolean',
            'visual_edit_config' => 'nullable|array',
        ]);

        try {
            $model = \Modules\Cms\Models\CmsModel::findOrFail($modelId);
            $model->visual_edit_enabled = $validated['visual_edit_enabled'];
            $model->visual_edit_config = $validated['visual_edit_config'] ?? null;
            $model->save();
            
            return [
                'code' => 0,
                'message' => '更新成功',
                'data' => $model->toArray()
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
}
