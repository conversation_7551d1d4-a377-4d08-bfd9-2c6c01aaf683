<?php

namespace Modules\Editor\Admin\Controllers;

use Bingo\Base\BingoController;
use Illuminate\Http\Request;
use Modules\Editor\Admin\Requests\EditorPageRequest;
use Modules\Editor\Services\EditorPageService;

class EditorPageController extends BingoController
{
    public function __construct(
        protected readonly EditorPageService $pageService
    ) {
    }

    /**
     * 获取页面列表
     *
     * @return array
     */
    public function index(): array
    {
        return $this->pageService->getAllPages();
    }

    /**
     * 创建页面
     *
     * @param EditorPageRequest $request
     * @return array
     */
    public function store(EditorPageRequest $request)
    {
        return $this->pageService->createPage($request->validated());
    }

    /**
     * 获取页面详情
     *
     * @param int $id
     * @return array
     */
    public function show(int $id): array
    {
        return $this->pageService->getPageById($id);
    }

    /**
     * 更新页面
     *
     * @param EditorPageRequest $request
     * @param int $id
     * @return array
     */
    public function update(EditorPageRequest $request, int $id): array
    {
        return $this->pageService->updatePage($id, $request->validated());
    }

    /**
     * 删除页面
     *
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        $result = $this->pageService->deletePage($id);
        return ['success' => $result];
    }

    /**
     * 根据分类获取页面
     *
     * @param int $categoryId
     * @return array
     */
    public function getByCategory(int $categoryId): array
    {
        return $this->pageService->getPagesByCategory($categoryId);
    }

    /**
     * 根据slug获取页面
     *
     * @param string $slug
     * @return array
     */
    public function getBySlug(string $slug): array
    {
        return $this->pageService->getPageBySlug($slug);
    }

    /**
     * 发布页面
     *
     * @param int $id
     * @return array
     */
    public function publish(int $id): array
    {
        return $this->pageService->publishPage($id);
    }

    /**
     * 将页面设为草稿
     *
     * @param int $id
     * @return array
     */
    public function draft(int $id): array
    {
        return $this->pageService->draftPage($id);
    }
}
