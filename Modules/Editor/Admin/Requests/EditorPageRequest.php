<?php

namespace Modules\Editor\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EditorPageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        // 获取当前请求的方法
        $method = $this->method();
        
        // 基础规则
        $rules = [
            'content' => 'nullable|array',
            'status' => 'nullable|in:draft,pending,rejected,published',
            'category_id' => 'nullable|integer|exists:bingo_editor_categories,id',
            'cms_content_id' => 'nullable|integer',
            'cms_model_id' => 'nullable|integer',
        ];
        
        // 根据请求方法设置不同的规则
        if ($method === 'POST') {
            // 创建页面时，标题是必需的
            $rules['title'] = 'required|string|max:255';
            $rules['slug'] = 'nullable|string|max:255|unique:bingo_editor_pages,slug';
        } else if ($method === 'PUT' || $method === 'PATCH') {
            // 更新页面时，标题是可选的
            $rules['title'] = 'nullable|string|max:255';
            
            // 更新时需要排除当前记录的唯一性检查
            $id = $this->route('id');
            $rules['slug'] = 'nullable|string|max:255|unique:bingo_editor_pages,slug,' . $id;
        }
        
        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'title.required' => T('Editor::validation.page.title.required'),
            'title.max' => T('Editor::validation.page.title.max'),
            'content.array' => T('Editor::validation.page.content.array'),
            'status.in' => T('Editor::validation.page.status.in'),
            'category_id.exists' => T('Editor::validation.page.category_id.exists'),
            'slug.unique' => T('Editor::validation.page.slug.unique'),
            'cms_content_id.integer' => T('Editor::validation.page.cms_content_id.integer'),
            'cms_model_id.integer' => T('Editor::validation.page.cms_model_id.integer'),
        ];
    }
}
