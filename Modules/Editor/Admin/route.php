<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Editor\Admin\Controllers\EditorController;
use Mo<PERSON><PERSON>\Editor\Admin\Controllers\EditorPageController;
use Modules\Editor\Admin\Controllers\EditorTemplateController;
use Modules\Editor\Admin\Controllers\EditorCategoryController;
use Mo<PERSON><PERSON>\Editor\Admin\Controllers\CmsEditorController;

Route::prefix('editor')->group(function () {
    // 原有路由
    Route::get('editor', [EditorController::class, 'editor']);
    Route::post('save', [EditorController::class, 'save'])->name('bingo-admin.editor.save');


    // 页面管理
    Route::apiResource('pages', EditorPageController::class);
    // 模板管理
    Route::apiResource('templates', EditorTemplateController::class);
    // 分类管理
    Route::apiResource('categories', EditorCategoryController::class);

    // 根据分类获取模板
    Route::get('templates/category/{categoryId}', [EditorTemplateController::class, 'getByCategory']);

    // 根据分类获取页面
    Route::get('pages/category/{categoryId}', [EditorPageController::class, 'getByCategory']);

    // 根据slug获取页面
    Route::get('pages/slug/{slug}', [EditorPageController::class, 'getBySlug']);

    // 发布页面
    Route::put('pages/{id}/publish', [EditorPageController::class, 'publish']);

    // 将页面设为草稿
    Route::put('pages/{id}/draft', [EditorPageController::class, 'draft']);

    // CMS兼容相关路由
    Route::prefix('cms')->group(function () {
        // 将CMS页面转换为可视化编辑器页面
        Route::post('convert', [CmsEditorController::class, 'convertCmsPage']);

        // 同步编辑器页面到CMS页面
        Route::post('sync', [CmsEditorController::class, 'syncEditorPage']);

        // 获取支持可视化编辑的CMS模型列表
        Route::get('models', [CmsEditorController::class, 'getSupportedModels']);

        // 获取CMS内容的可视化编辑页面
        Route::get('content/{cmsContentId}/editor-page', [CmsEditorController::class, 'getEditorPageByCmsContent']);

        // 更新CMS模型的可视化编辑配置
        Route::put('models/{modelId}/config', [CmsEditorController::class, 'updateModelConfig']);
    });
});
