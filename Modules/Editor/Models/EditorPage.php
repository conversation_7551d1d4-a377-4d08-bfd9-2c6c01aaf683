<?php

namespace Modules\Editor\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id 主键ID
 * @property string $title 页面标题
 * @property string $description 页面描述
 * @property string $keywords 页面关键词
 * @property array $content 页面内容，JSON格式
 * @property string $status 状态：draft(草稿)、published(已发布)
 * @property int $category_id 分类ID
 * @property string $slug 页面别名，用于URL
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 */
class EditorPage extends Model
{
    protected $table = 'editor_pages';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'content',
        'status',
        'category_id',
        'slug',
        'creator_id',
        'created_at',
        'updated_at',
    ];

    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'content' => 'json',
    ];

    /**
     * 状态常量
     */
    const STATUS_DRAFT = 'draft';        // 草稿
    const STATUS_PENDING = 'pending';    // 待审核
    const STATUS_REJECTED = 'rejected';  // 已拒绝
    const STATUS_PUBLISHED = 'published'; // 已发布

    /**
     * 获取页面所属的分类
     *
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(EditorCategory::class, 'category_id');
    }

    /**
     * 获取草稿状态的页面
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function drafts()
    {
        return static::where('status', self::STATUS_DRAFT);
    }

    /**
     * 获取已发布状态的页面
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function published()
    {
        return static::where('status', self::STATUS_PUBLISHED);
    }

    /**
     * 获取特定分类下的页面
     *
     * @param int $categoryId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function byCategory($categoryId)
    {
        return static::where('category_id', $categoryId);
    }

    /**
     * 搜索页面
     *
     * @param string $keyword
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function search($keyword)
    {
        return static::where('title', 'like', "%{$keyword}%")
                     ->orWhere('description', 'like', "%{$keyword}%")
                     ->orWhere('keywords', 'like', "%{$keyword}%");
    }

    /**
     * 根据slug获取页面
     *
     * @param string $slug
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function findBySlug($slug)
    {
        return static::where('slug', $slug);
    }

    /**
     * 生成唯一的slug
     *
     * @param string $title
     * @return string
     */
    public static function generateSlug($title)
    {
        // 尝试使用Str::slug生成slug
        $slug = \Str::slug($title);

        // 如果生成的slug为空（通常是因为标题全是中文或其他非拉丁字符）
        if (empty($slug)) {
            // 方法1：使用标题的md5哈希值的前8位
            $slug = substr(md5($title), 0, 8);

            // 方法2：也可以使用拼音转换，但需要额外的库支持
            // 如果项目中有拼音转换库，可以使用类似下面的代码
            // $slug = \Pinyin::permalink($title);
        }

        // 确保slug的唯一性
        $originalSlug = $slug;
        $count = 0;

        // 检查slug是否已存在，如果存在则添加数字后缀
        while (static::where('slug', $slug)->count() > 0) {
            $count++;
            $slug = "{$originalSlug}-{$count}";
        }

        return $slug;
    }
}
