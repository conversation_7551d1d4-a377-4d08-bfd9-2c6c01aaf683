<?php

namespace Modules\Editor\Providers;

use Illuminate\Support\ServiceProvider;
use Modules\Editor\Services\CmsEditorBridgeService;

class CmsEditorServiceProvider extends ServiceProvider
{
    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        // 注册服务
        $this->app->singleton(CmsEditorBridgeService::class, function ($app) {
            return new CmsEditorBridgeService();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // 注册视图组件
        $this->loadViewsFrom(__DIR__.'/../views', 'editor');
        
        // 注册迁移文件
        $this->loadMigrationsFrom(__DIR__.'/../database/migrations');
        
        // 发布资源
        $this->publishes([
            __DIR__.'/../views/components' => resource_path('js/components/editor'),
        ], 'editor-components');
    }
}
