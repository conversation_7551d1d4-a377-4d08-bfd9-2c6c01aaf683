<?php

namespace Modules\Editor\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use <PERSON><PERSON><PERSON>\Editor\Providers\CmsEditorServiceProvider;

class EditorServiceProvider extends BingoModuleServiceProvider
{
    public function boot(): void
    {
        $path = dirname(__DIR__, 2) . DIRECTORY_SEPARATOR . 'Editor' . DIRECTORY_SEPARATOR . 'Lang';
        $this->loadTranslationsFrom($path, 'Editor');
        $this->registerNavigation();
        $this->publishes([
            __DIR__ . '/../Config/n1ed.php' => config_path('n1ed.php'),
        ], 'config');

        // 注册CMS编辑器服务提供者
        $this->app->register(CmsEditorServiceProvider::class);
    }

    /**
     * route path
     *
     * @return string
     */
    public function moduleName(): string
    {
        return 'Editor';
    }

    protected function navigation(): array
    {
        return [
            [
                "key" => "editor",
                "parent" => "application",
                "nav_name" => T("Editor::nav.editor"),
                "path" => "/editorTiptap",
                "icon" => "Edit",
                "order" => 11
            ]
        ];
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [];
    }
}
