<?php

namespace Modules\Editor\Services;

use Illuminate\Support\Facades\DB;
use Modules\Cms\Models\CmsContent;
use Modules\Cms\Models\CmsModel;
use Modules\Cms\Models\CmsPage;
use Modules\Editor\Models\EditorPage;
use Bingo\Exceptions\BizException;

/**
 * CMS与可视化编辑器桥接服务
 * 
 * 负责处理CMS内容与可视化编辑器内容之间的转换和同步
 */
class CmsEditorBridgeService
{
    /**
     * 将CMS页面内容转换为可视化编辑器页面
     *
     * @param int $cmsContentId CMS内容ID
     * @return array 创建的编辑器页面数据
     */
    public function convertCmsPageToEditorPage(int $cmsContentId): array
    {
        // 获取CMS内容
        $cmsContent = CmsContent::findOrFail($cmsContentId);
        
        // 获取CMS模型
        $cmsModel = CmsModel::findOrFail($cmsContent->model_id);
        
        // 检查模型是否支持可视化编辑
        if (!$cmsModel->visual_edit_enabled) {
            throw new BizException('该内容模型不支持可视化编辑');
        }
        
        // 获取页面内容
        $cmsPageContent = CmsPage::where('content_id', $cmsContentId)
            ->where('lang', $cmsContent->lang)
            ->first();
            
        if (!$cmsPageContent) {
            throw new BizException('未找到对应的页面内容');
        }
        
        // 转换HTML内容为JSON格式
        $jsonContent = $this->convertHtmlToJson($cmsPageContent->content);
        
        // 创建编辑器页面
        DB::beginTransaction();
        try {
            // 创建编辑器页面
            $editorPage = new EditorPage();
            $editorPage->title = $cmsContent->title;
            $editorPage->description = $cmsContent->summary ?? '';
            $editorPage->keywords = $cmsContent->seo_keywords ?? '';
            $editorPage->content = $jsonContent;
            $editorPage->status = $cmsContent->status ? 'published' : 'draft';
            $editorPage->category_id = $cmsContent->category_id;
            $editorPage->slug = $cmsContent->alias ?? $this->generateSlug($cmsContent->title);
            $editorPage->creator_id = $cmsContent->creator_id;
            $editorPage->cms_content_id = $cmsContent->id;
            $editorPage->cms_model_id = $cmsContent->model_id;
            $editorPage->cms_content_type = 'page';
            $editorPage->save();
            
            // 更新CMS内容
            $cmsContent->editor_page_id = $editorPage->id;
            $cmsContent->content_format = 'visual';
            $cmsContent->save();
            
            // 更新CMS页面内容
            $cmsPageContent->json_content = $jsonContent;
            $cmsPageContent->save();
            
            DB::commit();
            
            return $editorPage->toArray();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 将编辑器页面内容同步到CMS页面
     *
     * @param int $editorPageId 编辑器页面ID
     * @return array 更新后的CMS内容数据
     */
    public function syncEditorPageToCmsPage(int $editorPageId): array
    {
        // 获取编辑器页面
        $editorPage = EditorPage::findOrFail($editorPageId);
        
        // 检查是否关联了CMS内容
        if (!$editorPage->cms_content_id) {
            throw new BizException('该编辑器页面未关联CMS内容');
        }
        
        // 获取CMS内容
        $cmsContent = CmsContent::findOrFail($editorPage->cms_content_id);
        
        // 获取CMS页面内容
        $cmsPageContent = CmsPage::where('content_id', $cmsContent->id)
            ->where('lang', $cmsContent->lang)
            ->first();
            
        if (!$cmsPageContent) {
            throw new BizException('未找到对应的CMS页面内容');
        }
        
        // 将JSON内容转换为HTML
        $htmlContent = $this->convertJsonToHtml($editorPage->content);
        
        // 更新CMS内容
        DB::beginTransaction();
        try {
            // 更新CMS内容
            $cmsContent->title = $editorPage->title;
            $cmsContent->summary = $editorPage->description;
            $cmsContent->seo_keywords = $editorPage->keywords;
            $cmsContent->status = $editorPage->status === 'published' ? 1 : 0;
            $cmsContent->save();
            
            // 更新CMS页面内容
            $cmsPageContent->content = $htmlContent;
            $cmsPageContent->json_content = $editorPage->content;
            $cmsPageContent->save();
            
            DB::commit();
            
            return $cmsContent->toArray();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 将HTML内容转换为JSON格式
     *
     * @param string $html HTML内容
     * @return array JSON格式内容
     */
    protected function convertHtmlToJson(string $html): array
    {
        // 这里实现HTML到JSON的转换逻辑
        // 实际实现会更复杂，这里只是一个简化示例
        
        $dom = new \DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        
        $blocks = [];
        $blockId = 1;
        
        // 处理标题
        $headings = $dom->getElementsByTagName('h1');
        foreach ($headings as $heading) {
            $blocks[] = [
                'id' => 'header-' . $blockId++,
                'type' => 'header',
                'data' => [
                    'text' => $heading->textContent,
                    'level' => 1,
                    'alignment' => 'left'
                ]
            ];
        }
        
        // 处理段落
        $paragraphs = $dom->getElementsByTagName('p');
        foreach ($paragraphs as $paragraph) {
            $blocks[] = [
                'id' => 'paragraph-' . $blockId++,
                'type' => 'paragraph',
                'data' => [
                    'text' => $paragraph->textContent,
                    'alignment' => 'left'
                ]
            ];
        }
        
        // 处理图片
        $images = $dom->getElementsByTagName('img');
        foreach ($images as $image) {
            $blocks[] = [
                'id' => 'image-' . $blockId++,
                'type' => 'image',
                'data' => [
                    'url' => $image->getAttribute('src'),
                    'caption' => $image->getAttribute('alt'),
                    'width' => '100%',
                    'height' => 'auto',
                    'alignment' => 'center'
                ]
            ];
        }
        
        return [
            'version' => '1.0',
            'blocks' => $blocks,
            'settings' => [
                'background' => '#ffffff',
                'maxWidth' => '1200px',
                'padding' => '20px'
            ]
        ];
    }
    
    /**
     * 将JSON格式内容转换为HTML
     *
     * @param array $json JSON格式内容
     * @return string HTML内容
     */
    protected function convertJsonToHtml(array $json): string
    {
        // 这里实现JSON到HTML的转换逻辑
        // 实际实现会更复杂，这里只是一个简化示例
        
        $html = '';
        
        if (isset($json['blocks']) && is_array($json['blocks'])) {
            foreach ($json['blocks'] as $block) {
                switch ($block['type']) {
                    case 'header':
                        $level = $block['data']['level'] ?? 1;
                        $text = $block['data']['text'] ?? '';
                        $alignment = $block['data']['alignment'] ?? 'left';
                        $html .= "<h{$level} style=\"text-align: {$alignment}\">{$text}</h{$level}>";
                        break;
                        
                    case 'paragraph':
                        $text = $block['data']['text'] ?? '';
                        $alignment = $block['data']['alignment'] ?? 'left';
                        $html .= "<p style=\"text-align: {$alignment}\">{$text}</p>";
                        break;
                        
                    case 'image':
                        $url = $block['data']['url'] ?? '';
                        $caption = $block['data']['caption'] ?? '';
                        $width = $block['data']['width'] ?? '100%';
                        $height = $block['data']['height'] ?? 'auto';
                        $alignment = $block['data']['alignment'] ?? 'center';
                        $html .= "<div style=\"text-align: {$alignment}\"><img src=\"{$url}\" alt=\"{$caption}\" style=\"width: {$width}; height: {$height};\"></div>";
                        if ($caption) {
                            $html .= "<div style=\"text-align: {$alignment}\"><small>{$caption}</small></div>";
                        }
                        break;
                }
            }
        }
        
        return $html;
    }
    
    /**
     * 生成唯一的slug
     *
     * @param string $title 标题
     * @return string 生成的slug
     */
    protected function generateSlug(string $title): string
    {
        $slug = \Str::slug($title);
        $count = EditorPage::where('slug', $slug)->count();
        
        return $count ? "{$slug}-{$count}" : $slug;
    }
}
