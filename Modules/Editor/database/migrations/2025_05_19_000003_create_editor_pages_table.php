<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('editor_pages', function (Blueprint $table) {

            $table->comment('可视化编辑器页面表');
            $table->bigIncrements('id')->comment('主键ID');
            $table->string('title')->comment('页面标题');
            $table->json('content')->comment('页面内容，JSON格式');
            $table->enum('status', ['draft', 'pending', 'rejected', 'published'])->default('draft')->comment('状态：draft(草稿)、pending(待审核)、rejected(已拒绝)、published(已发布)');
            $table->unsignedBigInteger('category_id')->nullable()->comment('分类ID');
            $table->string('slug')->nullable()->unique()->comment('页面别名，用于URL');

            $table->unsignedBigInteger('cms_content_id')->index()->nullable()->comment('关联的CMS内容ID');
            $table->unsignedBigInteger('cms_model_id')->index()->nullable()->comment('关联的CMS模型ID');

            $table->unsignedInteger('creator_id')->default(0)->comment('创建者ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('editor_pages');
    }
};
