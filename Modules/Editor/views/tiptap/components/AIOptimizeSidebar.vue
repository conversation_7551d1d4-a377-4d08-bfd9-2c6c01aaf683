<template>
  <div class="ai-optimize-sidebar" v-show="visible">
    <div class="sidebar-header">
      <div class="header-left">
        <h3 class="sidebar-title">AI优化模块</h3>
      </div>
      <button class="close-button" @click="closeSidebar">×</button>
    </div>

    <div class="sidebar-content">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">AI正在生成优化内容，请稍候...</div>
      </div>
      
      <div v-else class="main-content">
        <div class="input-section">
          <p class="input-label">模块优化指令</p>
          <el-input
            v-model="promptText"
            type="textarea"
            :rows="4"
            :placeholder="placeholderText"
            resize="none"
            @keydown.enter.ctrl.prevent="generateContent"
          />
          <div class="prompt-tips">
            <p>提示: 
              <el-tooltip content="描述你希望如何优化这个模块，比如'改进文案''调整样式''添加更多细节'等" placement="top">
                <span class="tooltip-trigger">如何编写有效指令?</span>
              </el-tooltip>
            </p>
          </div>

          <div class="action-buttons">
            <el-button 
              type="primary" 
              @click="generateContent" 
              :disabled="loading || !promptText.trim()"
              :loading="loading"
            >
              生成优化内容
            </el-button>
            <el-button @click="closeSidebar">
              取消
            </el-button>
          </div>
        </div>

        <div v-if="generatedContent" class="output-section">
          <div class="output-header">
            <span>生成结果</span>
            <div class="output-actions">
              <el-button type="primary" size="small" @click="applyChange">
                应用内容
              </el-button>
              <el-button size="small" @click="resetContent">
                重新生成
              </el-button>
            </div>
          </div>
          
          <div class="output-preview">
            <div class="preview-content">
              <iframe-renderer :content="generatedContent" height="100%" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch } from 'vue'
import http from '/admin/support/http'
import { ElMessage } from 'element-plus'

// 解决TypeScript类型问题，添加默认导出
import { defineAsyncComponent, defineOptions } from 'vue'

defineOptions({
  name: 'AIOptimizeSidebar'
})

// 使用本地ContentRenderer替代iframe-renderer
const IframeRenderer = defineAsyncComponent(() => 
  import('./ContentRenderer.vue')
)

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  },
  blockContent: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['close', 'apply-change', 'update:visible'])

// 提示文本和占位符
const promptText = ref('')
const loading = ref(false)
const generatedContent = ref('')

// 根据块类型提供不同的占位符文本
const placeholderText = computed(() => {
  const blockTypeMap: Record<string, string> = {
    'bootstrap-button': '例: 优化按钮文案，使用更吸引人的文字，突出行动号召性',
    'bootstrap-card': '例: 调整卡片内容，增加更多细节，使用更专业的语言',
    'bootstrap-hero': '例: 重新设计英雄区域，使其更有冲击力，修改标题和副标题',
    'richTextBlock': '例: 优化文本内容，改进语法和表达，使其更专业',
    'default': '请描述您希望如何优化此模块，例如改进文案、调整样式或添加更多细节'
  }
  
  return blockTypeMap[props.blockType] || blockTypeMap['default']
})

// 监听可见性变化，重置状态
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 侧边栏打开时的处理
  } else {
    // 侧边栏关闭时重置状态
    resetForm()
  }
})

// 关闭侧边栏
const closeSidebar = () => {
  emit('update:visible', false)
  emit('close')
}

// 生成优化内容
const generateContent = async () => {
  if (!promptText.value.trim()) {
    ElMessage.warning('请输入优化指令')
    return
  }
  
  try {
    loading.value = true
    
    // 准备发送到API的数据
    const requestData = {
      block_type: props.blockType,
      original_content: props.blockContent,
      instruction: promptText.value.trim(),
    }
    
    // 模拟API调用
    // 实际项目中，这里应该调用真实的API
    const response = await mockApiCall(requestData)
    
    if (response.success) {
      generatedContent.value = response.content
      ElMessage.success('优化内容生成成功')
    } else {
      ElMessage.error(response.message || '生成内容失败')
    }
  } catch (error) {
    console.error('生成内容时出错:', error)
    ElMessage.error('生成内容时出错，请重试')
  } finally {
    loading.value = false
  }
}

// 模拟API调用函数（实际项目中应替换为真实API调用）
const mockApiCall = async (data: any) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // 根据不同的块类型返回不同的模拟内容
  let mockContent = ''
  
  if (data.block_type.includes('button')) {
    mockContent = `<button class="btn btn-primary btn-lg">立即行动，获取专属优惠！</button>`
  } else if (data.block_type.includes('card')) {
    mockContent = `
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">专业解决方案</h5>
          <p class="card-text">我们提供行业领先的专业服务，帮助您实现业务目标，提升企业价值。通过定制化方案满足您的独特需求。</p>
          <a href="#" class="btn btn-primary">了解更多</a>
        </div>
      </div>
    `
  } else if (data.block_type.includes('hero')) {
    mockContent = `
      <div class="p-5 text-center text-white hero-container bg-primary">
        <h1 class="display-4 fw-bold">引领行业创新，成就卓越未来</h1>
        <p class="lead">借助我们的专业知识和丰富经验，为您的业务注入新的活力和发展动力</p>
        <div class="gap-2 d-grid d-sm-flex justify-content-sm-center">
          <button type="button" class="gap-3 px-4 btn btn-light btn-lg">立即开始</button>
          <button type="button" class="px-4 btn btn-outline-light btn-lg">了解更多</button>
        </div>
      </div>
    `
  } else {
    // 默认返回一些优化后的富文本内容
    mockContent = `
      <div>
        <h2>专业解决方案，满足您的需求</h2>
        <p>我们提供全方位的专业服务，帮助您解决业务挑战，实现可持续发展。通过深入了解您的需求，我们定制最适合您的解决方案。</p>
        <ul>
          <li>专业的团队支持</li>
          <li>定制化的解决方案</li>
          <li>全天候的技术支持</li>
          <li>行业领先的技术应用</li>
        </ul>
        <p>立即与我们联系，开启成功之旅！</p>
      </div>
    `
  }
  
  // 声明正确的返回类型包含message字段
  return {
    success: true,
    content: mockContent,
    message: '' // 添加message字段以满足类型检查
  }
}

// 应用生成的内容
const applyChange = () => {
  if (!generatedContent.value) {
    ElMessage.warning('没有可应用的内容')
    return
  }
  
  emit('apply-change', generatedContent.value)
  ElMessage.success('已应用优化内容')
  
  // 应用后关闭侧边栏
  setTimeout(() => {
    closeSidebar()
  }, 500)
}

// 重置表单
const resetContent = () => {
  generatedContent.value = ''
}

// 重置整个表单
const resetForm = () => {
  promptText.value = ''
  generatedContent.value = ''
  loading.value = false
}
</script>

<style lang="scss" scoped>
.ai-optimize-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sidebar-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  
  &:hover {
    color: #f56c6c;
  }
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  .loading-text {
    color: #666;
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  .input-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 0;
  }
}

.prompt-tips {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  
  .tooltip-trigger {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.output-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  
  span {
    font-weight: 500;
    font-size: 14px;
  }
  
  .output-actions {
    display: flex;
    gap: 8px;
  }
}

.output-preview {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  min-height: 200px;
  background: #f8f9fa;
  overflow: auto;
  
  .preview-content {
    width: 100%;
    height: 100%;
    min-height: 180px;
  }
}
</style> 