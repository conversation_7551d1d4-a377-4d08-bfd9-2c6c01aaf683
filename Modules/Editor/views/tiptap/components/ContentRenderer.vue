<template>
  <div class="content-renderer" :style="{ height }">
    <div class="rendered-content" ref="contentRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, onMounted, watch, defineOptions } from 'vue'

defineOptions({
  name: 'ContentRenderer'
})

const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '100%'
  }
})

const contentRef = ref<HTMLElement | null>(null)

// 更新内容
const updateContent = () => {
  if (contentRef.value) {
    contentRef.value.innerHTML = props.content || ''
  }
}

// 初始化和内容变化时更新
onMounted(updateContent)
watch(() => props.content, updateContent)
</script>

<style lang="scss" scoped>
.content-renderer {
  width: 100%;
  overflow: auto;
  border-radius: 4px;
  
  .rendered-content {
    font-family: 'Inter', 'Microsoft JhengHei', sans-serif;
    color: #333;
    line-height: 1.6;
    
    /* Bootstrap 基础样式模拟 */
    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      margin-top: 0;
      margin-bottom: 0.5rem;
      font-weight: 500;
      line-height: 1.2;
    }
    
    :deep(h1) { font-size: 2.5rem; }
    :deep(h2) { font-size: 2rem; }
    :deep(h3) { font-size: 1.75rem; }
    :deep(h4) { font-size: 1.5rem; }
    :deep(h5) { font-size: 1.25rem; }
    :deep(h6) { font-size: 1rem; }
    
    :deep(p) {
      margin-top: 0;
      margin-bottom: 1rem;
    }
    
    :deep(ul), :deep(ol) {
      margin-top: 0;
      margin-bottom: 1rem;
      padding-left: 2rem;
    }
    
    :deep(img) {
      max-width: 100%;
      height: auto;
    }
    
    /* 模拟Bootstrap按钮 */
    :deep(.btn) {
      display: inline-block;
      font-weight: 400;
      text-align: center;
      white-space: nowrap;
      vertical-align: middle;
      user-select: none;
      border: 1px solid transparent;
      padding: 0.375rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      border-radius: 0.25rem;
      transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, 
                  border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      text-decoration: none;
    }
    
    :deep(.btn-primary) {
      color: #fff;
      background-color: #0d6efd;
      border-color: #0d6efd;
    }
    
    :deep(.btn-lg) {
      padding: 0.5rem 1rem;
      font-size: 1.25rem;
      border-radius: 0.3rem;
    }
    
    /* 模拟Bootstrap卡片 */
    :deep(.card) {
      position: relative;
      display: flex;
      flex-direction: column;
      min-width: 0;
      word-wrap: break-word;
      background-color: #fff;
      background-clip: border-box;
      border: 1px solid rgba(0, 0, 0, 0.125);
      border-radius: 0.25rem;
    }
    
    :deep(.card-body) {
      flex: 1 1 auto;
      padding: 1.25rem;
    }
    
    :deep(.card-title) {
      margin-bottom: 0.75rem;
    }
    
    :deep(.card-text) {
      margin-top: 0;
      margin-bottom: 1rem;
    }
    
    /* Bootstrap 工具类 */
    :deep(.text-center) { text-align: center; }
    :deep(.bg-primary) { background-color: #0d6efd; }
    :deep(.text-white) { color: #fff; }
    :deep(.p-5) { padding: 3rem; }
    :deep(.d-grid) { display: grid; }
    :deep(.gap-2) { gap: 0.5rem; }
    :deep(.d-sm-flex) { display: flex; }
    :deep(.justify-content-sm-center) { justify-content: center; }
    
    /* 其他常用Bootstrap类 */
    :deep(.display-4) { 
      font-size: 3.5rem;
      font-weight: 300;
      line-height: 1.2;
    }
    
    :deep(.lead) {
      font-size: 1.25rem;
      font-weight: 300;
    }
    
    :deep(.fw-bold) { font-weight: 700; }
  }
}
</style> 