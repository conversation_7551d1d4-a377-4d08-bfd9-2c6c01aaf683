<template>
  <div class="block-edit-sidebar">
    <div class="sidebar-header">
      <div class="header-left">
        <button class="back-button" @click="closeBlockEdit">
          <span class="back-icon">←</span>
        </button>
        <h3 class="sidebar-title">{{ getBlockTypeTitle(blockType) }}</h3>
      </div>
      <button class="close-button" @click="closeBlockEdit">×</button>
    </div>

    <div class="block-edit-content">
      <!-- 直接渲染对应的编辑器组件 -->
      <component
        :is="getContentComponent()"
        :block-element="blockElement"
        :block-type="blockType"
        :styles="blockStyles"
        @update-block="updateBlock"
        @update-styles="updateStyles"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits, onMounted, defineAsyncComponent } from 'vue'

// 使用懒加载方式导入编辑器组件
const StyleEditor = defineAsyncComponent(() => import('./block-editors/StyleEditor.vue'))

const ButtonEditor = defineAsyncComponent(() => import('./block-editors/ButtonEditor.vue'))
const AlertEditor = defineAsyncComponent(() => import('./block-editors/AlertEditor.vue'))
const CardEditor = defineAsyncComponent(() => import('./block-editors/CardEditor.vue'))
const AClassEditor = defineAsyncComponent(() => import('./block-editors/AClassEditor.vue'))
const AccordionEditor = defineAsyncComponent(() => import('./block-editors/AccordionEditor.vue'))
const DividerEditor = defineAsyncComponent(() => import('./block-editors/DividerEditor.vue'))
const LayoutEditor = defineAsyncComponent(() => import('./block-editors/LayoutEditor.vue'))
const CountdownEditor = defineAsyncComponent(() => import('./block-editors/CountdownEditor.vue'))
const FeatureListEditor = defineAsyncComponent(() => import('./block-editors/FeatureListEditor.vue'))
const FeatureCardsEditor = defineAsyncComponent(() => import('./block-editors/FeatureCardsEditor.vue'))
const FormEditor = defineAsyncComponent(() => import('./block-editors/FormEditor.vue'))
const TableEditor = defineAsyncComponent(() => import('./block-editors/TableEditor.vue'))
const HeadingEditor = defineAsyncComponent(() => import('./block-editors/HeadingEditor.vue'))
const MetricsEditor = defineAsyncComponent(() => import('./block-editors/MetricsEditor.vue'))
const PricingEditor = defineAsyncComponent(() => import('./block-editors/PricingEditor.vue'))
const RichTextEditor = defineAsyncComponent(() => import('./block-editors/RichTextEditor.vue'))
const SocialFlowEditor = defineAsyncComponent(() => import('./block-editors/SocialFlowEditor.vue'))
const TestimonialSliderEditor = defineAsyncComponent(() => import('./block-editors/TestimonialSliderEditor.vue'))
const StatsCardEditor = defineAsyncComponent(() => import('./block-editors/StatsCardEditor.vue'))
const InfoSectionEditor = defineAsyncComponent(() => import('./block-editors/infoSectionEditor.vue'))
const HeroEditor = defineAsyncComponent(() => import('./block-editors/HeroEditor.vue'))
const FooterEditor = defineAsyncComponent(() => import('./block-editors/FooterEditor.vue'))
const CTAEditor = defineAsyncComponent(() => import('./block-editors/CTAEditor.vue'))
const ImageEditor = defineAsyncComponent(() => import('./block-editors/ImageEditor.vue'))
const NavEditor = defineAsyncComponent(() => import('./block-editors/NavEditor.vue'))
const TimelineEditor = defineAsyncComponent(() => import('./block-editors/TimelineEditor.vue'))

const props = defineProps({
  blockType: {
    type: String,
    default: ''
  },
  blockElement: {
    type: Object,
    default: null
  },
  blockStyles: {
    type: Object,
    default: () => ({
      width: 'auto',
      textAlign: 'left',
      marginTop: 0,
      marginRight: 0,
      marginBottom: 0,
      marginLeft: 0,
      paddingTop: 0,
      paddingRight: 0,
      paddingBottom: 0,
      paddingLeft: 0
    })
  }
})

const emit = defineEmits(['close', 'update-block', 'update-styles'])

// 定义模块类型与名称映射
const moduleTypeNameMap: Record<string, string> = {
  // Bootstrap组件
  'bootstrap-alert': '提示框',
  'bootstrap-card': '卡片',
  'bootstrap-button-group': '按钮组',
  'bootstrap-button': '按钮',
  'bootstrap-form': '表单',
  'bootstrap-nav': '导航',
  'bootstrap-carousel': '轮播图',
  'bootstrap-table': '表格',
  'bootstrap-accordion': '手风琴',
  'bootstrap-divider': '间隔线',
  'bootstrap-a-class': 'A类宣传模块',
  'bootstrap-layout': '布局',
  'bootstrap-countdown': '倒计时',
  'bootstrap-feature-list': '功能列表',
  'bootstrap-heading': '大标题',
  'bootstrap-metrics': '数据统计',
  'bootstrap-pricing': '定价卡片',
  'bootstrap-cta': '号召行动',
  'bootstrap-hero': '英雄区域',
  'bootstrap-footer': '页脚',
  
  // 特殊组件类型
  'heading': '大标题',
  'cta': '号召行动',
  'hero': '英雄区域',
  'footer': '页脚',
  
  // 核心模块
  'richTextBlock': '富文本',
  'socialFlowBlock': '社交媒体',
  'testimonialSliderBlock': '客户评价',
  'timelineBlock': '时间线',
  'statsCardBlock': '数据卡片',
  'navbarBlock': '导航栏',
  'heroBlock': '英雄区域',
  'featureCardsBlock': '特性卡片',
  'infoSectionBlock': '信息区块',
  'footerBlock': '页脚',
  
  // 数据属性标识
  'rich-text': '富文本',
  'social-flow': '社交媒体',
  'social-media': '社交媒体',
  'testimonial-slider': '客户评价',
  'timeline-block': '时间线',
  'stats-card': '数据卡片',
  'navbar': '导航栏',
  'feature-cards': '特性卡片',
  'info-section': '信息区块',
  'image': '图片',
  'image-group': '图片组'
}

// 根据块类型获取对应的标题
const getBlockTypeTitle = (blockType: string) => {
  return moduleTypeNameMap[blockType] || '编辑块'
}

// 模块类型到编辑器组件的映射
const editorComponentMap: Record<string, any> = {
  // Bootstrap组件
  'bootstrap-alert': AlertEditor,
  'bootstrap-button': ButtonEditor,
  'bootstrap-card': CardEditor,
  'bootstrap-button-group': ButtonEditor,
  'bootstrap-a-class': AClassEditor,
  'bootstrap-accordion': AccordionEditor,
  'bootstrap-divider': DividerEditor,
  'bootstrap-layout': LayoutEditor,
  'bootstrap-countdown': CountdownEditor,
  'bootstrap-feature-list': FeatureListEditor,
  'bootstrap-form': FormEditor,
  'bootstrap-nav': StyleEditor,
  'bootstrap-carousel': StyleEditor,
  'bootstrap-table': TableEditor,
  'bootstrap-heading': HeadingEditor,
  'bootstrap-metrics': MetricsEditor,
  'bootstrap-pricing': PricingEditor,
  'bootstrap-cta': CTAEditor,
  'bootstrap-hero': HeroEditor,
  'bootstrap-footer': FooterEditor,
  'bootstrap-image': ImageEditor,
  
  // 特殊类型
  'heading': HeadingEditor,
  'cta': CTAEditor,
  'hero': HeroEditor,
  'footer': FooterEditor,
  
  // 核心模块
  'richTextBlock': RichTextEditor,
  'socialFlowBlock': SocialFlowEditor,
  'testimonialSliderBlock': TestimonialSliderEditor,
  'timelineBlock': TimelineEditor,
  'statsCardBlock': StatsCardEditor,
  'navbarBlock': NavEditor,
  'heroBlock': HeroEditor,
  'featureCardsBlock': FeatureCardsEditor,
  'infoSectionBlock': InfoSectionEditor,
  'footerBlock': FooterEditor,
  
  // 数据属性标识
  'rich-text': RichTextEditor,
  'social-flow': SocialFlowEditor,
  'social-media': SocialFlowEditor,
  'testimonial-slider': TestimonialSliderEditor,
  'bootstrap-timeline': TimelineEditor,
  'stats-card': StatsCardEditor,
  'navbar': NavEditor,
  'feature-cards': FeatureCardsEditor,
  'info-section': InfoSectionEditor,
  'image': ImageEditor,
  'image-group': ImageEditor
}

// 根据块类型获取对应的内容编辑组件
const getContentComponent = () => {
  // 优先处理带有data-bs-component属性的元素类型
  if (props.blockElement && props.blockElement.getAttribute) {
    const dataComponent = props.blockElement.getAttribute('data-bs-component')
    if (dataComponent && editorComponentMap[dataComponent]) {
      return editorComponentMap[dataComponent]
    }
  }
  
  // 然后检查blockType是否在映射表中
  if (props.blockType && editorComponentMap[props.blockType]) {
    return editorComponentMap[props.blockType]
  }
  
  // 默认返回StyleEditor
  return StyleEditor
}

// 关闭块编辑
const closeBlockEdit = () => {
  emit('close')
}

// 更新块内容
const updateBlock = (data: { html?: string }) => {
  // 简单传递数据，不在这里直接操作DOM
  emit('update-block', data)
}

// 更新样式
const updateStyles = (styles: any) => {
  emit('update-styles', styles)
}
</script>

<style lang="scss" scoped>
.block-edit-sidebar {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  transition: all 0.3s ease;
}

.sidebar-header {
  padding: 12px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  background: none;
  border: none;
  font-size: 18px;
  color: #606266;
  cursor: pointer;
  margin-right: 10px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;

  &:hover {
    color: #409eff;
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  padding: 0;
  line-height: 1;

  &:hover {
    color: #f56c6c;
  }
}

.sidebar-title {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.block-edit-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;

  /* 确保内容不会溢出 */
  max-width: 100%;
  box-sizing: border-box;
}
</style>