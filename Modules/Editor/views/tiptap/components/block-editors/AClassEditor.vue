<template>
  <div class="edit-section">
    <el-form label-position="top">
      <el-form-item label="标题">
        <el-input v-model="aClassTitle" @input="updateAClassContent" />
      </el-form-item>
      
      <el-form-item label="副标题">
        <el-input
          v-model="aClassSubtitle"
          type="textarea"
          :rows="3"
          @input="updateAClassContent"
        />
      </el-form-item>
      
      <el-form-item label="特性列表">
        <div v-for="(feature, index) in aClassFeatures" :key="index" class="feature-item">
          <div class="feature-header">
            <span class="feature-title">特性 {{ index + 1 }}</span>
            <el-button 
              type="danger" 
              size="small" 
              icon="el-icon-delete" 
              circle 
              @click="removeFeature(index)" 
              v-if="aClassFeatures.length > 1"
            />
          </div>
          <el-input 
            v-model="aClassFeatures[index]" 
            @input="updateAClassContent" 
            placeholder="输入特性描述" 
          />
        </div>
        <el-button type="primary" plain class="add-feature-btn" @click="addFeature">
          添加特性
        </el-button>
      </el-form-item>
      
      <el-form-item label="主按钮文本">
        <el-input v-model="aClassPrimaryButton" @input="updateAClassContent" />
      </el-form-item>
      
      <el-form-item label="次要按钮文本">
        <el-input v-model="aClassSecondaryButton" @input="updateAClassContent" />
      </el-form-item>
      
      <el-form-item label="图片URL">
        <el-input 
          v-model="aClassImageUrl" 
          @input="updateAClassContent" 
          placeholder="输入图片链接地址" 
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, watch } from 'vue'

// 为了解决TypeScript类型问题，添加默认导出
defineOptions({
  name: 'AClassEditor'
})

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 编辑器状态
const isInitialized = ref(false)
const isUpdating = ref(false)
const hasChanges = ref(false)
const lastError = ref<Error | null>(null)

// 防抖处理
const debounceTimeout = ref<number | null>(null)

// A类模块数据
const aClassTitle = ref('探索更好的解决方案')
const aClassSubtitle = ref('我们提供专业的服务，让您的业务更上一层楼。无论您需要什么，我们都能提供最佳解决方案。')
const aClassFeatures = ref(['高效的工作流程', '专业的团队支持', '全天候客户服务'])
const aClassPrimaryButton = ref('立即开始')
const aClassSecondaryButton = ref('了解更多')
const aClassImageUrl = ref('https://via.placeholder.com/600x400')

/**
 * 数据验证
 */
const validateData = (): boolean => {
  try {
    // 验证标题
    if (!aClassTitle.value?.trim()) {
      throw new Error('标题不能为空')
    }

    // 验证副标题
    if (!aClassSubtitle.value?.trim()) {
      throw new Error('副标题不能为空')
    }

    // 验证特性列表
    if (!aClassFeatures.value?.length) {
      throw new Error('至少需要一个特性')
    }

    // 验证按钮文本
    if (!aClassPrimaryButton.value?.trim() || !aClassSecondaryButton.value?.trim()) {
      throw new Error('按钮文本不能为空')
    }

    // 验证图片URL
    if (!aClassImageUrl.value?.trim()) {
      throw new Error('图片URL不能为空')
    }

    return true
  } catch (error) {
    lastError.value = error as Error
    console.error('数据验证失败:', error)
    return false
  }
}

/**
 * 重置编辑器状态到默认值
 */
const resetEditorState = () => {
  // 重置数据
  aClassTitle.value = '探索更好的解决方案'
  aClassSubtitle.value = '我们提供专业的服务，让您的业务更上一层楼。无论您需要什么，我们都能提供最佳解决方案。'
  aClassFeatures.value = ['高效的工作流程', '专业的团队支持', '全天候客户服务']
  aClassPrimaryButton.value = '立即开始'
  aClassSecondaryButton.value = '了解更多'
  aClassImageUrl.value = 'https://via.placeholder.com/600x400'

  // 重置状态
  isInitialized.value = false
  isUpdating.value = false
  hasChanges.value = false
  lastError.value = null

  // 清除防抖定时器
  if (debounceTimeout.value) {
    clearTimeout(debounceTimeout.value)
    debounceTimeout.value = null
  }
}

/**
 * 从HTML元素中提取A类模块数据
 */
const extractAClassData = (element: HTMLElement): boolean => {
  try {
    isUpdating.value = true
    lastError.value = null

    // 提取标题
    const titleEl = element.querySelector('.a-class-title')
    if (titleEl) {
      aClassTitle.value = titleEl.textContent?.trim() || aClassTitle.value
    }
    
    // 提取副标题
    const subtitleEl = element.querySelector('.a-class-subtitle')
    if (subtitleEl) {
      aClassSubtitle.value = subtitleEl.textContent?.trim() || aClassSubtitle.value
    }
    
    // 提取特性列表
    const featureEls = element.querySelectorAll('.a-class-feature-text')
    if (featureEls.length > 0) {
      const features = Array.from(featureEls)
        .map(el => el.textContent?.trim() || '特性')
        .filter(Boolean)
      if (features.length > 0) {
        aClassFeatures.value = features
      }
    }
    
    // 提取按钮文本
    const primaryButtonEl = element.querySelector('.btn-primary')
    if (primaryButtonEl) {
      aClassPrimaryButton.value = primaryButtonEl.textContent?.trim() || aClassPrimaryButton.value
    }
    
    const secondaryButtonEl = element.querySelector('.btn-outline-secondary')
    if (secondaryButtonEl) {
      aClassSecondaryButton.value = secondaryButtonEl.textContent?.trim() || aClassSecondaryButton.value
    }
    
    // 提取图片URL
    const imgEl = element.querySelector('img') as HTMLImageElement
    if (imgEl && imgEl.src) {
      aClassImageUrl.value = imgEl.src
    }

    // 验证提取的数据
    if (!validateData()) {
      throw new Error('提取的数据验证失败')
    }
    
    return true
  } catch (error) {
    console.error('提取A类模块数据时出错:', error)
    lastError.value = error as Error
    return false
  } finally {
    isUpdating.value = false
  }
}

/**
 * 初始化编辑器
 */
const initializeEditor = (element: HTMLElement | null) => {
  try {
    isUpdating.value = true
    lastError.value = null

    if (!element) {
      resetEditorState()
      return
    }

    // 提取数据
    const dataExtracted = extractAClassData(element)
    
    // 如果提取失败，使用默认值
    if (!dataExtracted) {
      resetEditorState()
    } else {
      isInitialized.value = true
    }
    
    // 初始化完成后更新内容
    debouncedUpdateContent()
  } catch (error) {
    console.error('初始化编辑器时出错:', error)
    lastError.value = error as Error
    resetEditorState()
  } finally {
    isUpdating.value = false
  }
}

/**
 * 防抖处理的内容更新
 */
const debouncedUpdateContent = () => {
  if (debounceTimeout.value) {
    clearTimeout(debounceTimeout.value)
  }

  debounceTimeout.value = window.setTimeout(() => {
    if (validateData()) {
      updateAClassContent()
      hasChanges.value = false
    }
  }, 300) // 300ms 防抖延迟
}

// 监听所有数据变化
watch([
  () => props.blockElement,
  () => aClassTitle.value,
  () => aClassSubtitle.value,
  () => aClassFeatures.value,
  () => aClassPrimaryButton.value,
  () => aClassSecondaryButton.value,
  () => aClassImageUrl.value
], ([newBlockElement, ...rest], [oldBlockElement, ...oldRest]) => {
  // 如果是 blockElement 变化
  if (newBlockElement !== oldBlockElement) {
    if (newBlockElement) {
      initializeEditor(newBlockElement as HTMLElement)
    } else {
      resetEditorState()
    }
    return
  }

  // 如果是其他数据变化
  if (isInitialized.value && !isUpdating.value) {
    hasChanges.value = true
    debouncedUpdateContent()
  }
}, { deep: true, immediate: true })

// 单独监听特性列表的深层变化
watch(() => [...aClassFeatures.value], () => {
  if (isInitialized.value && !isUpdating.value) {
    hasChanges.value = true
    debouncedUpdateContent()
  }
}, { deep: true })

// 添加特性时的处理
const addFeature = () => {
  isUpdating.value = true
  try {
    aClassFeatures.value.push('新特性')
    hasChanges.value = true
    debouncedUpdateContent()
  } finally {
    isUpdating.value = false
  }
}

// 移除特性时的处理
const removeFeature = (index: number) => {
  isUpdating.value = true
  try {
    if (aClassFeatures.value.length > 1) {
      aClassFeatures.value.splice(index, 1)
      hasChanges.value = true
      debouncedUpdateContent()
    }
  } finally {
    isUpdating.value = false
  }
}

// 更新内容的处理
const updateAClassContent = () => {
  if (!isInitialized.value || isUpdating.value) {
    return
  }

  try {
    isUpdating.value = true
    lastError.value = null

    // 验证数据
    if (!validateData()) {
      return
    }

    // 构建特性列表HTML
    const featuresHtml = aClassFeatures.value.map(feature => `
      <div class="mb-3 d-flex align-items-center">
        <div class="a-class-icon-wrapper me-3">
          <i class="bi bi-check-circle-fill text-primary"></i>
        </div>
        <div class="a-class-feature-text">${feature}</div>
      </div>
    `).join('')
    
    const html = `
      <div data-bs-component="a-class" class="a-class-container">
        <div class="container py-5">
          <div class="row align-items-center">
            <div class="col-md-6">
              <h2 class="mb-4 a-class-title fw-bold">${aClassTitle.value}</h2>
              <p class="mb-4 a-class-subtitle fs-5">${aClassSubtitle.value}</p>
              <div class="mb-4 a-class-features">
                ${featuresHtml}
              </div>
              <div class="a-class-actions">
                <button type="button" class="btn btn-primary btn-lg me-3">${aClassPrimaryButton.value}</button>
                <button type="button" class="btn btn-outline-secondary btn-lg">${aClassSecondaryButton.value}</button>
              </div>
            </div>
            <div class="col-md-6">
              <div class="text-center a-class-image-container">
                <img src="${aClassImageUrl.value}" class="rounded shadow img-fluid" alt="产品展示图">
              </div>
            </div>
          </div>
        </div>
      </div>
    `
    
    emit('update-block', { html })
    hasChanges.value = false
  } catch (error) {
    console.error('更新A类模块内容时出错:', error)
    lastError.value = error as Error
  } finally {
    isUpdating.value = false
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
}

.feature-item {
  margin-bottom: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #f8f9fa;
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.feature-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.add-feature-btn {
  margin-top: 10px;
  width: 100%;
}
</style>