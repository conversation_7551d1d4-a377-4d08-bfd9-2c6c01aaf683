<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <el-form-item label="手风琴项目">
            <div v-for="(item, index) in accordionItems" :key="index" class="accordion-item-edit">
              <div class="accordion-item-header">
                <span>项目 #{{ index + 1 }}</span>
                <div class="accordion-item-actions">
                  <el-button 
                    type="text" 
                    :icon="Edit" 
                    @click="editItem(index)"
                    title="编辑"
                  />
                  <el-button 
                    type="text" 
                    :icon="Delete" 
                    @click="removeItem(index)" 
                    v-if="accordionItems.length > 1"
                    title="删除"
                  />
                </div>
              </div>
              <div v-if="editingIndex === index" class="accordion-item-content">
                <el-form-item label="标题">
                  <el-input v-model="accordionItems[index].title" @input="markAsChanged" />
                </el-form-item>
                <el-form-item label="内容">
                  <el-input 
                    v-model="accordionItems[index].content" 
                    type="textarea" 
                    :rows="3" 
                    @input="markAsChanged"
                  />
                </el-form-item>
                <el-form-item label="是否展开">
                  <el-switch v-model="accordionItems[index].expanded" @change="markAsChanged" />
                </el-form-item>
              </div>
            </div>
            <div class="add-item-button">
              <el-button type="primary" @click="addItem" icon="Plus">添加项目</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <el-form-item label="边框样式">
            <el-radio-group v-model="accordionBorderStyle" @change="markAsChanged">
              <el-radio label="default">标准边框</el-radio>
              <el-radio label="flush">无边框</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="颜色方案">
            <el-select v-model="accordionTheme" @change="markAsChanged" style="width: 100%">
              <el-option label="默认" value="default" />
              <el-option label="主题色" value="primary" />
              <el-option label="成功色" value="success" />
              <el-option label="信息色" value="info" />
              <el-option label="警告色" value="warning" />
              <el-option label="危险色" value="danger" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="项目间距">
            <el-select v-model="gapSize" @change="markAsChanged" style="width: 100%">
              <el-option label="无间距" value="none" />
              <el-option label="小间距 (0.5rem)" value="small" />
              <el-option label="中等间距 (1rem)" value="medium" />
              <el-option label="大间距 (1.5rem)" value="large" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, defineProps, defineEmits, defineOptions, computed } from 'vue'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'AccordionEditor'
})

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 编辑中的手风琴项目索引
const editingIndex = ref(0)

// 是否有未保存的更改
const isChanged = ref(false)

// 手风琴项目
interface AccordionItem {
  title: string
  content: string
  expanded: boolean
  id?: string
  headingId?: string
}

// 手风琴数据和配置
const accordionId = ref('')
const originalHtml = ref('')
const originalStructure = ref<{
  rootElement: HTMLElement | null,
  outerClasses: string[],
  containerClasses: string[],
  rowClasses: string[],
  colClasses: string[],
  customAttributes: Record<string, string>
}>({
  rootElement: null,
  outerClasses: [],
  containerClasses: [],
  rowClasses: [],
  colClasses: [],
  customAttributes: {}
})

// 手风琴数据
const accordionItems = ref<AccordionItem[]>([
  {
    title: '手风琴项目 #1',
    content: '这是第一个手风琴项目的内容。您可以在这里放置任何文本或HTML内容。',
    expanded: true
  }
])

// 样式选项
const accordionBorderStyle = ref('default')
const accordionTheme = ref('default')
const gapSize = ref('none')

/**
 * 重置编辑器状态
 */
const resetEditorState = () => {
  accordionId.value = ''
  originalHtml.value = ''
  originalStructure.value = {
    rootElement: null,
    outerClasses: [],
    containerClasses: [],
    rowClasses: [],
    colClasses: [],
    customAttributes: {}
  }
  accordionItems.value = [{
    title: '手风琴项目 #1',
    content: '这是第一个手风琴项目的内容。您可以在这里放置任何文本或HTML内容。',
    expanded: true
  }]
  accordionBorderStyle.value = 'default'
  accordionTheme.value = 'default'
  gapSize.value = 'none'
  editingIndex.value = 0
  isChanged.value = false
}

/**
 * 初始化编辑器
 */
const initializeEditor = (element: HTMLElement | null) => {
  try {
    if (!element) {
      resetEditorState()
      return
    }

    // 存储原始HTML
    originalHtml.value = element.outerHTML
    
    // 解析结构
    const structureParsed = parseAccordionStructure(element)
    
    // 提取项目内容
    const itemsExtracted = extractAccordionItems(element)
    
    // 如果解析失败，使用默认值
    if (!structureParsed || !itemsExtracted || accordionItems.value.length === 0) {
      accordionItems.value = [{
        title: '手风琴项目 #1',
        content: '这是第一个手风琴项目的内容。您可以在这里放置任何文本或HTML内容。',
        expanded: true
      }]
    }
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('初始化编辑器时出错:', error)
    resetEditorState()
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newElement) => {
  if (newElement) {
    initializeEditor(newElement as HTMLElement)
  } else {
    resetEditorState()
  }
}, { deep: true, immediate: true })

// 解析HTML结构，提取原始结构信息
const parseAccordionStructure = (element: HTMLElement) => {
  try {
    // 存储原始元素用于参考
    originalStructure.value.rootElement = element
    
    // 获取根元素的类名和属性
    originalStructure.value.outerClasses = Array.from(element.classList)
    
    // 提取ID
    const id = element.id || `accordion-${Date.now()}`
    accordionId.value = id
    
    // 提取自定义属性
    const customAttributes: Record<string, string> = {}
    Array.from(element.attributes).forEach(attr => {
      if (attr.name !== 'class' && attr.name !== 'id' && attr.name !== 'style') {
        customAttributes[attr.name] = attr.value
      }
    })
    originalStructure.value.customAttributes = customAttributes
    
    // 查找容器结构
    const container = element.querySelector('.container-fluid, .container')
    if (container) {
      originalStructure.value.containerClasses = Array.from(container.classList)
      
      const row = container.querySelector('.row')
      if (row) {
        originalStructure.value.rowClasses = Array.from(row.classList)
        
        const col = row.querySelector('[class*="col-"]')
        if (col) {
          originalStructure.value.colClasses = Array.from(col.classList)
        }
      }
    }
    
    // 提取样式信息
    // 边框样式
    if (element.classList.contains('accordion-flush')) {
      accordionBorderStyle.value = 'flush'
    } else {
      accordionBorderStyle.value = 'default'
    }
    
    // 主题色
    const themeMatches = Array.from(element.classList).find(cls => {
      return cls.match(/text-(primary|secondary|success|info|warning|danger)/)
    })
    
    if (themeMatches) {
      const theme = themeMatches.replace('text-', '')
      if (['primary', 'secondary', 'success', 'info', 'warning', 'danger'].includes(theme)) {
        accordionTheme.value = theme
      }
    } else {
      accordionTheme.value = 'default'
    }
    
    // 间距
    if (element.classList.contains('accordion-gap-small')) {
      gapSize.value = 'small'
    } else if (element.classList.contains('accordion-gap-medium')) {
      gapSize.value = 'medium'
    } else if (element.classList.contains('accordion-gap-large')) {
      gapSize.value = 'large'
    } else {
      gapSize.value = 'none'
    }
    
    return true
  } catch (error) {
    console.error('解析手风琴结构时出错:', error)
    return false
  }
}

// 提取手风琴项目内容
const extractAccordionItems = (element: HTMLElement) => {
  try {
    // 提取各个手风琴项
    const items = element.querySelectorAll('.accordion-item')
    
    if (items.length > 0) {
      const newItems: AccordionItem[] = []
      
      items.forEach((item, index) => {
        const button = item.querySelector('.accordion-button')
        const collapse = item.querySelector('.accordion-collapse')
        const body = item.querySelector('.accordion-body')
        
        // 获取相关ID
        const headingId = button?.closest('.accordion-header')?.id || `heading-${index}`
        const itemId = collapse?.id || `collapse-${index}`
        
        // 是否展开
        const isExpanded = collapse?.classList.contains('show') || 
                          button?.getAttribute('aria-expanded') === 'true' || 
                          false
        
        newItems.push({
          title: button?.textContent?.trim() || `手风琴项目 #${index + 1}`,
          content: body?.innerHTML?.trim() || '这是手风琴项目的内容。',
          expanded: isExpanded,
          id: itemId,
          headingId: headingId
        })
      })
      
      if (newItems.length > 0) {
        accordionItems.value = newItems
      }
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('提取手风琴项目时出错:', error)
    return false
  }
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加手风琴项目
const addItem = () => {
  accordionItems.value.push({
    title: `手风琴项目 #${accordionItems.value.length + 1}`,
    content: '这是新手风琴项目的内容。',
    expanded: false
  })
  editingIndex.value = accordionItems.value.length - 1
  markAsChanged()
}

// 编辑手风琴项目
const editItem = (index: number) => {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 删除手风琴项目
const removeItem = (index: number) => {
  if (accordionItems.value.length > 1) {
    accordionItems.value.splice(index, 1)
    if (editingIndex.value === index) {
      editingIndex.value = -1
    } else if (editingIndex.value > index) {
      editingIndex.value--
    }
    markAsChanged()
  }
}

// 保留原始ID或生成新ID
const getAccordionId = () => {
  return accordionId.value || `accordion-${Date.now()}`
}

/**
 * 准备手风琴HTML，保留原有结构和属性
 */
const prepareAccordionHTML = (): string => {
  // 使用原始ID或生成新ID
  const currentAccordionId = getAccordionId()
  
  // 构建类名列表，确保保留原始类名
  let accordionClasses = ['accordion']
  
  // 过滤掉核心功能类，保留其他自定义类
  if (originalStructure.value.outerClasses.length > 0) {
    originalStructure.value.outerClasses.forEach(cls => {
      if (!['accordion', 'accordion-flush', 'responsive-block'].includes(cls) && 
          !cls.startsWith('accordion-gap-') && 
          !cls.startsWith('text-')) {
        accordionClasses.push(cls)
      }
    })
  }
  
  // 添加响应式块类
  if (!accordionClasses.includes('responsive-block')) {
    accordionClasses.push('responsive-block')
  }
  
  // flush风格
  if (accordionBorderStyle.value === 'flush') {
    accordionClasses.push('accordion-flush')
  }
  
  // 主题色
  if (accordionTheme.value !== 'default') {
    if (["primary","secondary","success","danger","info","warning"].includes(accordionTheme.value)) {
      accordionClasses.push(`text-${accordionTheme.value}`)
    }
  }
  
  // 间距类
  if (gapSize.value !== 'none') {
    accordionClasses.push(`accordion-gap-${gapSize.value}`)
  }
  
  // 计算项目间距样式 - 使用CSS类而不是内联样式
  let marginClass = ''
  if (gapSize.value === 'small') {
    marginClass = 'mb-2' // 0.5rem
  } else if (gapSize.value === 'medium') {
    marginClass = 'mb-3' // 1rem
  } else if (gapSize.value === 'large') {
    marginClass = 'mb-4' // 1.5rem
  }

  // 生成自定义属性字符串
  let customAttrsStr = ''
  Object.entries(originalStructure.value.customAttributes).forEach(([key, value]) => {
    // 确保特殊字符被正确转义
    const escapedValue = value.replace(/"/g, '&quot;')
    customAttrsStr += ` ${key}="${escapedValue}"`
  })
  
  // 使用原始容器类或默认类
  const containerClasses = originalStructure.value.containerClasses.length > 0 
    ? originalStructure.value.containerClasses.join(' ')
    : 'container-fluid p-0'
  
  // 使用原始行类或默认类
  const rowClasses = originalStructure.value.rowClasses.length > 0
    ? originalStructure.value.rowClasses.join(' ')
    : 'row justify-content-center'
  
  // 使用原始列类或默认类
  const colClasses = originalStructure.value.colClasses.length > 0
    ? originalStructure.value.colClasses.join(' ')
    : 'col-12 col-md-10 col-lg-8'

  // 生成手风琴项目HTML
  let itemsHtml = ''
  accordionItems.value.forEach((item, idx) => {
    // 使用原有ID或生成新ID
    const itemId = item.id || `${currentAccordionId}-collapse-${idx}`
    const headingId = item.headingId || `${currentAccordionId}-heading-${idx}`
    
    // 添加按钮样式
    const buttonClass = [
      'accordion-button',
      !item.expanded ? 'collapsed' : '',
      accordionTheme.value !== 'default' ? `text-${accordionTheme.value}` : ''
    ].filter(Boolean).join(' ')
    
    // 安全处理HTML内容 - 确保特殊字符处理
    const safeTitle = item.title || '标题'
    const safeContent = item.content || '内容'
    
    // 添加外部CSS类处理间距，避免内联样式
    itemsHtml += `
      <div class="accordion-item ${marginClass}">
        <h2 class="accordion-header" id="${headingId}">
          <button class="${buttonClass}" 
            type="button" 
            data-bs-toggle="collapse" 
            data-bs-target="#${itemId}" 
            aria-expanded="${item.expanded ? 'true' : 'false'}" 
            aria-controls="${itemId}">
            ${safeTitle}
          </button>
        </h2>
        <div id="${itemId}" 
          class="accordion-collapse collapse${item.expanded ? ' show' : ''}" 
          aria-labelledby="${headingId}" 
          data-bs-parent="#${currentAccordionId}">
          <div class="accordion-body">
            ${safeContent}
          </div>
        </div>
      </div>
    `
  })

  // 返回完整手风琴HTML，保留原有结构
  return `
    <div data-bs-component="accordion" class="${accordionClasses.join(' ')}" id="${currentAccordionId}"${customAttrsStr}>
      <div class="${containerClasses}">
        <div class="${rowClasses}">
          <div class="${colClasses}">
            ${itemsHtml}
          </div>
        </div>
      </div>
    </div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareAccordionHTML()
    
    // 发出更新事件，包含完整的HTML结构
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用手风琴更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.accordion-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.accordion-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.accordion-item-actions {
  display: flex;
  gap: 5px;
}

.accordion-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 