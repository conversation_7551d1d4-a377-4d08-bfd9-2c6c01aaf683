<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <el-form-item label="按钮列表" v-if="!isSingleButtonTag">
            <div v-for="(button, index) in buttons" :key="index" class="button-item-edit">
              <div class="button-item-header">
                <span>按钮 #{{ index + 1 }}</span>
                <div class="button-item-actions">
                  <el-button 
                    type="text" 
                    :icon="Edit" 
                    @click="editItem(index)"
                    title="编辑"
                  />
                  <el-button 
                    type="text" 
                    :icon="Delete" 
                    @click="removeItem(index)" 
                    v-if="buttons.length > 1"
                    title="删除"
                  />
                </div>
              </div>
              <div v-if="editingIndex === index" class="button-item-content">
                <el-form-item label="按钮文本">
                  <el-input v-model="buttons[index].text" @input="markAsChanged" />
                </el-form-item>
                <el-form-item label="链接地址">
                  <el-input v-model="buttons[index].href" @input="markAsChanged" placeholder="可选" />
                </el-form-item>
              </div>
            </div>
            <div class="add-item-button" v-if="!isSingleButtonTag">
              <el-button type="primary" @click="addItem" icon="Plus">添加按钮</el-button>
            </div>
          </el-form-item>
          
          <!-- For single button, show simplified edit form -->
          <template v-if="isSingleButtonTag">
            <el-form-item label="按钮文本">
              <el-input v-model="buttons[0].text" @input="markAsChanged" />
            </el-form-item>
            <el-form-item label="链接地址">
              <el-input v-model="buttons[0].href" @input="markAsChanged" placeholder="可选" />
            </el-form-item>
          </template>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <!-- Button background color - always visible -->
          <el-form-item label="Button style">
            <el-select v-model="buttonType" @change="markAsChanged" style="width: 100%">
              <el-option label="Primary" value="primary" />
              <el-option label="Secondary" value="secondary" />
              <el-option label="Success" value="success" />
              <el-option label="Danger" value="danger" />
              <el-option label="Warning" value="warning" />
              <el-option label="Info" value="info" />
              <el-option label="Light" value="light" />
              <el-option label="Dark" value="dark" />
              <el-option label="Link" value="link" />
            </el-select>
          </el-form-item>

          <!-- Text color - always visible -->
          <el-form-item label="Text color">
            <el-color-picker v-model="textColor" @change="markAsChanged" show-alpha />
          </el-form-item>

          <!-- Options only visible for non-single buttons -->
          <template v-if="!isSingleButtonTag">
            <el-form-item label="Button size">
              <el-select v-model="buttonSize" @change="markAsChanged" style="width: 100%">
                <el-option label="Large" value="lg" />
                <el-option label="Medium" value="" />
                <el-option label="Small" value="sm" />
              </el-select>
            </el-form-item>

            <el-form-item label="Gap">
              <el-select v-model="gapSize" @change="markAsChanged" style="width: 100%">
                <el-option label="Small" value="small" />
                <el-option label="Medium" value="medium" />
                <el-option label="Large" value="large" />
              </el-select>
            </el-form-item>

            <el-form-item label="Alignment">
              <el-radio-group v-model="alignment" @change="markAsChanged">
                <el-radio-button label="left">
                  左对齐
                </el-radio-button>
                <el-radio-button label="center">
                  居中对齐
                </el-radio-button>
                <el-radio-button label="right">
                  右对齐
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </template>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, computed, watch } from 'vue'
import { Minus, Edit, Delete, Plus } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'ButtonEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 编辑中的按钮索引
const editingIndex = ref(0)

// 按钮数据结构
interface ButtonItem {
  text: string
  href: string
  classes?: string[]
  customAttributes?: Record<string, string>
}

// 原始HTML
const originalHtml = ref('')

// 按钮列表
const buttons = ref<ButtonItem[]>([
  {
    text: '按钮',
    href: '',
    classes: ['btn', 'btn-primary'],
    customAttributes: {}
  }
])

// 样式属性
const buttonType = ref('primary')
const textColor = ref('') // 新增：文本颜色
const buttonSize = ref('')
const gapSize = ref('medium')
const alignment = ref('left')

// 判断是否为单一按钮标签
const isSingleButtonTag = computed(() => {
  return buttonStructure.value.type === ButtonType.DIRECT && (
    props.blockElement?.tagName === 'BUTTON' ||
    (props.blockElement?.tagName === 'A' && props.blockElement?.classList.contains('btn'))
  )
})

// 按钮类型
enum ButtonType {
  // 独立按钮 - 直接的按钮元素
  DIRECT = 'direct',
  // 容器内的按钮 - 在标准结构内的按钮
  CONTAINED = 'contained',
  // 未知类型
  UNKNOWN = 'unknown'
}

// 原始结构信息
const buttonStructure = ref({
  type: ButtonType.UNKNOWN,
  containerClasses: [] as string[],
  rowClasses: [] as string[],
  colClasses: [] as string[],
  customAttributes: {} as Record<string, string>
})

/**
 * 从元素中提取所有属性
 */
const extractAttributes = (element: Element): Record<string, string> => {
  const attributes: Record<string, string> = {}
  
  Array.from(element.attributes).forEach(attr => {
    if (attr.name !== 'class' && attr.name !== 'style' && !attr.name.startsWith('data-v-')) {
      attributes[attr.name] = attr.value
    }
  })
  
  return attributes
}

/**
 * 从类名列表中提取按钮样式
 */
const extractButtonStyle = (classList: string[]) => {
  // 提取按钮类型
  const typeClass = classList.find(cls => cls.startsWith('btn-'))
  if (typeClass) {
    if (typeClass.startsWith('btn-outline-')) {
      buttonType.value = typeClass.replace('btn-outline-', '')
    } else {
      buttonType.value = typeClass.replace('btn-', '')
    }
  }
  
  // 提取按钮大小
  if (classList.includes('btn-lg')) {
    buttonSize.value = 'lg'
  } else if (classList.includes('btn-sm')) {
    buttonSize.value = 'sm'
  } else {
    buttonSize.value = ''
  }
}

/**
 * 从元素提取对齐方式
 */
const extractAlignment = (element: Element) => {
  if (element.classList.contains('text-center') || 
      element.classList.contains('justify-content-center') ||
      (element as HTMLElement).style?.textAlign === 'center') {
    alignment.value = 'center'
  } else if (element.classList.contains('text-right') || 
             element.classList.contains('justify-content-end') ||
             (element as HTMLElement).style?.textAlign === 'right') {
    alignment.value = 'right'
  } else {
    alignment.value = 'left'
  }
}

/**
 * 解析按钮文本颜色
 */
const extractButtonTextColor = (element: HTMLElement) => {
  // 尝试获取计算样式
  const computedStyle = window.getComputedStyle(element)
  const color = computedStyle.color
  
  if (color && color !== 'rgb(0, 0, 0)' && color !== '#000000') {
    textColor.value = color
  }
}

/**
 * 提取按钮及其属性
 */
const extractButtonElements = () => {
  if (!props.blockElement) return false
  
  // 保存原始HTML
  originalHtml.value = props.blockElement.outerHTML
  
  try {
    // 1. 首先检查是否是 div[data-bs-component="bootstrap-button"] 包裹的按钮
    const isWrappedButton = 
      props.blockElement.tagName === 'DIV' && 
      props.blockElement.getAttribute('data-bs-component') === 'bootstrap-button'

    // 2. 检查是否是直接的按钮元素
    const isDirectButton = 
      props.blockElement.tagName === 'BUTTON' || 
      (props.blockElement.tagName === 'A' && 
      props.blockElement.classList.contains('btn'))
    
    // 3. 检查是否是带有按钮的完整结构
    const containerQuery = props.blockElement.querySelector('.container, .container-fluid')
    const buttonQuery = props.blockElement.querySelector('button, a.btn, [class*="btn-"]')
    
    if (isWrappedButton) {
      // 处理被 div 包裹的按钮情况
      buttonStructure.value.type = ButtonType.DIRECT
      buttonStructure.value.customAttributes = extractAttributes(props.blockElement)
      
      // 获取内部的按钮元素
      const buttonEl = props.blockElement.querySelector('button, a.btn')
      if (buttonEl) {
        buttons.value = [{
          text: buttonEl.textContent?.trim() || '按钮',
          href: buttonEl instanceof HTMLAnchorElement ? buttonEl.href : '',
          classes: Array.from(buttonEl.classList),
          customAttributes: extractAttributes(buttonEl)
        }]
        
        // 提取按钮样式
        const classList = Array.from(buttonEl.classList)
        extractButtonStyle(classList)
        
        // 提取文本颜色
        extractButtonTextColor(buttonEl as HTMLElement)
        
        // 提取对齐方式
        extractAlignment(props.blockElement)
        
        return true
      }
    } else if (isDirectButton) {
      // 保持原有的直接按钮处理逻辑
      buttonStructure.value.type = ButtonType.DIRECT
      buttonStructure.value.customAttributes = extractAttributes(props.blockElement)
      
      const buttonEl = props.blockElement
      buttons.value = [{
        text: buttonEl.textContent?.trim() || '按钮',
        href: buttonEl instanceof HTMLAnchorElement ? buttonEl.href : '',
        classes: Array.from(buttonEl.classList),
        customAttributes: extractAttributes(buttonEl)
      }]
      
      // 提取按钮样式
      const classList = Array.from(buttonEl.classList)
      extractButtonStyle(classList)
      
      // 提取文本颜色
      extractButtonTextColor(buttonEl)
      
      // 如果有父元素，记录对齐方式
      if (buttonEl.parentElement) {
        extractAlignment(buttonEl.parentElement)
      }
      
      return true
    } else if (containerQuery && buttonQuery) {
      // 保持原有的完整结构处理逻辑
      buttonStructure.value.type = ButtonType.CONTAINED
      
      // 提取容器结构
      const container = containerQuery
      buttonStructure.value.containerClasses = Array.from(container.classList)
      
      const row = container.querySelector('.row')
      if (row) {
        buttonStructure.value.rowClasses = Array.from(row.classList)
        
        const col = row.querySelector('[class*="col-"]')
        if (col) {
          buttonStructure.value.colClasses = Array.from(col.classList)
          
          // 提取对齐方式
          extractAlignment(col)
        }
      }
      
      // 收集所有按钮
      const buttonElements = props.blockElement.querySelectorAll('button, a.btn, [class*="btn-"]')
      if (buttonElements.length > 0) {
        const newButtons: ButtonItem[] = []
        
        buttonElements.forEach((buttonEl) => {
          newButtons.push({
            text: buttonEl.textContent?.trim() || '按钮',
            href: buttonEl instanceof HTMLAnchorElement ? buttonEl.href : '',
            classes: Array.from(buttonEl.classList),
            customAttributes: extractAttributes(buttonEl)
          })
          
          // 提取第一个按钮的文本颜色
          if (newButtons.length === 1) {
            extractButtonTextColor(buttonEl as HTMLElement)
          }
        })
        
        if (newButtons.length > 0) {
          buttons.value = newButtons
          
          // 提取第一个按钮的样式
          extractButtonStyle(newButtons[0].classes || [])
        }
      }
      
      return true
    } else if (buttonQuery) {
      // 保持原有的单个按钮处理逻辑
      buttonStructure.value.type = ButtonType.DIRECT
      buttonStructure.value.customAttributes = extractAttributes(props.blockElement)
      
      const buttonEl = buttonQuery
      buttons.value = [{
        text: buttonEl.textContent?.trim() || '按钮',
        href: buttonEl instanceof HTMLAnchorElement ? buttonEl.href : '',
        classes: Array.from(buttonEl.classList),
        customAttributes: extractAttributes(buttonEl)
      }]
      
      // 提取按钮样式
      const classList = Array.from(buttonEl.classList)
      extractButtonStyle(classList)
      
      // 提取文本颜色
      extractButtonTextColor(buttonEl as HTMLElement)
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('提取按钮元素时出错:', error)
    return false
  }
}

// 组件挂载时，提取按钮数据
onMounted(() => {
  const extracted = extractButtonElements()
  
  if (!extracted || buttons.value.length === 0) {
    // 如果无法提取或没有找到按钮，使用默认值
    buttons.value = [{
      text: '按钮',
      href: '',
      classes: ['btn', 'btn-primary'],
      customAttributes: {}
    }]
  }
  
  // 初始化时标记为未更改
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加按钮
const addItem = () => {
  buttons.value.push({
    text: `按钮 ${buttons.value.length + 1}`,
    href: '',
    classes: ['btn', `btn-${buttonType.value}`],
    customAttributes: {}
  })
  editingIndex.value = buttons.value.length - 1
  markAsChanged()
}

// 编辑按钮
const editItem = (index: number) => {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 删除按钮
const removeItem = (index: number) => {
  if (buttons.value.length > 1) {
    buttons.value.splice(index, 1)
    if (editingIndex.value === index) {
      editingIndex.value = -1
    } else if (editingIndex.value > index) {
      editingIndex.value--
    }
    markAsChanged()
  }
}

/**
 * 保留原始类名，但更新样式相关的类
 */
const getUpdatedClasses = (originalClasses: string[], buttonType: string, buttonSize: string) => {
  // 过滤掉样式相关的类
  const filteredClasses = originalClasses.filter(cls => 
    !cls.startsWith('btn-') && 
    cls !== 'btn-sm' && 
    cls !== 'btn-lg'
  )
  
  // 添加新的样式类
  const styleClass = `btn-${buttonType}`
  const sizeClass = buttonSize ? `btn-${buttonSize}` : ''
  
  // 确保有btn基础类
  if (!filteredClasses.includes('btn')) {
    filteredClasses.push('btn')
  }
  
  // 添加样式类
  filteredClasses.push(styleClass)
  
  // 添加大小类
  if (sizeClass) {
    filteredClasses.push(sizeClass)
  }
  
  return filteredClasses
}

/**
 * 生成单个按钮HTML，增加对包装div的支持
 */
const generateSingleButtonHtml = (button: ButtonItem): string => {
  // 更新类名，保留非样式相关的类
  const updatedClasses = getUpdatedClasses(
    button.classes || ['btn'], 
    buttonType.value, 
    buttonSize.value
  )
  
  // 生成自定义属性字符串
  let attributesStr = ''
  if (button.customAttributes) {
    Object.entries(button.customAttributes).forEach(([key, value]) => {
      if (key !== 'class' && key !== 'style') {
        attributesStr += ` ${key}="${value}"`
      }
    })
  }
  
  // 生成样式字符串，包含文本颜色
  const styleStr = textColor.value ? ` style="color: ${textColor.value};"` : ''
  
  // 生成按钮HTML
  let buttonHtml = ''
  if (button.href) {
    buttonHtml = `<a href="${button.href}" class="${updatedClasses.join(' ')}" role="button"${attributesStr}${styleStr}>${button.text}</a>`
  } else {
    buttonHtml = `<button type="button" class="${updatedClasses.join(' ')}"${attributesStr}${styleStr}>${button.text}</button>`
  }

  // 如果原始结构是 div 包裹的，保持这个结构
  if (props.blockElement?.tagName === 'DIV' && props.blockElement?.getAttribute('data-bs-component') === 'bootstrap-button') {
    return `<div data-bs-component="bootstrap-button">${buttonHtml}</div>`
  }
  
  return buttonHtml
}

/**
 * 生成按钮容器HTML
 */
const generateButtonContainerHtml = (): string => {
  // 使用原始容器类或默认类
  const containerClasses = buttonStructure.value.containerClasses.length > 0 
    ? buttonStructure.value.containerClasses.join(' ')
    : 'container-fluid p-0'
  
  // 使用原始行类或默认类，但移除对齐相关的类
  const rowClasses = buttonStructure.value.rowClasses.length > 0
    ? buttonStructure.value.rowClasses.join(' ').replace(/justify-content-(start|center|end|between|around|evenly)/, '')
    : 'row'
  
  // 添加对齐类
  const justifyClass = alignment.value === 'center' ? 'justify-content-center' : 
                      alignment.value === 'right' ? 'justify-content-end' : 
                      'justify-content-start'
  
  const alignedRowClasses = `${rowClasses} ${justifyClass}`.trim()
  
  // 使用原始列类或默认类，但移除文本对齐相关的类
  const colClasses = buttonStructure.value.colClasses.length > 0
    ? buttonStructure.value.colClasses.join(' ').replace(/text-(left|center|right)/, '')
    : 'col-12 col-md-10 col-lg-8'
  
  // 添加文本对齐类
  const textAlignClass = `text-${alignment.value}`
  const alignedColClasses = `${colClasses} ${textAlignClass}`.trim()
  
  // 生成按钮组类，根据间隔大小设置
  const gapClass = gapSize.value === 'large' ? 'gap-3' : 
                   gapSize.value === 'medium' ? 'gap-2' :
                   'gap-1'
  
  // 为按钮组添加适当的类
  const buttonGroupClass = `d-flex flex-wrap ${gapClass}`
  
  // 生成按钮HTML
  const buttonsHtml = buttons.value.map(button => generateSingleButtonHtml(button)).join('\n')
  
  // 生成自定义属性字符串
  let attributesStr = ''
  Object.entries(buttonStructure.value.customAttributes).forEach(([key, value]) => {
    if (key !== 'class' && key !== 'style') {
      attributesStr += ` ${key}="${value}"`
    }
  })
  
  // 返回完整的按钮容器HTML，确保多个按钮被包装在按钮组容器中
  return `
    <div data-bs-component="button" class="button-block responsive-block"${attributesStr}>
      <div class="${containerClasses}">
        <div class="${alignedRowClasses}">
          <div class="${alignedColClasses}">
            <div class="${buttonGroupClass}">
              ${buttonsHtml}
            </div>
          </div>
        </div>
      </div>
    </div>
  `.trim()
}

/**
 * 准备最终按钮HTML
 */
const prepareButtonHTML = (): string => {
  try {
    // 判断是否有多个按钮
    // 如果用户添加了多个按钮，无论原始元素是什么，都应该使用容器布局
    if (buttons.value.length > 1) {
      return generateButtonContainerHtml();
    }
    
    // 先判断是否传入的是单个按钮元素 (无包装容器)
    const isDirect = props.blockElement && (
      props.blockElement.tagName === 'BUTTON' || 
      (props.blockElement.tagName === 'A' && props.blockElement.classList.contains('btn')) ||
      (props.blockElement.hasAttribute('data-bs-component') && 
       props.blockElement.getAttribute('data-bs-component') === 'button' && 
       !props.blockElement.querySelector('.container, .container-fluid'))
    );
    
    // 对于单个按钮的情况才使用单个按钮输出
    if (isDirect && buttons.value.length === 1) {
      // 判断是否为单个按钮标签
      const isSingleButtonTag = props.blockElement && (
        props.blockElement.tagName === 'BUTTON' || 
        (props.blockElement.tagName === 'A' && props.blockElement.classList.contains('btn'))
      );
      
      // 使用第一个按钮的数据
      const button = buttons.value[0];
      
      // 保留所有原始类，但更新样式类
      let classes: string[] = [];
      if (props.blockElement) {
        classes = Array.from(props.blockElement.classList)
          .filter((cls: string) => 
            !cls.startsWith('btn-') && 
            cls !== 'btn-sm' && 
            cls !== 'btn-lg'
          );
      }
      
      // 确保有btn基础类
      if (!classes.includes('btn')) {
        classes.push('btn');
      }
      
      // 添加样式类
      classes.push(`btn-${buttonType.value}`);
      
      // 添加大小类
      if (buttonSize.value) {
        classes.push(`btn-${buttonSize.value}`);
      }
      
      // 保留原有属性
      const originalAttributes: Record<string, string> = {};
      if (props.blockElement) {
        Array.from(props.blockElement.attributes).forEach(attr => {
          if (attr.name !== 'class' && attr.name !== 'style' && !attr.name.startsWith('data-v-')) {
            originalAttributes[attr.name] = attr.value;
          }
        });
      }
      
      // 确保有组件标识
      if (!('data-bs-component' in originalAttributes)) {
        originalAttributes['data-bs-component'] = 'button';
      }
      
      // 生成属性字符串
      let attributesStr = '';
      Object.entries(originalAttributes).forEach(([key, value]) => {
        attributesStr += ` ${key}="${value}"`;
      });
      
      // 生成样式字符串，包含文本颜色
      const styleStr = textColor.value ? ` style="color: ${textColor.value};"` : ''
      
      // 生成按钮HTML，保持为单个按钮
      if (isSingleButtonTag && props.blockElement?.tagName === 'A') {
        return `<a href="${button.href || '#'}" class="${classes.join(' ')}" role="button"${attributesStr}${styleStr}>${button.text}</a>`;
      } else {
        return `<button type="button" class="${classes.join(' ')}"${attributesStr}${styleStr}>${button.text}</button>`;
      }
    }
    
    // 对于其他所有情况，使用容器布局
    return generateButtonContainerHtml();
  } catch (error) {
    console.error('准备按钮HTML时出错:', error);
    // 返回空字符串，避免崩溃
    return '';
  }
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareButtonHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用按钮更改时出错:', error)
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置编辑状态
    editingIndex.value = 0
    isChanged.value = false
    
    // 重新提取按钮数据
    const extracted = extractButtonElements()
    
    if (!extracted || buttons.value.length === 0) {
      // 如果无法提取或没有找到按钮，使用默认值
      buttons.value = [{
        text: '按钮',
        href: '',
        classes: ['btn', 'btn-primary'],
        customAttributes: {}
      }]
    }
  }
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.button-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.button-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.button-item-actions {
  display: flex;
  gap: 5px;
}

.button-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 