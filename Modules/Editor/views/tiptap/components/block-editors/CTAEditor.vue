<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <div class="editor-section">
          <div class="form-item">
            <label>标题</label>
            <el-input 
              v-model="title" 
              placeholder="请输入标题"
              @change="markAsChanged"
            />
          </div>

          <div class="form-item">
            <label>内容 (可选)</label>
            <el-input 
              v-model="content" 
              type="textarea" 
              :rows="3"
              placeholder="请输入内容描述"
              @change="markAsChanged"
            />
          </div>

          <div class="form-item">
            <label>按钮文本</label>
            <el-input 
              v-model="buttonLabel" 
              placeholder="请输入按钮文本"
              @change="markAsChanged"
            />
          </div>

          <div class="form-item">
            <label>按钮链接</label>
            <el-input 
              v-model="buttonUrl" 
              placeholder="请输入按钮链接"
              @change="markAsChanged"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="样式" name="style">
        <div class="editor-section">
          <div class="form-item">
            <label>背景颜色</label>
            <el-select 
              v-model="bgColor" 
              placeholder="请选择背景颜色"
              @change="markAsChanged"
              style="width: 100%"
            >
              <el-option label="主要 (蓝色)" value="primary" />
              <el-option label="次要 (灰色)" value="secondary" />
              <el-option label="成功 (绿色)" value="success" />
              <el-option label="危险 (红色)" value="danger" />
              <el-option label="警告 (黄色)" value="warning" />
              <el-option label="信息 (青色)" value="info" />
              <el-option label="亮色 (白色)" value="light" />
              <el-option label="暗色 (深灰)" value="dark" />
            </el-select>
          </div>

          <div class="form-item">
            <label>按钮样式</label>
            <el-select 
              v-model="buttonType" 
              placeholder="请选择按钮样式"
              @change="markAsChanged"
              style="width: 100%"
            >
              <el-option label="主要 (蓝色)" value="primary" />
              <el-option label="次要 (灰色)" value="secondary" />
              <el-option label="成功 (绿色)" value="success" />
              <el-option label="危险 (红色)" value="danger" />
              <el-option label="警告 (黄色)" value="warning" />
              <el-option label="信息 (青色)" value="info" />
              <el-option label="亮色 (白色)" value="light" />
              <el-option label="暗色 (深灰)" value="dark" />
              <el-option label="链接 (无背景)" value="link" />
            </el-select>
          </div>

          <div class="form-item">
            <label>对齐方式</label>
            <el-radio-group v-model="textAlign" @change="markAsChanged">
              <el-radio-button label="left">
                左对齐
              </el-radio-button>
              <el-radio-button label="center">
                居中对齐
              </el-radio-button>
              <el-radio-button label="right">
                右对齐
              </el-radio-button>
            </el-radio-group>
          </div>

          <div class="form-item">
            <label>按钮形状</label>
            <div class="button-shape-options">
              <el-radio-group v-model="buttonShape" @change="markAsChanged">
                <el-radio label="default">方形</el-radio>
                <el-radio label="rounded">圆角</el-radio>
                <el-radio label="pill">胶囊</el-radio>
              </el-radio-group>
            </div>
          </div>
          
          <div class="form-item">
            <label>上边距</label>
            <el-input-number 
              v-model="paddingTop" 
              :min="0" 
              :max="10"
              @change="markAsChanged"
            />
          </div>
          <div class="form-item">
            <label>下边距</label>
            <el-input-number 
              v-model="paddingBottom" 
              :min="0" 
              :max="10"
              @change="markAsChanged"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges" size="small">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定义组件名称
defineOptions({
  name: 'CTAEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  }
})

const emit = defineEmits(['update-block'])

// 状态
const activeTab = ref('content')
const title = ref('立即行动，抓住机会！')
const content = ref('')
const buttonLabel = ref('立即联系')
const buttonUrl = ref('#')
const buttonType = ref('light')
const bgColor = ref('primary')
const textAlign = ref('center')
const buttonShape = ref('pill')
const paddingTop = ref(5)
const paddingBottom = ref(5)
const isChanged = ref(false)

// 原始结构信息
const originalStructure = ref({
  containerClasses: [] as string[],
  rowClasses: [] as string[],
  colClasses: [] as string[],
  customAttributes: {} as Record<string, string>
})

// 原始HTML
const originalHtml = ref('')

// 枚举CTA类型
enum CTAType {
  // 标准CTA - 带有容器的完整结构
  STANDARD = 'standard',
  // 简单CTA - 只有div+标题+按钮的简化结构
  SIMPLE = 'simple',
  // 未知类型
  UNKNOWN = 'unknown'
}

// CTA结构类型
const ctaStructure = ref({
  type: CTAType.UNKNOWN,
})

/**
 * 从元素中提取所有属性
 */
const extractAttributes = (element: Element): Record<string, string> => {
  const attributes: Record<string, string> = {}
  
  Array.from(element.attributes).forEach(attr => {
    if (attr.name !== 'class' && attr.name !== 'style' && !attr.name.startsWith('data-v-')) {
      attributes[attr.name] = attr.value
    }
  })
  
  return attributes
}

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 设置文本对齐并标记为已更改
const setTextAlign = (align: string) => {
  if (textAlign.value === align) return
  textAlign.value = align
  markAsChanged()
}

/**
 * 设置默认值
 */
const setDefaultValues = () => {
  title.value = '立即行动，抓住机会！'
  content.value = ''
  buttonLabel.value = '立即联系'
  buttonUrl.value = '#'
  buttonType.value = 'light'
  bgColor.value = 'primary'
  textAlign.value = 'center'
  buttonShape.value = 'pill'
  paddingTop.value = 5
  paddingBottom.value = 5
}

/**
 * 从DOM元素加载数据
 */
const loadDataFromElement = (element: HTMLElement): boolean => {
  try {
    if (!element) {
      console.warn('未提供有效的块元素，使用默认值')
      return false
    }
    
    // 保存原始HTML以备参考
    originalHtml.value = element.outerHTML
    
    // 判断CTA类型
    // 1. 检查是否是标准CTA组件
    const isCTAComponent = element.getAttribute('data-bs-component') === 'bootstrap-cta'
    const containerQuery = element.querySelector('.container, .container-fluid')
    
    if (isCTAComponent && containerQuery) {
      // 标准CTA结构
      ctaStructure.value.type = CTAType.STANDARD
      
      // 提取容器结构
      const container = containerQuery
      originalStructure.value.containerClasses = Array.from(container.classList)
      
      // 提取行和列结构
      const row = container.querySelector('.row')
      if (row) {
        originalStructure.value.rowClasses = Array.from(row.classList)
        
        const col = row.querySelector('[class*="col-"]')
        if (col) {
          originalStructure.value.colClasses = Array.from(col.classList)
        }
      }
      
      // 提取自定义属性
      originalStructure.value.customAttributes = extractAttributes(element)
    } else {
      // 简单CTA结构
      ctaStructure.value.type = CTAType.SIMPLE
      originalStructure.value.customAttributes = extractAttributes(element)
    }
    
    // 获取标题
    const titleElement = element.querySelector('h2')
    if (titleElement) {
      title.value = titleElement.textContent?.trim() || '立即行动，抓住机会！'
    }

    // 获取内容
    const contentElement = element.querySelector('.container > p')
    if (contentElement) {
      content.value = contentElement.textContent?.trim() || ''
    }

    // 获取按钮文本和属性
    const buttonElement = element.querySelector('button')
    if (buttonElement) {
      buttonLabel.value = buttonElement.textContent?.trim() || '立即联系'
      
      // 获取按钮类型
      const btnClasses = Array.from(buttonElement.classList)
      for (const btnClass of btnClasses) {
        if (btnClass.startsWith('btn-')) {
          buttonType.value = btnClass.replace('btn-', '')
          break
        }
      }
      
      // 获取按钮形状
      if (buttonElement.classList.contains('rounded-pill')) {
        buttonShape.value = 'pill'
      } else if (buttonElement.classList.contains('rounded')) {
        buttonShape.value = 'rounded'
      } else {
        buttonShape.value = 'default'
      }
      
      // 获取按钮链接
      const btnUrl = buttonElement.getAttribute('data-button-url')
      if (btnUrl) {
        buttonUrl.value = btnUrl
      }
    }

    // 获取背景颜色
    const ctaElement = element.matches('[data-bs-component="bootstrap-cta"]') ? element : element.querySelector('[data-bs-component="bootstrap-cta"]')
    if (ctaElement) {
      const elementClasses = Array.from(ctaElement.classList)
      for (const cls of elementClasses) {
        if (cls.startsWith('bg-')) {
          bgColor.value = cls.replace('bg-', '')
          break
        }
      }
      
      // 获取边距
      const pyTopClass = elementClasses.find(cls => cls.startsWith('pt-') || cls.startsWith('py-'))
      if (pyTopClass) {
        const value = parseInt(pyTopClass.replace(/p[ty]-/, ''))
        if (!isNaN(value)) {
          paddingTop.value = value
        }
      }
      
      const pyBottomClass = elementClasses.find(cls => cls.startsWith('pb-') || cls.startsWith('py-'))
      if (pyBottomClass) {
        const value = parseInt(pyBottomClass.replace(/p[by]-/, ''))
        if (!isNaN(value)) {
          paddingBottom.value = value
        }
      }
    }

    // 获取对齐方式
    const containerElement = element.querySelector('.container')
    if (containerElement) {
      if (containerElement.classList.contains('text-left')) {
        textAlign.value = 'left'
      } else if (containerElement.classList.contains('text-right')) {
        textAlign.value = 'right'
      } else {
        // 默认为居中
        textAlign.value = 'center'
      }
    }
    
    console.log('CTA元素数据加载成功', {
      title: title.value,
      content: content.value,
      buttonLabel: buttonLabel.value,
      buttonUrl: buttonUrl.value,
      buttonType: buttonType.value,
      bgColor: bgColor.value,
      textAlign: textAlign.value,
      structure: ctaStructure.value.type
    })

    return true
  } catch (error) {
    console.error('加载CTA元素数据时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    
    // 重新加载数据
    const loaded = loadDataFromElement(newValue)
    
    // 如果加载失败，使用默认值
    if (!loaded) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的初始化逻辑，因为已经由 watch 处理
  isChanged.value = false
})

/**
 * 准备最终CTA HTML
 */
const prepareCTAHTML = (): string => {
  try {
    // 如果没有原始元素或原始HTML，直接生成新的HTML
    if (!props.blockElement || !originalHtml.value) {
      // 根据按钮形状设置类名
      const buttonShapeClass = buttonShape.value === 'pill' 
        ? 'rounded-pill' 
        : buttonShape.value === 'rounded' 
          ? 'rounded' 
          : ''

      // 设置文本对齐类名（center是默认值，不需要额外类名）
      const textAlignClass = textAlign.value !== 'center' ? `text-${textAlign.value}` : ''
      
      // 创建标准CTA结构
      return `
<div data-bs-component="bootstrap-cta" class="py-${paddingTop.value} py-${paddingBottom.value} text-white bg-${bgColor.value}">
  <div class="container ${textAlignClass}">
    <h2 class="mb-4 display-6 fw-bold">${title.value}</h2>
    ${content.value ? `<p class="mb-4">${content.value}</p>` : ''}
    <button type="button" class="px-4 py-2 bootstrap-button btn btn-${buttonType.value} ${buttonShapeClass}" data-button-url="${buttonUrl.value}">${buttonLabel.value}</button>
  </div>
</div>
      `.trim()
    }
    
    // 保留原始结构但更新内容
    // 创建一个临时的DOM元素来解析原始HTML
    const tempElement = document.createElement('div')
    tempElement.innerHTML = originalHtml.value
    const ctaElement = tempElement.firstElementChild
    
    if (!ctaElement) {
      return generateDefaultCTA()
    }
    
    // 确保有data-bs-component="cta"属性
    ctaElement.setAttribute('data-bs-component', 'bootstrap-cta')
    
    // 更新按钮形状
    const buttonShapeClass = buttonShape.value === 'pill' 
      ? 'rounded-pill' 
      : buttonShape.value === 'rounded' 
        ? 'rounded' 
        : ''
    
    // 更新背景色类
    // 移除旧的背景色类
    const bgClasses = Array.from(ctaElement.classList).filter(cls => cls.startsWith('bg-'))
    bgClasses.forEach(cls => ctaElement.classList.remove(cls))
    
    // 添加新的背景色类
    ctaElement.classList.add(`bg-${bgColor.value}`)
    
    // 更新padding类
    // 移除旧的padding类
    const paddingClasses = Array.from(ctaElement.classList).filter(cls => 
      cls.startsWith('py-') || cls.startsWith('pt-') || cls.startsWith('pb-'))
    paddingClasses.forEach(cls => ctaElement.classList.remove(cls))
    
    // 添加新的padding类
    ctaElement.classList.add(`py-${paddingTop.value}`, `py-${paddingBottom.value}`)
    
    // 查找标题元素并更新
    const titleElement = ctaElement.querySelector('h1, h2, h3, h4, h5, h6')
    if (titleElement) {
      titleElement.textContent = title.value
    }
    
    // 查找内容元素并更新
    const contentElement = ctaElement.querySelector('p')
    if (contentElement) {
      if (content.value) {
        contentElement.textContent = content.value
      } else {
        // 如果没有内容，尝试移除段落
        contentElement.parentNode?.removeChild(contentElement)
      }
    } else if (content.value) {
      // 如果有内容但没有段落元素，尝试添加一个
      const container = ctaElement.querySelector('.container')
      if (container) {
        const newContentElement = document.createElement('p')
        newContentElement.className = 'mb-4'
        newContentElement.textContent = content.value
        
        // 找到合适的位置插入内容
        const h2 = container.querySelector('h2')
        if (h2 && h2.nextSibling) {
          container.insertBefore(newContentElement, h2.nextSibling)
        } else if (h2) {
          h2.insertAdjacentElement('afterend', newContentElement)
        } else {
          container.appendChild(newContentElement)
        }
      }
    }
    
    // 查找按钮元素并更新
    const buttonElement = ctaElement.querySelector('button, a.btn') as HTMLElement | null
    if (buttonElement) {
      // 更新按钮文本
      buttonElement.textContent = buttonLabel.value
      
      // 更新按钮URL属性
      buttonElement.setAttribute('data-button-url', buttonUrl.value)
      
      // 如果是a标签，还要更新href
      if (buttonElement.tagName.toLowerCase() === 'a') {
        buttonElement.setAttribute('href', buttonUrl.value)
      }
      
      // 更新按钮样式类
      // 移除旧的样式类
      const btnClasses = Array.from(buttonElement.classList).filter(cls => cls.startsWith('btn-'))
      btnClasses.forEach(cls => buttonElement.classList.remove(cls))
      
      // 移除旧的形状类
      buttonElement.classList.remove('rounded', 'rounded-pill')
      
      // 添加新的样式类
      buttonElement.classList.add(`btn-${buttonType.value}`)
      
      // 添加新的形状类
      if (buttonShapeClass) {
        buttonElement.classList.add(buttonShapeClass)
      }
    }
    
    // 查找并更新文本对齐方式
    const container = ctaElement.querySelector('.container')
    if (container) {
      // 移除旧的文本对齐类
      container.classList.remove('text-left', 'text-center', 'text-right')
      
      // 添加新的文本对齐类
      if (textAlign.value !== 'center') {
        container.classList.add(`text-${textAlign.value}`)
      }
    }
    
    // 返回更新后的HTML
    return tempElement.innerHTML
  } catch (error) {
    console.error('准备CTA HTML时出错:', error)
    // 出错时返回基本HTML结构
    return generateDefaultCTA()
  }
}

/**
 * 生成默认的CTA HTML
 */
const generateDefaultCTA = (): string => {
  // 根据按钮形状设置类名
  const buttonShapeClass = buttonShape.value === 'pill' 
    ? 'rounded-pill' 
    : buttonShape.value === 'rounded' 
      ? 'rounded' 
      : ''

  // 设置文本对齐类名（center是默认值，不需要额外类名）
  const textAlignClass = textAlign.value !== 'center' ? `text-${textAlign.value}` : ''
  
  return `
<div data-bs-component="bootstrap-cta" class="py-${paddingTop.value} py-${paddingBottom.value} text-white bg-${bgColor.value} responsive-block">
  <div class="container ${textAlignClass}">
    <h2 class="mb-4 display-6 fw-bold">${title.value || '立即行动，抓住机会！'}</h2>
    ${content.value ? `<p class="mb-4">${content.value}</p>` : ''}
    <button type="button" class="px-4 py-2 bootstrap-button btn btn-${buttonType.value} ${buttonShapeClass}" data-button-url="${buttonUrl.value || '#'}">${buttonLabel.value || '立即联系'}</button>
  </div>
</div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareCTAHTML()
    
    // 发出更新事件
    emit('update-block', { 
      html,
    })
    
    // 重置更改状态
    isChanged.value = false
    
    ElMessage.success('CTA内容已更新')
  } catch (error) {
    console.error('应用CTA更改时出错:', error)
    ElMessage.error('更新CTA失败')
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 15px;
  position: relative;
  max-width: 100%;
  overflow-x: hidden;
}

.editor-section {
  margin-bottom: 10px;
}

.form-item {
  margin-bottom: 15px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #606266;
  }
}

.align-options {
  display: flex;
  gap: 5px;

  .align-option {
    flex: 1;
    padding: 8px 0;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f5f7fa;
    }

    &.active {
      background-color: #ecf5ff;
      color: #409eff;
      border-color: #b3d8ff;
    }
  }
}

.button-shape-options {
  margin-top: 8px;
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  height: 40px;
  line-height: 40px;
}

.apply-button-container {
  margin-top: 15px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 