<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <el-form-item label="Image or icon">
            <el-select v-model="imageType" @change="markAsChanged" style="width: 100%">
              <el-option label="Icon" value="icon" />
              <el-option label="Image" value="image" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="imageType === 'image'" label="图片">
            <!-- 图片展示区域 -->
            <div class="image-preview-container">
              <div v-if="cardImageUrl" class="image-preview">
                <img :src="cardImageUrl" class="preview-img" />
              </div>
              <div v-else class="image-placeholder">
                <el-icon class="placeholder-icon"><Picture /></el-icon>
                <div class="placeholder-text">暂无图片</div>
              </div>
            </div>

            <!-- 图片操作按钮 -->
            <div class="button-group">
              <el-button type="primary" class="button-no-border" @click="openFileManager">
                <el-icon class="icon"><Upload /></el-icon>
                <span>选择图片</span>
              </el-button>
              <el-button v-if="cardImageUrl" class="delete-btn" @click="confirmDeleteImage">
                <el-icon class="icon"><Delete /></el-icon>
                <span>删除图片</span>
              </el-button>
            </div>
          </el-form-item>

          <el-form-item v-else label="Icon">
            <el-select v-model="selectedIcon" @change="markAsChanged" style="width: 100%">
              <el-option label="User" value="user" />
              <el-option label="Setting" value="setting" />
              <el-option label="Message" value="message" />
            </el-select>
          </el-form-item>

          <el-form-item label="Cards">
            <div v-for="(card, index) in cards" :key="index" class="card-item-edit">
              <div class="card-item-header">
                <span>Content Creation</span>
                <div class="card-item-actions">
                  <el-button 
                    type="text" 
                    :icon="Edit" 
                    @click="editItem(index)"
                    title="编辑"
                  />
                  <el-button 
                    type="text" 
                    :icon="DocumentCopy" 
                    @click="duplicateItem(index)"
                    title="复制"
                  />
                  <el-button 
                    type="text" 
                    :icon="Delete" 
                    @click="removeItem(index)" 
                    v-if="cards.length > 1"
                    title="删除"
                  />
                </div>
              </div>
              <div v-if="editingIndex === index" class="card-item-content">
                <el-form-item label="标题">
                  <el-input v-model="cards[index].title" @input="markAsChanged" />
                </el-form-item>
                <el-form-item label="内容">
                  <el-input
                    v-model="cards[index].content"
                    type="textarea"
                    :rows="3"
                    @input="markAsChanged"
                  />
                </el-form-item>
              </div>
            </div>
            <div class="add-item-button">
              <el-button type="primary" @click="addItem" icon="Plus">添加卡片</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <el-form-item label="Card orientation">
            <el-radio-group v-model="orientation" @change="markAsChanged">
              <el-radio label="column">Column</el-radio>
              <el-radio label="row">Row</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="Card style">
            <el-select v-model="cardStyle" @change="markAsChanged" style="width: 100%">
              <el-option label="Card variant 1" value="variant1" />
              <el-option label="Card variant 2" value="variant2" />
              <el-option label="Card variant 3" value="variant3" />
            </el-select>
          </el-form-item>

          <el-form-item label="Heading style">
            <el-select v-model="headingStyle" @change="markAsChanged" style="width: 100%">
              <el-option label="Heading 1" value="h1" />
              <el-option label="Heading 2" value="h2" />
              <el-option label="Heading 3" value="h3" />
            </el-select>
          </el-form-item>

          <el-form-item label="Horizontal alignment">
            <el-radio-group v-model="alignment" @change="markAsChanged">
              <el-radio-button label="left">
                左对齐
              </el-radio-button>
              <el-radio-button label="center">
                居中对齐
              </el-radio-button>
              <el-radio-button label="right">
                右对齐
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>

    <!-- 文件管理器弹窗 -->
    <DocumentsManager 
      :BaseUrl="baseUrl" 
      :token="token" 
      :isMultiSelect="false" 
      :locale="localeLang"
      @confirmSelection="confirmSelection" 
      ref="documentsManagerRef" 
      v-model="visibleDialog"
      :showUploadButton="false" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, computed, watch } from 'vue'
import { Minus, Edit, Delete, Plus, DocumentCopy, Picture, Upload } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { DocumentsManager } from 'filestudio-bingo'
import { env, getAuthToken } from '/admin/support/helper'

// 为了解决TypeScript类型问题，添加默认导出
defineOptions({
  name: 'CardEditor'
})

const props = defineProps({
  blockElement: {
    type: Object,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 编辑中的卡片索引
const editingIndex = ref(0)

// 卡片数据结构
interface CardItem {
  title: string
  content: string
}

// 卡片列表
const cards = ref<CardItem[]>([
  {
    title: '卡片标题',
    content: '这是卡片内容，您可以在这里添加任何文本描述。'
  }
])

// 样式属性
const imageType = ref('icon')
const selectedIcon = ref('user')
const cardImageUrl = ref('')
const orientation = ref('column')
const cardStyle = ref('variant1')
const headingStyle = ref('h3')
const alignment = ref('left')

// 文件管理器相关
const visibleDialog = ref(false)
const documentsManagerRef = ref(null)
let tokens: string | null = getAuthToken()
const token = ref<string>(tokens ?? '')
const baseUrl = ref<string>(env('VITE_BASE_URL').replace('/admin/', '/'))
const localeLang = computed(() => localStorage.getItem('bwms_language') || 'zh_CN')

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 打开文件管理器
const openFileManager = () => {
  visibleDialog.value = true
}

// 确认删除图片
const confirmDeleteImage = () => {
  ElMessageBox.confirm(
    '确定要删除当前图片吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    cardImageUrl.value = ''
    markAsChanged()
    ElMessage.success('图片已删除')
  }).catch(() => {
    // 取消删除，不做任何操作
  })
}

// 文件选择确认
const confirmSelection = (selectedFiles: any[]) => {
  if (!selectedFiles || selectedFiles.length === 0) return
  
  const file = selectedFiles[0]
  cardImageUrl.value = file.path || file.url
  markAsChanged()
}

// 添加卡片
const addItem = () => {
  cards.value.push({
    title: `卡片标题 ${cards.value.length + 1}`,
    content: '这是卡片内容，您可以在这里添加任何文本描述。'
  })
  editingIndex.value = cards.value.length - 1
  markAsChanged()
}

// 编辑卡片
const editItem = (index: number) => {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 复制卡片
const duplicateItem = (index: number) => {
  const newCard = { ...cards.value[index] }
  cards.value.splice(index + 1, 0, newCard)
  editingIndex.value = index + 1
  markAsChanged()
}

// 删除卡片
const removeItem = (index: number) => {
  if (cards.value.length > 1) {
    cards.value.splice(index, 1)
    if (editingIndex.value === index) {
      editingIndex.value = -1
    } else if (editingIndex.value > index) {
      editingIndex.value--
    }
    markAsChanged()
  }
}

// 获取token
const getToken = () => {
  // 从localStorage或cookie获取token
  const authToken = localStorage.getItem('auth_token') || ''
  token.value = authToken
}

/**
 * 从块元素中提取卡片数据
 */
const extractCardData = () => {
  if (!props.blockElement) return false
  
  try {
    const cardElements = props.blockElement.querySelectorAll('.card')
    
    if (cardElements.length > 0) {
      const newCards: CardItem[] = []
      
      cardElements.forEach((cardEl: Element) => {
        const titleEl = cardEl.querySelector('.card-title')
        const contentEl = cardEl.querySelector('.card-text')
        
        newCards.push({
          title: titleEl?.textContent?.trim() || '卡片标题',
          content: contentEl?.textContent?.trim() || '这是卡片内容'
        })
      })
      
      if (newCards.length > 0) {
        cards.value = newCards
      }
      
      // 提取图片URL
      const imgElement = props.blockElement.querySelector('.card-img-top')
      if (imgElement) {
        cardImageUrl.value = imgElement.getAttribute('src') || ''
        imageType.value = 'image'
      } else {
        // 检查是否有图标
        const iconElement = props.blockElement.querySelector('.card-icon i')
        if (iconElement) {
          const iconClass = iconElement.className
          const iconMatch = iconClass.match(/el-icon-(\w+)/)
          if (iconMatch && iconMatch[1]) {
            selectedIcon.value = iconMatch[1]
            imageType.value = 'icon'
          }
        }
      }
      
      // 提取样式设置
      const containerEl = props.blockElement
      if (containerEl) {
        // 提取方向
        if (containerEl.classList.contains('flex-row')) {
          orientation.value = 'row'
        }
        
        // 提取对齐方式
        if (containerEl.style.textAlign === 'center') {
          alignment.value = 'center'
        } else if (containerEl.style.textAlign === 'right') {
          alignment.value = 'right'
        }
      }
      
      return true
    }
    
    return false
  } catch (error) {
    console.error('提取卡片数据时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置编辑状态
    editingIndex.value = -1
    isChanged.value = false
    
    // 重新提取卡片数据
    const extracted = extractCardData()
    
    if (!extracted) {
      // 如果无法提取或没有找到卡片，使用默认值
      cards.value = [{
        title: '卡片标题',
        content: '这是卡片内容，您可以在这里添加任何文本描述。'
      }]
      imageType.value = 'icon'
      selectedIcon.value = 'user'
      cardImageUrl.value = ''
      orientation.value = 'column'
      cardStyle.value = 'variant1'
      headingStyle.value = 'h3'
      alignment.value = 'left'
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  getToken()
  // 移除原有的提取逻辑，因为已经由 watch 处理
  isChanged.value = false
})

// 准备卡片HTML
const prepareCardHTML = (): string => {
  // 生成所有卡片的HTML
  const cardsHtml = cards.value.map(card => {
    let mediaContent = '';
    
    if (imageType.value === 'image' && cardImageUrl.value) {
      mediaContent = `<img src="${cardImageUrl.value}" class="card-img-top" alt="卡片图片">`;
    } else {
      mediaContent = `<div class="card-icon"><i class="el-icon-${selectedIcon.value}"></i></div>`;
    }
    
    return `
      <div data-bs-component="card" class="card-block responsive-block">
        <div class="p-0 container-fluid">
          <div class="row justify-content-${alignment.value}">
            <div class="col-12 col-md-10 col-lg-8">
              <div class="card">
                ${mediaContent}
                <div class="card-body">
                  <${headingStyle.value} class="card-title">${card.title}</${headingStyle.value}>
                  <p class="card-text">${card.content}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `.trim();
  }).join('\n');
  
  return cardsHtml;
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareCardHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用卡片更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.card-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.card-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.card-item-actions {
  display: flex;
  gap: 5px;
}

.card-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图片预览相关样式 */
.image-preview-container {
  width: 100%;
  height: 200px;
  background-color: #F6F6F6;
  margin-bottom: 18px;
  overflow: hidden;
  border-radius: 4px;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .placeholder-icon {
    font-size: 40px;
    color: #9E9E9E;
    margin-bottom: 10px;
  }
  
  .placeholder-text {
    color: #9E9E9E;
    font-size: 16px;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  
  .delete-btn {
    color: #707070;
    background: #FFFFFF;
    border-radius: 5px;
  }
}
</style> 