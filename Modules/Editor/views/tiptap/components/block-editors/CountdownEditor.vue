<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top" size="small">
          <el-form-item label="倒计时标题">
            <el-input v-model="title" @input="markAsChanged" />
          </el-form-item>
          
          <el-form-item label="结束日期">
            <div class="date-time-selector">
              <el-date-picker
                v-model="endDate"
                type="date"
                placeholder="选择日期"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                @change="markAsChanged"
                class="date-picker"
                size="small"
              />
              <el-time-picker
                v-model="endTime"
                placeholder="选择时间"
                format="HH:mm"
                value-format="HH:mm"
                @change="markAsChanged"
                class="time-picker"
                size="small"
              />
            </div>
            <div class="date-description">
              活动的结束日期或者你希望计时器结束的时间。
            </div>
          </el-form-item>
          
          <el-form-item label="结束消息">
            <el-input
              v-model="message"
              type="textarea"
              :rows="2"
              placeholder="倒计时结束后显示的消息"
              @input="markAsChanged"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top" size="small">
          <el-form-item label="计数器">
            <div class="sub-field-group">
              <div class="sub-field-label">背景</div>
              <el-radio-group v-model="hasFill" @change="markAsChanged" size="small">
                <el-radio :label="true">填充</el-radio>
                <el-radio :label="false">无填充</el-radio>
              </el-radio-group>
            </div>
            
            <div class="sub-field-group" v-if="hasFill">
              <div class="sub-field-label">边框</div>
              <div class="border-radii-wrapper">
                <div class="border-size">
                  <span class="border-label">宽度</span>
                  <el-select v-model="borderSize" @change="markAsChanged" class="border-select" size="small">
                    <el-option label="无边框" value="0px" />
                    <el-option label="1px" value="1px" />
                    <el-option label="2px" value="2px" />
                    <el-option label="3px" value="3px" />
                    <el-option label="4px" value="4px" />
                    <el-option label="5px" value="5px" />
                  </el-select>
                </div>
                <div class="border-radius">
                  <span class="border-label">圆角</span>
                  <el-slider
                    v-model="borderRadius"
                    :min="0"
                    :max="50"
                    @change="markAsChanged"
                    size="small"
                  />
                </div>
              </div>
            </div>
            
            <div class="sub-field-group" v-if="hasFill">
              <div class="sub-field-label">填充颜色</div>
              <div class="color-picker-wrapper">
                <el-input
                  v-model="fillColor"
                  placeholder="#000000"
                  @input="markAsChanged"
                  class="color-input"
                  size="small"
                >
                  <template #prepend>#</template>
                </el-input>
                <div
                  class="color-preview"
                  :style="{ backgroundColor: '#' + fillColor }"
                ></div>
                <el-color-picker 
                  v-model="fillColorPicker" 
                  show-alpha 
                  @change="updateFillColor" 
                  class="color-picker-button"
                  size="small"
                />
              </div>
            </div>
            
            <div class="sub-field-group">
              <div class="sub-field-label">文字颜色</div>
              <div class="color-picker-wrapper">
                <el-input
                  v-model="textColor"
                  placeholder="#000000"
                  @input="markAsChanged"
                  class="color-input"
                  size="small"
                >
                  <template #prepend>#</template>
                </el-input>
                <div
                  class="color-preview"
                  :style="{ backgroundColor: '#' + textColor }"
                ></div>
                <el-color-picker 
                  v-model="textColorPicker" 
                  show-alpha 
                  @change="updateTextColor" 
                  class="color-picker-button"
                  size="small"
                />
              </div>
            </div>
          </el-form-item>
          
          <el-form-item label="计数器标签">
            <div class="sub-field-group">
              <div class="sub-field-label">文字颜色</div>
              <div class="color-picker-wrapper">
                <el-input
                  v-model="labelColor"
                  placeholder="#000000"
                  @input="markAsChanged"
                  class="color-input"
                  size="small"
                >
                  <template #prepend>#</template>
                </el-input>
                <div
                  class="color-preview"
                  :style="{ backgroundColor: '#' + labelColor }"
                ></div>
                <el-color-picker 
                  v-model="labelColorPicker" 
                  show-alpha 
                  @change="updateLabelColor" 
                  class="color-picker-button"
                  size="small"
                />
              </div>
            </div>
          </el-form-item>

          <el-form-item label="对齐方式">
            <el-radio-group v-model="alignment" @change="markAsChanged" size="small">
              <el-radio-button label="left">
                左对齐
              </el-radio-button>
              <el-radio-button label="center">
                居中对齐
              </el-radio-button>
              <el-radio-button label="right">
                右对齐
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges" size="small">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定义组件名称
defineOptions({
  name: 'CountdownEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 内容属性
const title = ref('倒计时')
const endDate = ref('')
const endTime = ref('12:00')
const message = ref('活动即将开始，敬请期待！')

// 样式属性
const hasFill = ref(true)
const borderSize = ref('0px')
const borderRadius = ref(8)
const fillColor = ref('F4F2FF')
const textColor = ref('4F38E0')
const labelColor = ref('09152B')
const alignment = ref('center')

// 颜色选择器的值
const fillColorPicker = ref('#' + fillColor.value)
const textColorPicker = ref('#' + textColor.value)
const labelColorPicker = ref('#' + labelColor.value)

// 更新填充颜色
const updateFillColor = (value: string) => {
  if (value) {
    // 从 '#RRGGBB' 或 '#RRGGBBAA' 格式转换为 'RRGGBB'
    fillColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 更新文字颜色
const updateTextColor = (value: string) => {
  if (value) {
    textColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 更新标签颜色
const updateLabelColor = (value: string) => {
  if (value) {
    labelColor.value = value.substring(1).replace(/[^0-9A-F]/gi, '').slice(0, 6)
    markAsChanged()
  }
}

// 监听文本输入，同步更新颜色选择器
watch(fillColor, (newValue) => {
  fillColorPicker.value = '#' + newValue
})

watch(textColor, (newValue) => {
  textColorPicker.value = '#' + newValue
})

watch(labelColor, (newValue) => {
  labelColorPicker.value = '#' + newValue
})

/**
 * 从块元素中提取倒计时数据
 */
const extractCountdownData = () => {
  if (!props.blockElement) return false
  
  try {
    // 提取标题
    const titleEl = props.blockElement.querySelector('.countdown-title')
    if (titleEl) {
      title.value = titleEl.textContent?.trim() || '倒计时'
    }

    // 提取消息
    const messageEl = props.blockElement.querySelector('.countdown-message')
    if (messageEl) {
      message.value = messageEl.textContent?.trim() || '活动即将开始，敬请期待！'
    }

    // 提取日期时间
    const dateAttr = props.blockElement.getAttribute('data-target-date')
    if (dateAttr) {
      try {
        const date = new Date(dateAttr)
        // 设置日期
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        endDate.value = `${year}/${month}/${day}`

        // 设置时间
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        endTime.value = `${hours}:${minutes}`
      } catch (e) {
        console.error('解析日期时出错:', e)
        setDefaultDate()
      }
    } else {
      setDefaultDate()
    }

    // 提取样式属性
    const counterEl = props.blockElement.querySelector('.countdown-value')
    if (counterEl) {
      // 检查是否有背景色
      const bgColorClass = Array.from(counterEl.classList).find(cls => cls.startsWith('bg-'))
      hasFill.value = !!bgColorClass

      // 获取边框样式
      const borderProperty = window.getComputedStyle(counterEl).borderWidth
      borderSize.value = borderProperty || '0px'

      // 获取圆角样式
      const borderRadiusProperty = window.getComputedStyle(counterEl).borderRadius
      const radiusValue = parseInt(borderRadiusProperty)
      if (!isNaN(radiusValue)) {
        borderRadius.value = radiusValue
      }

      // 获取填充颜色
      const bgColor = window.getComputedStyle(counterEl).backgroundColor
      if (bgColor && bgColor !== 'transparent') {
        const rgbMatch = bgColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
          fillColor.value = `${r}${g}${b}`.toUpperCase()
        }
      }

      // 获取文字颜色
      const textColorValue = window.getComputedStyle(counterEl).color
      if (textColorValue) {
        const rgbMatch = textColorValue.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
          textColor.value = `${r}${g}${b}`.toUpperCase()
        }
      }
    }

    // 获取标签颜色
    const labelEl = props.blockElement.querySelector('.countdown-label')
    if (labelEl) {
      const labelColorValue = window.getComputedStyle(labelEl).color
      if (labelColorValue) {
        const rgbMatch = labelColorValue.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
          labelColor.value = `${r}${g}${b}`.toUpperCase()
        }
      }
    }

    // 获取对齐方式
    const containerEl = props.blockElement.querySelector('.countdown-container')
    if (containerEl) {
      const textAlignValue = window.getComputedStyle(containerEl).textAlign
      if (textAlignValue) {
        if (textAlignValue === 'center' || textAlignValue === 'right') {
          alignment.value = textAlignValue
        } else {
          alignment.value = 'left'
        }
      }
    }

    return true
  } catch (error) {
    console.error('提取倒计时数据时出错:', error)
    return false
  }
}

/**
 * 设置默认日期（当前日期加7天）
 */
const setDefaultDate = () => {
    const defaultDate = new Date()
    defaultDate.setDate(defaultDate.getDate() + 7)
    
    const year = defaultDate.getFullYear()
    const month = String(defaultDate.getMonth() + 1).padStart(2, '0')
    const day = String(defaultDate.getDate()).padStart(2, '0')
    endDate.value = `${year}/${month}/${day}`
  }
  
// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    
    // 重新提取数据
    const extracted = extractCountdownData()
    
    if (!extracted) {
      // 如果无法提取数据，设置默认值
      title.value = '倒计时'
      message.value = '活动即将开始，敬请期待！'
      setDefaultDate()
      endTime.value = '12:00'
      hasFill.value = true
      borderSize.value = '0px'
      borderRadius.value = 8
      fillColor.value = 'F4F2FF'
      textColor.value = '4F38E0'
      labelColor.value = '09152B'
      alignment.value = 'center'
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的提取逻辑，因为已经由 watch 处理
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 准备目标日期
const prepareTargetDate = (): string => {
  if (!endDate.value) {
    ElMessage.warning('请选择结束日期')
    return ''
  }

  try {
    // 使用选择的日期和时间创建目标日期
    const [year, month, day] = endDate.value.split('/').map(Number)
    const [hours, minutes] = endTime.value ? endTime.value.split(':').map(Number) : [0, 0]
    
    // 创建日期对象 (月份要减1，因为JavaScript中月份从0开始)
    const targetDate = new Date(year, month - 1, day, hours, minutes)
    
    // 如果日期无效，抛出错误
    if (isNaN(targetDate.getTime())) {
      throw new Error('无效的日期')
    }
    
    return targetDate.toISOString()
  } catch (e) {
    console.error('创建目标日期时出错:', e)
    ElMessage.error('请输入有效的日期和时间')
    return ''
  }
}

// 准备倒计时HTML
const prepareCountdownHTML = (): string => {
  const targetDate = prepareTargetDate()
  if (!targetDate) return ''
  
  // 构建基本样式
  const counterStyle: string[] = []
  const labelStyle: string[] = []
  
  // 填充样式
  if (hasFill.value) {
    counterStyle.push(`background-color: #${fillColor.value}`)
  } else {
    counterStyle.push('background-color: transparent')
  }
  
  // 边框样式
  if (borderSize.value !== '0px') {
    counterStyle.push(`border: ${borderSize.value} solid #${fillColor.value}`)
  }
  
  // 圆角样式
  counterStyle.push(`border-radius: ${borderRadius.value}px`)
  
  // 文字颜色
  counterStyle.push(`color: #${textColor.value}`)
  
  // 标签文字颜色
  labelStyle.push(`color: #${labelColor.value}`)
  
  // 创建倒计时脚本
  const countdownScript = `
    (function() {
      // 找到当前倒计时组件
      const countdown = document.currentScript.parentElement;
      if (!countdown) return;
      
      // 提取目标日期
      const targetDateStr = countdown.getAttribute('data-target-date');
      const targetDate = targetDateStr ? new Date(targetDateStr) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      
      // 获取显示元素
      const daysEl = countdown.querySelector('.days-item .countdown-value');
      const hoursEl = countdown.querySelector('.hours-item .countdown-value');
      const minutesEl = countdown.querySelector('.minutes-item .countdown-value');
      const secondsEl = countdown.querySelector('.seconds-item .countdown-value');
      
      if (!daysEl || !hoursEl || !minutesEl || !secondsEl) return;
      
      // 更新倒计时函数
      function updateCountdown() {
        const now = new Date();
        const diff = targetDate.getTime() - now.getTime();
        
        if (diff <= 0) {
          // 倒计时结束
          daysEl.textContent = '00';
          hoursEl.textContent = '00';
          minutesEl.textContent = '00';
          secondsEl.textContent = '00';
          return;
        }
        
        // 计算时间差
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        // 格式化显示
        daysEl.textContent = String(days).padStart(2, '0');
        hoursEl.textContent = String(hours).padStart(2, '0');
        minutesEl.textContent = String(minutes).padStart(2, '0');
        secondsEl.textContent = String(seconds).padStart(2, '0');
      }
      
      // 初始更新
      updateCountdown();
      
      // 每秒更新
      const intervalId = setInterval(updateCountdown, 1000);
      
      // 清理函数
      function cleanup() {
        clearInterval(intervalId);
        document.removeEventListener('visibilitychange', visibilityHandler);
      }
      
      // 处理页面可见性变化，优化性能
      function visibilityHandler() {
        if (document.hidden) {
          clearInterval(intervalId);
        } else {
          updateCountdown();
          setInterval(updateCountdown, 1000);
        }
      }
      
      document.addEventListener('visibilitychange', visibilityHandler);
      
      // 页面卸载时清理
      window.addEventListener('beforeunload', cleanup);
    })();
  `
  
  // 构建HTML - 避免使用嵌套模板字符串
  return `
<div data-bs-component="countdown" class="my-4 bootstrap-countdown" data-target-date="${targetDate}">
  <div class="p-0 container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 col-md-10 col-lg-8">
        <div class="countdown-container text-${alignment.value}">
          <h3 class="mb-4 countdown-title">${title.value}</h3>
          <div class="countdown-wrapper d-flex justify-content-${alignment.value}">
            <div class="mx-2 countdown-item days-item">
              <div class="p-3 mb-1 countdown-value fs-1 fw-bold" style="${counterStyle.join('; ')}">00</div>
              <div class="countdown-label" style="${labelStyle.join('; ')}">天</div>
            </div>
            <div class="mx-2 countdown-item hours-item">
              <div class="p-3 mb-1 countdown-value fs-1 fw-bold" style="${counterStyle.join('; ')}">00</div>
              <div class="countdown-label" style="${labelStyle.join('; ')}">时</div>
            </div>
            <div class="mx-2 countdown-item minutes-item">
              <div class="p-3 mb-1 countdown-value fs-1 fw-bold" style="${counterStyle.join('; ')}">00</div>
              <div class="countdown-label" style="${labelStyle.join('; ')}">分</div>
            </div>
            <div class="mx-2 countdown-item seconds-item">
              <div class="p-3 mb-1 countdown-value fs-1 fw-bold" style="${counterStyle.join('; ')}">00</div>
              <div class="countdown-label" style="${labelStyle.join('; ')}">秒</div>
            </div>
          </div>
          <div class="mt-4 countdown-message">${message.value}</div>
        </div>
      </div>
    </div>
  </div>
  <script>${countdownScript}<\/script>
</div>`.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = prepareCountdownHTML()
    if (!html) return
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
    
    ElMessage.success('倒计时设置已更新')
  } catch (error) {
    console.error('应用倒计时更改时出错:', error)
    ElMessage.error('更新倒计时失败，请检查输入')
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 15px;
  position: relative;
  max-width: 100%;
  overflow-x: hidden;
}

.apply-button-container {
  margin-top: 15px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-tabs__nav) {
  padding: 0 5px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-radio-button__inner) {
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-time-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 5px;
  flex-wrap: wrap;
  
  .date-picker {
    flex: 1;
    min-width: 120px;
  }
  
  .time-picker {
    flex: 0 0 100px;
  }
}

.date-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.sub-field-group {
  margin-bottom: 12px;
  
  .sub-field-label {
    font-size: 13px;
    color: #606266;
    margin-bottom: 6px;
  }
}

.border-radii-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  
  .border-size,
  .border-radius {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .border-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
  
  .color-input {
    flex: 1;
  }
  
  .color-preview {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    flex-shrink: 0;
    cursor: pointer;
  }
  
  .color-picker-button {
    height: 24px;
  }
}

.border-select {
  width: 100%;
}

:deep(.el-input-group__prepend) {
  padding: 0 8px;
}

:deep(.el-form-item__label) {
  padding-bottom: 4px;
  line-height: 1.4;
  font-size: 13px;
}

:deep(.el-slider) {
  margin-top: 6px;
  margin-bottom: 6px;
}
</style> 

