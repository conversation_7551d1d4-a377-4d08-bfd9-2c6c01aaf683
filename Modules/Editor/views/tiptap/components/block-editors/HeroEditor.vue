<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <el-form-item label="标题">
            <el-input v-model="heroContent.title" @input="markAsChanged" type="textarea" :rows="2" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="heroContent.description" @input="markAsChanged" type="textarea" :rows="3" />
          </el-form-item>
          
          <el-divider content-position="left">按钮组</el-divider>
          
          <div v-for="(button, index) in heroContent.buttons" :key="index" class="button-item-edit">
            <div class="button-item-header">
              <span>按钮 #{{ index + 1 }}</span>
              <div class="button-item-actions">
                <el-button 
                  type="text" 
                  :icon="Edit" 
                  @click="editButtonItem(index)"
                  title="编辑"
                />
                <el-button 
                  type="text" 
                  :icon="Delete" 
                  @click="removeButtonItem(index)" 
                  v-if="heroContent.buttons.length > 1"
                  title="删除"
                />
              </div>
            </div>
            <div v-if="editingButtonIndex === index" class="button-item-content">
              <el-form-item label="按钮文本">
                <el-input v-model="heroContent.buttons[index].text" @input="markAsChanged" />
              </el-form-item>
              <el-form-item label="按钮类型">
                <el-select v-model="heroContent.buttons[index].type" @change="markAsChanged" style="width: 100%">
                  <el-option label="Primary" value="primary" />
                  <el-option label="Secondary" value="secondary" />
                  <el-option label="Outline Primary" value="outline-primary" />
                  <el-option label="Outline Secondary" value="outline-secondary" />
                </el-select>
              </el-form-item>
              <el-form-item label="链接地址">
                <el-input v-model="heroContent.buttons[index].href" @input="markAsChanged" placeholder="可选" />
              </el-form-item>
            </div>
          </div>
          
          <div class="add-item-button">
            <el-button type="primary" @click="addButtonItem" icon="Plus">添加按钮</el-button>
          </div>
          
          <el-divider content-position="left">图片设置</el-divider>
          
          <el-form-item label="Hero 图片 URL">
            <el-input v-model="heroContent.imageUrl" @input="markAsChanged" placeholder="输入图片URL" />
          </el-form-item>
          <el-form-item label="图片 Alt 文本">
            <el-input v-model="heroContent.imageAlt" @input="markAsChanged" placeholder="描述图片内容" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <el-form-item label="内边距">
            <el-radio-group v-model="heroStyles.padding" @change="markAsChanged">
              <el-radio-button label="py-2">小</el-radio-button>
              <el-radio-button label="py-5">中</el-radio-button>
              <el-radio-button label="py-7">大</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="布局类型">
            <el-radio-group v-model="heroStyles.layoutType" @change="markAsChanged">
              <el-radio-button label="standard">标准布局</el-radio-button>
              <el-radio-button label="reverse">反向布局</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="按钮样式">
            <el-radio-group v-model="heroStyles.buttonStyle" @change="markAsChanged">
              <el-radio-button label="rounded">常规</el-radio-button>
              <el-radio-button label="rounded-pill">胶囊形</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="背景颜色">
            <el-color-picker v-model="heroStyles.backgroundColor" @change="markAsChanged" show-alpha />
          </el-form-item>
          
          <el-form-item label="标题颜色">
            <el-color-picker v-model="heroStyles.titleColor" @change="markAsChanged" show-alpha />
          </el-form-item>
          
          <el-form-item label="描述文字颜色">
            <el-color-picker v-model="heroStyles.descriptionColor" @change="markAsChanged" show-alpha />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, computed, watch } from 'vue'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'HeroEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 编辑中的按钮索引
const editingButtonIndex = ref(0)

// 原始HTML
const originalHtml = ref('')

// 英雄区内容数据
interface ButtonItem {
  text: string
  href: string
  type: string
}

interface HeroContent {
  title: string
  description: string
  buttons: ButtonItem[]
  imageUrl: string
  imageAlt: string
}

// 英雄区样式数据
interface HeroStyles {
  padding: string
  layoutType: string
  buttonStyle: string
  backgroundColor: string
  titleColor: string
  descriptionColor: string
}

// 初始化英雄区内容和样式
const heroContent = ref<HeroContent>({
  title: 'Create legendary brands',
  description: 'Add a brief and powerful description of your business value proposition and how you solve for customers',
  buttons: [
    {
      text: 'Request demo',
      href: '',
      type: 'primary'
    },
    {
      text: 'Learn more',
      href: '',
      type: 'outline-secondary'
    }
  ],
  imageUrl: 'https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/hero-banner.png',
  imageAlt: 'Hero image'
})

const heroStyles = ref<HeroStyles>({
  padding: 'py-5',
  layoutType: 'standard',
  buttonStyle: 'rounded-pill',
  backgroundColor: '',
  titleColor: '',
  descriptionColor: ''
})

/**
 * 设置默认值
 */
const setDefaultValues = () => {
  heroContent.value = {
    title: 'Create legendary brands',
    description: 'Add a brief and powerful description of your business value proposition and how you solve for customers',
    buttons: [
      {
        text: 'Request demo',
        href: '',
        type: 'primary'
      },
      {
        text: 'Learn more',
        href: '',
        type: 'outline-secondary'
      }
    ],
    imageUrl: 'https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/hero-banner.png',
    imageAlt: 'Hero image'
  }
  
  heroStyles.value = {
    padding: 'py-5',
    layoutType: 'standard',
    buttonStyle: 'rounded-pill',
    backgroundColor: '',
    titleColor: '',
    descriptionColor: ''
  }
  
  // 初始化编辑第一个按钮
  if (heroContent.value.buttons.length > 0) {
    editingButtonIndex.value = 0
  }
}

/**
 * 从英雄区块元素中提取内容和样式
 */
const extractHeroContent = (): boolean => {
  if (!props.blockElement) return false
  
  // 保存原始HTML
  originalHtml.value = props.blockElement.outerHTML
  
  try {
    // 提取padding类
    const paddingClass = Array.from(props.blockElement.classList)
      .find(cls => cls.startsWith('py-'))
    
    if (paddingClass) {
      heroStyles.value.padding = paddingClass
    }
    
    // 提取背景色
    const computedStyle = window.getComputedStyle(props.blockElement)
    if (computedStyle.backgroundColor && computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
      heroStyles.value.backgroundColor = computedStyle.backgroundColor
    }
    
    // 提取标题内容
    const headingElement = props.blockElement.querySelector('h1, h2')
    if (headingElement) {
      heroContent.value.title = headingElement.textContent?.trim() || ''
      
      // 提取标题颜色
      const headingComputedStyle = window.getComputedStyle(headingElement)
      if (headingComputedStyle.color && headingComputedStyle.color !== 'rgb(0, 0, 0)') {
        heroStyles.value.titleColor = headingComputedStyle.color
      }
    }
    
    // 提取描述内容
    const descriptionElement = props.blockElement.querySelector('p')
    if (descriptionElement) {
      heroContent.value.description = descriptionElement.textContent?.trim() || ''
      
      // 提取描述颜色
      const descriptionComputedStyle = window.getComputedStyle(descriptionElement)
      if (descriptionComputedStyle.color && descriptionComputedStyle.color !== 'rgb(0, 0, 0)') {
        heroStyles.value.descriptionColor = descriptionComputedStyle.color
      }
    }
    
    // 提取按钮信息
    const buttonElements = props.blockElement.querySelectorAll('button, a.btn')
    if (buttonElements.length > 0) {
      const extractedButtons: ButtonItem[] = []
      
      buttonElements.forEach((buttonEl) => {
        const buttonType = Array.from(buttonEl.classList)
          .find(cls => cls.startsWith('btn-'))
          ?.replace('btn-', '') || 'primary'
        
        extractedButtons.push({
          text: buttonEl.textContent?.trim() || '按钮',
          href: buttonEl instanceof HTMLAnchorElement ? buttonEl.href : '',
          type: buttonType
        })
        
        // 检查按钮样式是否为胶囊形
        if (buttonEl.classList.contains('rounded-pill')) {
          heroStyles.value.buttonStyle = 'rounded-pill'
        } else {
          heroStyles.value.buttonStyle = 'rounded'
        }
      })
      
      if (extractedButtons.length > 0) {
        heroContent.value.buttons = extractedButtons
      }
    }
    
    // 提取图片信息
    const imageElement = props.blockElement.querySelector('img')
    if (imageElement) {
      heroContent.value.imageUrl = imageElement.src || ''
      heroContent.value.imageAlt = imageElement.alt || 'Hero image'
    }
    
    // 检查布局顺序
    const rowElement = props.blockElement.querySelector('.row')
    if (rowElement) {
      const firstColumn = rowElement.querySelector('.col-lg-6:first-child')
      if (firstColumn && !firstColumn.querySelector('h1, h2')) {
        // 如果第一列不包含标题，则为反向布局
        heroStyles.value.layoutType = 'reverse'
      } else {
        heroStyles.value.layoutType = 'standard'
      }
    }
    
    return true
  } catch (error) {
    console.error('提取英雄区内容时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    editingButtonIndex.value = -1
    
    // 重新提取数据
    const extracted = extractHeroContent()
    
    // 如果提取失败，使用默认值
    if (!extracted) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的初始化逻辑，因为已经由 watch 处理
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 添加按钮
const addButtonItem = () => {
  heroContent.value.buttons.push({
    text: `按钮 ${heroContent.value.buttons.length + 1}`,
    href: '',
    type: 'primary'
  })
  editingButtonIndex.value = heroContent.value.buttons.length - 1
  markAsChanged()
}

// 编辑按钮
const editButtonItem = (index: number) => {
  editingButtonIndex.value = editingButtonIndex.value === index ? -1 : index
}

// 删除按钮
const removeButtonItem = (index: number) => {
  if (heroContent.value.buttons.length > 1) {
    heroContent.value.buttons.splice(index, 1)
    if (editingButtonIndex.value === index) {
      editingButtonIndex.value = -1
    } else if (editingButtonIndex.value > index) {
      editingButtonIndex.value--
    }
    markAsChanged()
  }
}

/**
 * 生成英雄区HTML
 */
const generateHeroHtml = (): string => {
  try {
    // 决定列的顺序
    const isReverse = heroStyles.value.layoutType === 'reverse'
    
    // 生成按钮HTML
    const buttonsHtml = heroContent.value.buttons.map(button => {
      const btnClass = button.type.startsWith('outline-') 
        ? `btn-${button.type}` 
        : `btn-${button.type}`
      
      const btnHtml = button.href 
        ? `<a href="${button.href}" class="px-4 py-2 bootstrap-button btn ${btnClass} ${heroStyles.value.buttonStyle}" role="button">${button.text}</a>`
        : `<button class="px-4 py-2 bootstrap-button btn ${btnClass} ${heroStyles.value.buttonStyle}">${button.text}</button>`
      
      return `<div data-bs-component="button">${btnHtml}</div>`
    }).join('\n')
    
    // 生成内容列HTML
    const contentColHtml = `
      <div class="col-lg-6">
        <div data-bs-component="rich-text" class="bootstrap-heading">
          <h1 class="mb-3 display-4 fw-bold" ${heroStyles.value.titleColor ? `style="color: ${heroStyles.value.titleColor}"` : ''}>${heroContent.value.title}</h1>
          <p class="text-muted" ${heroStyles.value.descriptionColor ? `style="color: ${heroStyles.value.descriptionColor} !important"` : ''}>${heroContent.value.description}</p>
        </div>
        <div class="gap-2 mt-4 d-flex">
          ${buttonsHtml}
        </div>
      </div>
    `
    
    // 生成图片列HTML
    const imageColHtml = `
      <div class="col-lg-6">
        <div class="position-relative">
          <img src="${heroContent.value.imageUrl}" class="img-fluid rounded-4" alt="${heroContent.value.imageAlt}">
          <div class="p-3 bg-white shadow position-absolute top-50 end-0 translate-middle-y rounded-3" style="max-width: 200px; right: -30px;">
            <div class="gap-2 d-flex align-items-center">
              <div class="rounded-circle bg-success" style="width: 12px; height: 12px;"></div>
              <span>Browser preview</span>
            </div>
            <div class="mt-2">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="previewSwitch1" checked>
                <label class="form-check-label" for="previewSwitch1">Mobile</label>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="previewSwitch2" checked>
                <label class="form-check-label" for="previewSwitch2">Desktop</label>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="previewSwitch3" checked>
                <label class="form-check-label" for="previewSwitch3">Tablet</label>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="previewSwitch4" checked>
                <label class="form-check-label" for="previewSwitch4">Print</label>
              </div>
            </div>
          </div>
        </div>
      </div>
    `
    
    // 根据布局类型决定列的顺序
    const columnsHtml = isReverse 
      ? `${imageColHtml}\n${contentColHtml}`
      : `${contentColHtml}\n${imageColHtml}`
    
    // 生成背景样式
    const bgStyle = heroStyles.value.backgroundColor 
      ? ` style="background-color: ${heroStyles.value.backgroundColor}"`
      : ''
    
    // 返回完整的英雄区HTML
    return `
<div class="${heroStyles.value.padding} hero-section" data-bs-component="hero"${bgStyle}>
  <div class="container">
    <div class="row align-items-center">
      ${columnsHtml}
    </div>
  </div>
</div>
    `.trim()
  } catch (error) {
    console.error('生成英雄区HTML时出错:', error)
    // 返回空字符串，避免崩溃
    return ''
  }
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generateHeroHtml()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
  } catch (error) {
    console.error('应用英雄区更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.button-item-edit {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
}

.button-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  
  span {
    font-weight: bold;
  }
}

.button-item-actions {
  display: flex;
  gap: 5px;
}

.button-item-content {
  padding: 15px;
}

.add-item-button {
  margin-top: 15px;
  text-align: center;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 