<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <el-form label-position="top">
          <!-- Logo设置 -->
          <el-form-item label="Logo">
            <div class="image-preview-container">
              <div v-if="logoUrl" class="image-preview">
                <img :src="logoUrl" class="preview-img" />
              </div>
              <div v-else class="image-placeholder">
                <el-icon class="placeholder-icon"><Picture /></el-icon>
                <div class="placeholder-text">暂无Logo</div>
              </div>
            </div>

            <div class="button-group">
              <el-button type="primary" class="button-no-border" @click="openLogoFileManager">
                <el-icon class="icon"><Upload /></el-icon>
                <span>选择Logo</span>
              </el-button>
              <el-button v-if="logoUrl" class="delete-btn" @click="confirmDeleteLogo">
                <el-icon class="icon"><Delete /></el-icon>
                <span>删除Logo</span>
              </el-button>
            </div>

            <el-form-item label="Logo高度">
              <el-input-number 
                v-model="logoHeight" 
                :min="20" 
                :max="100" 
                @change="markAsChanged"
                size="default"
                controls-position="right"
              />
              <span class="unit-text">px</span>
            </el-form-item>
          </el-form-item>

          <!-- 导航链接设置 -->
          <el-form-item label="导航链接">
            <div v-for="(item, index) in navItems" :key="index" class="nav-item-container">
              <div class="nav-item-header">
                <span>导航项 {{ index + 1 }}</span>
                <el-button 
                  type="danger" 
                  size="small" 
                  circle
                  @click="removeNavItem(index)"
                  v-if="navItems.length > 1"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              
              <el-input
                v-model="item.text"
                placeholder="链接文本"
                class="mb-2"
                @input="markAsChanged"
              />
              <el-input
                v-model="item.href"
                placeholder="链接地址"
                class="mb-2"
                @input="markAsChanged"
              />
            </div>
            
            <el-button 
              type="primary" 
              plain 
              class="add-nav-btn" 
              @click="addNavItem"
            >
              <el-icon><Plus /></el-icon>
              添加导航项
            </el-button>
          </el-form-item>

          <!-- 按钮设置 -->
          <el-form-item label="操作按钮">
            <el-switch
              v-model="showActionButton"
              @change="markAsChanged"
              class="mb-2"
            />
            
            <template v-if="showActionButton">
              <el-input
                v-model="actionButton.text"
                placeholder="按钮文本"
                class="mb-2"
                @input="markAsChanged"
              />
              <el-input
                v-model="actionButton.href"
                placeholder="按钮链接"
                class="mb-2"
                @input="markAsChanged"
              />
            </template>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="样式" name="style">
        <el-form label-position="top">
          <!-- 导航栏背景色 -->
          <el-form-item label="背景颜色">
            <el-select 
              v-model="navbarBg" 
              @change="markAsChanged"
              class="w-100"
            >
              <el-option label="白色" value="bg-white" />
              <el-option label="浅色" value="bg-light" />
              <el-option label="深色" value="bg-dark navbar-dark" />
              <el-option label="主题色" value="bg-primary navbar-dark" />
            </el-select>
          </el-form-item>

          <!-- 导航栏内边距 -->
          <el-form-item label="内边距">
            <el-select 
              v-model="navbarPadding" 
              @change="markAsChanged"
              class="w-100"
            >
              <el-option label="小" value="py-2" />
              <el-option label="中" value="py-3" />
              <el-option label="大" value="py-4" />
            </el-select>
          </el-form-item>

          <!-- 按钮样式 -->
          <el-form-item label="按钮样式" v-if="showActionButton">
            <el-select 
              v-model="actionButton.style" 
              @change="markAsChanged"
              class="w-100"
            >
              <el-option label="主要按钮" value="btn-primary" />
              <el-option label="次要按钮" value="btn-secondary" />
              <el-option label="轮廓按钮" value="btn-outline-primary" />
              <el-option label="链接按钮" value="btn-link" />
            </el-select>
          </el-form-item>

          <!-- 导航栏定位 -->
          <el-form-item label="导航栏定位">
            <el-select 
              v-model="navbarPosition" 
              @change="markAsChanged"
              class="w-100"
            >
              <el-option label="默认" value="" />
              <el-option label="固定顶部" value="fixed-top" />
              <el-option label="固定底部" value="fixed-bottom" />
              <el-option label="粘性顶部" value="sticky-top" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges">应用更改</el-button>
    </div>

    <!-- 文件管理器弹窗 -->
    <DocumentsManager 
      :BaseUrl="baseUrl" 
      :token="token" 
      :isMultiSelect="false"
      :locale="localeLang"
      @confirmSelection="confirmLogoSelection" 
      ref="documentsManagerRef" 
      v-model="visibleDialog"
      :showUploadButton="false" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, defineOptions, computed, watch } from 'vue'
import { Picture, Upload, Delete, Plus } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { DocumentsManager } from 'filestudio-bingo'
import { env, getAuthToken } from '/admin/support/helper'

// 定义组件名称
defineOptions({
  name: 'NavEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  },
  blockType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update-block'])

// 当前激活的标签
const activeTab = ref('content')

// 是否有未保存的更改
const isChanged = ref(false)

// 原始HTML
const originalHtml = ref('')

// Logo相关
const logoUrl = ref('')
const logoHeight = ref(30)

// 导航项
const navItems = ref([
  { text: 'Home', href: '#' },
  { text: 'Features', href: '#' },
  { text: 'Pricing', href: '#' }
])

// 操作按钮
const showActionButton = ref(true)
const actionButton = ref({
  text: 'Get started',
  href: '#',
  style: 'btn-primary'
})

// 样式设置
const navbarBg = ref('bg-white')
const navbarPadding = ref('py-3')
const navbarPosition = ref('')

// 文件管理器相关
const visibleDialog = ref(false)
const documentsManagerRef = ref(null)
let tokens: string | null = getAuthToken()
const token = ref<string>(tokens ?? '')
const baseUrl = ref<string>(env('VITE_BASE_URL').replace('/admin/', '/'))
const localeLang = computed(() => localStorage.getItem('bwms_language') || 'zh_CN')

/**
 * 设置默认值
 */
const setDefaultValues = () => {
  logoUrl.value = 'https://7528302.fs1.hubspotusercontent-na1.net/hub/7528302/hubfs/theme_hubspot/elevate/images/hexagontalxio-dark.png'
  logoHeight.value = 30
  
  navItems.value = [
    { text: 'Home', href: '#' },
    { text: 'Features', href: '#' },
    { text: 'Pricing', href: '#' }
  ]
  
  showActionButton.value = true
  actionButton.value = {
    text: 'Get started',
    href: '#',
    style: 'btn-primary'
  }
  
  navbarBg.value = 'bg-white'
  navbarPadding.value = 'py-3'
  navbarPosition.value = ''
}

/**
 * 提取导航栏数据
 */
const extractNavbarData = (): boolean => {
  if (!props.blockElement) return false
  
  try {
    // 保存原始HTML
    originalHtml.value = props.blockElement.outerHTML
    
    // 提取Logo
    const logoImg = props.blockElement.querySelector('.navbar-brand img')
    if (logoImg) {
      logoUrl.value = logoImg.getAttribute('src') || ''
      logoHeight.value = parseInt(logoImg.getAttribute('height') || '30')
    }
    
    // 提取导航项
    const navLinks = props.blockElement.querySelectorAll('.nav-link')
    if (navLinks.length > 0) {
      navItems.value = Array.from(navLinks).map(link => ({
        text: link.textContent || '',
        href: link.getAttribute('href') || '#'
      }))
    }
    
    // 提取操作按钮
    const actionBtn = props.blockElement.querySelector('.btn')
    if (actionBtn) {
      showActionButton.value = true
      actionButton.value = {
        text: actionBtn.textContent || 'Get started',
        href: actionBtn.getAttribute('href') || '#',
        style: Array.from(actionBtn.classList)
          .find(cls => cls.startsWith('btn-')) || 'btn-primary'
      }
    } else {
      showActionButton.value = false
    }
    
    // 提取样式
    const navbar = props.blockElement.querySelector('.navbar')
    if (navbar) {
      // 背景色
      if (navbar.classList.contains('bg-light')) navbarBg.value = 'bg-light'
      else if (navbar.classList.contains('bg-dark')) navbarBg.value = 'bg-dark navbar-dark'
      else if (navbar.classList.contains('bg-primary')) navbarBg.value = 'bg-primary navbar-dark'
      else navbarBg.value = 'bg-white'
      
      // 内边距
      if (navbar.classList.contains('py-2')) navbarPadding.value = 'py-2'
      else if (navbar.classList.contains('py-4')) navbarPadding.value = 'py-4'
      else navbarPadding.value = 'py-3'
      
      // 定位
      if (navbar.classList.contains('fixed-top')) navbarPosition.value = 'fixed-top'
      else if (navbar.classList.contains('fixed-bottom')) navbarPosition.value = 'fixed-bottom'
      else if (navbar.classList.contains('sticky-top')) navbarPosition.value = 'sticky-top'
      else navbarPosition.value = ''
    }
    
    return true
  } catch (error) {
    console.error('提取导航栏数据时出错:', error)
    return false
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue && newValue !== oldValue) {
    // 重置更改状态
    isChanged.value = false
    
    // 重新提取数据
    const extracted = extractNavbarData()
    
    // 如果提取失败，使用默认值
    if (!extracted) {
      setDefaultValues()
    }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 移除原有的初始化逻辑，因为已经由 watch 处理
  isChanged.value = false
})

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

// 打开文件管理器
const openLogoFileManager = () => {
  visibleDialog.value = true
}

// 确认删除Logo
const confirmDeleteLogo = () => {
  ElMessageBox.confirm(
    '确定要删除当前Logo吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    logoUrl.value = ''
    markAsChanged()
    ElMessage.success('Logo已删除')
  }).catch(() => {
    // 取消删除，不做任何操作
  })
}

// Logo选择确认
const confirmLogoSelection = (selectedFiles: any[]) => {
  if (!selectedFiles || selectedFiles.length === 0) return
  
  const file = selectedFiles[0]
  logoUrl.value = file.path || file.url
  markAsChanged()
}

// 添加导航项
const addNavItem = () => {
  navItems.value.push({
    text: '新导航项',
    href: '#'
  })
  markAsChanged()
}

// 删除导航项
const removeNavItem = (index: number) => {
  navItems.value.splice(index, 1)
  markAsChanged()
}

// 生成导航栏HTML
const generateNavbarHtml = (): string => {
  // 生成导航项HTML
  const navItemsHtml = navItems.value.map(item => `
    <li class="nav-item">
      <a class="nav-link" href="${item.href}">${item.text}</a>
    </li>
  `).join('\n')
  
  // 生成操作按钮HTML
  const actionButtonHtml = showActionButton.value ? `
    <button class="btn ${actionButton.value.style} rounded-pill ms-3">${actionButton.value.text}</button>
  ` : ''
  
  // 生成完整的导航栏HTML
  return `
<nav class="navbar-section responsive-block" data-bs-component="navbar">
  <div class="container">
    <div class="navbar navbar-expand-lg ${navbarBg.value} ${navbarPadding.value} ${navbarPosition.value}">
      <a class="navbar-brand" href="#">
        <img src="${logoUrl.value}" height="${logoHeight.value}" alt="Logo">
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          ${navItemsHtml}
        </ul>
        ${actionButtonHtml}
      </div>
    </div>
  </div>
</nav>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generateNavbarHtml()
    emit('update-block', { html })
    isChanged.value = false
  } catch (error) {
    console.error('应用导航栏更改时出错:', error)
  }
}
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 20px;
  position: relative;
}

.image-preview-container {
  width: 100%;
  height: 100px;
  background-color: #F6F6F6;
  margin-bottom: 18px;
  overflow: hidden;
  border-radius: 4px;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  .placeholder-icon {
    font-size: 40px;
    color: #9E9E9E;
    margin-bottom: 10px;
  }
  
  .placeholder-text {
    color: #9E9E9E;
    font-size: 16px;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  
  .delete-btn {
    color: #707070;
    background: #FFFFFF;
    border-radius: 5px;
  }
}

.nav-item-container {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  
  .nav-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    span {
      font-weight: 500;
    }
  }
}

.add-nav-btn {
  width: 100%;
  margin-top: 10px;
}

.unit-text {
  margin-left: 8px;
  color: #909399;
}

.apply-button-container {
  margin-top: 20px;
  text-align: center;
  padding: 10px 0;
  border-top: 1px dashed #e4e7ed;
}

.mb-2 {
  margin-bottom: 8px;
}

.w-100 {
  width: 100%;
}

:deep(.el-input-number) {
  width: 120px;
}
</style> 