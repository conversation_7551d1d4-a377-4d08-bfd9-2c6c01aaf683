export const shiliPageTemplate = `
<div class="marketing-landing-page responsive-block">
    <!-- 导航栏 -->
    <nav class="py-3 bg-white navbar navbar-expand-lg navbar-light" data-bs-component="navbar">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img data-bs-component="bootstrap-image" src="https://7528302.fs1.hubspotusercontent-na1.net/hub/7528302/hubfs/theme_hubspot/elevate/images/hexagontalxio-dark.png" height="30" alt="Logo">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Pricing</a>
                    </li>
                </ul>
                <div data-bs-component="bootstrap-button">
                    <button class="btn btn-primary rounded-pill ms-3">Get started</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 头部区域 -->
    <div class="py-5 hero-section" data-bs-component="hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div data-bs-component="bootstrap-heading">
                        <h1 class="mb-3 display-4 fw-bold">Create legendary brands</h1>
                        <p class="text-muted">Add a brief and powerful description of your business value proposition and how you solve for customers</p>
                    </div>
                    <div class="gap-2 mt-4 d-flex">
                        <div data-bs-component="bootstrap-button">
                            <button class="px-4 py-2 bootstrap-button btn btn-primary rounded-pill">Request demo</button>
                        </div>
                        <div data-bs-component="bootstrap-button">
                            <button class="px-4 py-2 bootstrap-button btn btn-outline-secondary rounded-pill">Learn more</button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="position-relative">
                        <img data-bs-component="bootstrap-image" src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/hero-banner.png" class="img-fluid rounded-4" alt="Hero image">
                        <div class="p-3 bg-white shadow position-absolute top-50 end-0 translate-middle-y rounded-3" style="max-width: 200px; right: -30px;">
                            <div class="gap-2 d-flex align-items-center">
                                <div class="rounded-circle bg-success" style="width: 12px; height: 12px;"></div>
                                <span>Browser preview</span>
                            </div>
                            <div class="mt-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="previewSwitch1" checked>
                                    <label class="form-check-label" for="previewSwitch1">Mobile</label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="previewSwitch2" checked>
                                    <label class="form-check-label" for="previewSwitch2">Desktop</label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="previewSwitch3" checked>
                                    <label class="form-check-label" for="previewSwitch3">Tablet</label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="previewSwitch4" checked>
                                    <label class="form-check-label" for="previewSwitch4">Print</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 特性区域 -->
    <div data-bs-component="feature-cards" class="py-5 text-white bg-dark">
        <div class="container">
            <div class="row">
                <div class="mb-4 col-md-4">
                    <div class="text-center feature-card">
                        <div class="mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-play-circle" viewBox="0 0 16 16">
                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                <path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445z"/>
                            </svg>
                        </div>
                        <div data-bs-component="richTextBlock">
                            <h3>Content Creation</h3>
                            <p class="text-white-50">Stand out with our captivating content on social services, videos to engage their digital audience</p>
                        </div>
                    </div>
                </div>
                <div class="mb-4 col-md-4">
                    <div class="text-center feature-card">
                        <div class="mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-bar-chart" viewBox="0 0 16 16">
                                <path d="M4 11H2v3h2v-3zm5-4H7v7h2V7zm5-5v12h-2V2h2zm-2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1h-2zM6 7a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7zm-5 4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1v-3z"/>
                            </svg>
                        </div>
                        <div data-bs-component="richTextBlock">
                            <h3>Marketing Analytics</h3>
                            <p class="text-white-50">Our platform empowers informed decision making, tracking campaign performance, roi, and ROI</p>
                        </div>
                    </div>
                </div>
                <div class="mb-4 col-md-4">
                    <div class="text-center feature-card">
                        <div class="mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-graph-up" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M0 0h1v15h15v1H0V0Zm14.817 3.113a.5.5 0 0 1 .07.704l-4.5 5.5a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61 4.15-5.073a.5.5 0 0 1 .704-.07Z"/>
                            </svg>
                        </div>
                        <div data-bs-component="richTextBlock">
                            <h3>Journey Optimization</h3>
                            <p class="text-white-50">Use customer data to deliver personalized messaging and content, enhancing engagement</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 协作部分 -->
    <div data-bs-component="info-section" class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="mb-4 col-lg-6 mb-lg-0">
                    <img data-bs-component="bootstrap-image" src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/analytics-dashboard.png" class="img-fluid rounded-4" alt="Analytics dashboard">
                </div>
                <div class="col-lg-6">
                    <div data-bs-component="richTextBlock">
                        <h2 class="mb-3 fw-bold">Collaborate seamlessly</h2>
                        <p class="text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
                    </div>
                    <div data-bs-component="bootstrap-button">
                        <a href="#" class="explore-more">Explore more</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 多渠道营销 -->
    <div data-bs-component="info-section" class="py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="mb-4 col-lg-6 order-lg-2 mb-lg-0">
                    <img data-bs-component="bootstrap-image" src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/multichannel-campaign.png" class="img-fluid rounded-4" alt="Multichannel campaign">
                </div>
                <div class="col-lg-6 order-lg-1">
                    <div data-bs-component="richTextBlock">
                        <h2 class="mb-3 fw-bold">Manage multi-channel campaigns</h2>
                        <p class="text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
                    </div>
                    <div data-bs-component="bootstrap-button">
                        <a href="#" class="explore-more">Explore more</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 内容创建 -->
    <div data-bs-component="info-section" class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="mb-4 col-lg-6 mb-lg-0">
                    <img data-bs-component="bootstrap-image" src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/content-calendar.png" class="img-fluid rounded-4" alt="Content calendar">
                </div>
                <div class="col-lg-6">
                    <div data-bs-component="richTextBlock">
                        <h2 class="mb-3 fw-bold">Create and target content dynamically</h2>
                        <p class="text-muted">Write a description highlighting the functionality, benefits, and uniqueness of your feature. A couple of sentences here is just right.</p>
                    </div>
                    <div data-bs-component="bootstrap-button">
                        <a href="#" class="explore-more">Explore more</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 客户推荐 -->
    <div data-bs-component="testimonial-slider" class="py-5 bg-light">
        <div class="container">
            <div class="testimonial-card">
                <div class="row">
                    <div class="mb-3 text-center col-md-3 mb-md-0">
                        <img data-bs-component="bootstrap-image" src="https://7528302.fs1.hubspotusercontent-na1.net/hubfs/7528302/testimonial-avatar.png" class="mb-3 rounded-circle" width="100" height="100" alt="Testimonial avatar">
                    </div>
                    <div class="col-md-9">
                        <div data-bs-component="richTextBlock">
                            <p class="mb-4 lead">"The measurable results have transformed our business. Highly recommend for anyone looking to elevate their marketing game."</p>
                            <h5 class="mb-1">Neel Kumar</h5>
                            <p class="text-muted">VP of Marketing @ Heptawise</p>
                        </div>
                        <div data-bs-component="bootstrap-button">
                            <a href="#" class="text-decoration-none">Read case study →</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-4 text-center">
                <div class="gap-2 d-inline-flex">
                    <button class="btn btn-sm btn-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                    <button class="btn btn-sm btn-outline-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                    <button class="btn btn-sm btn-outline-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                    <button class="btn btn-sm btn-outline-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                    <button class="btn btn-sm btn-outline-primary rounded-circle" style="width: 12px; height: 12px; padding: 0;"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计 -->
    <div data-bs-component="metrics" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="stats-box">
                        <div data-bs-component="richTextBlock">
                            <h2 class="display-3 fw-bold text-primary">15k+</h2>
                            <p class="text-muted">Customers of Elevate</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-box">
                        <div data-bs-component="richTextBlock">
                            <h2 class="display-3 fw-bold text-primary">200%</h2>
                            <p class="text-muted">Daily active users</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-box">
                        <div data-bs-component="richTextBlock">
                            <h2 class="display-3 fw-bold text-primary">300%</h2>
                            <p class="text-muted">Daily active users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 行动号召 -->
    <div data-bs-component="bootstrap-cta" class="py-5 text-white bg-primary">
        <div class="container text-center">
            <div data-bs-component="richTextBlock">
                <h2 class="mb-4 display-6 fw-bold">Ready to Elevate your business?</h2>
            </div>
            <div data-bs-component="bootstrap-button">
                <button class="px-4 py-2 bootstrap-button btn btn-light rounded-pill">Get in touch</button>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div data-bs-component="footer" class="py-5 text-white bg-dark">
        <div class="container">
            <div class="text-center">
                <div data-bs-component="richTextBlock">
                    <p>No menus installed from the sidebar, create a new one by navigating to the Navigation Menu tool</p>
                </div>
            </div>
            <div data-bs-component="social-flow" class="social-flow-block social-flow-container">
                <div class="social-icons-wrapper">
                    <div class="social-icons-row">
                        <a href="#" class="social-icon facebook-icon" title="Facebook" target="_blank">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-icon twitter-icon" title="Twitter" target="_blank">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon instagram-icon" title="Instagram" target="_blank">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-icon linkedin-icon" title="LinkedIn" target="_blank">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="social-icon youtube-icon" title="YouTube" target="_blank">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    /* 基础样式 */
    .marketing-landing-page {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    /* 全局动效和过渡 */
    .marketing-landing-page .col-lg-6,
    .marketing-landing-page .feature-card,
    .marketing-landing-page .stats-box,
    .marketing-landing-page .testimonial-card {
      transition: transform 0.5s ease, opacity 0.5s ease, box-shadow 0.3s ease;
    }
    
    /* 悬停效果 */
    .marketing-landing-page .feature-card:hover,
    .marketing-landing-page .stats-box:hover,
    .marketing-landing-page .testimonial-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    /* 按钮样式增强 */
    .marketing-landing-page .btn {
      transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
    }
    
    .marketing-landing-page .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    /* 特性卡片样式 */
    .feature-card {
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      background-color: rgba(255, 255, 255, 0.05);
      height: 100%;
      transition: all 0.3s ease;
    }
    
    /* 统计盒子样式 */
    .stats-box {
      padding: 20px;
      text-align: center;
      border-radius: 10px;
      transition: all 0.3s ease;
      height: 100%;
    }
    
    /* 客户推荐样式 */
    .testimonial-card {
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
      background-color: #fff;
      transition: all 0.3s ease;
    }
    
    /* 链接样式 */
    .explore-more {
      color: #6c5ce7;
      font-weight: 500;
      text-decoration: none;
      position: relative;
      padding-bottom: 2px;
      transition: all 0.3s ease;
    }
    
    .explore-more:after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: #6c5ce7;
      transition: width 0.3s ease;
    }
    
    .explore-more:hover:after {
      width: 100%;
    }
    
    /* 移动端预览模式样式 - 增加选择器特异性 */
    .mobile-preview .marketing-landing-page .hero-section {
      padding: 40px 0;
    }
    
    .mobile-preview .marketing-landing-page .hero-section h1 {
      font-size: 2rem;
    }
    
    .mobile-preview .marketing-landing-page .feature-card,
    .mobile-preview .marketing-landing-page .stats-box,
    .mobile-preview .marketing-landing-page .testimonial-card {
      margin-bottom: 20px;
    }
    
    .mobile-preview .marketing-landing-page .hero-section .position-absolute {
      display: none ;
    }
    
    .mobile-preview .marketing-landing-page .display-3 {
      font-size: 2.5rem;
    }
    
    .mobile-preview .marketing-landing-page .display-4 {
      font-size: 2rem;
    }
    
    .mobile-preview .marketing-landing-page .display-6 {
      font-size: 1.5rem;
    }
    
    .mobile-preview .marketing-landing-page .py-5 {
      padding-top: 2rem !important;
      padding-bottom: 2rem !important;
    }
    
    .mobile-preview .marketing-landing-page .row {
      flex-direction: column;
    }
    
    .mobile-preview .marketing-landing-page .col-md-3,
    .mobile-preview .marketing-landing-page .col-md-4,
    .mobile-preview .marketing-landing-page .col-md-5,
    .mobile-preview .marketing-landing-page .col-md-6,
    .mobile-preview .marketing-landing-page .col-md-7,
    .mobile-preview .marketing-landing-page .col-md-8,
    .mobile-preview .marketing-landing-page .col-md-9,
    .mobile-preview .marketing-landing-page [class*="col-"] {
      width: 100% !important;
      max-width: 100% !important;
      flex: 0 0 100% !important;
    }
    
    .mobile-preview .marketing-landing-page .text-md-start {
      text-align: center !important;
    }
    
    .mobile-preview .marketing-landing-page .justify-content-md-start {
      justify-content: center !important;
    }
    
    .mobile-preview .marketing-landing-page .order-lg-1,
    .mobile-preview .marketing-landing-page .order-lg-2 {
      order: 0 !important;
    }

    /* 桌面预览模式样式 */
    .desktop-preview .marketing-landing-page .hero-section {
      padding: 70px 0;
    }
    
    .desktop-preview .marketing-landing-page .py-5 {
      padding-top: 4rem !important;
      padding-bottom: 4rem !important;
    }
    
    .desktop-preview .marketing-landing-page .text-md-start {
      text-align: left !important;
    }
    
    .desktop-preview .marketing-landing-page .justify-content-md-start {
      justify-content: flex-start !important;
    }

    /* 响应式媒体查询 */
    @media screen and (max-width: 767.98px) {
      .marketing-landing-page .hero-section {
        padding: 40px 0;
      }
      
      .marketing-landing-page .hero-section h1 {
        font-size: 2rem;
      }
      
      .marketing-landing-page .feature-card,
      .marketing-landing-page .stats-box,
      .marketing-landing-page .testimonial-card {
        margin-bottom: 20px;
      }
      
      .marketing-landing-page .hero-section .position-absolute {
        display: none;
      }
      
      .marketing-landing-page .display-3 {
        font-size: 2.5rem;
      }
      
      .marketing-landing-page .display-4 {
        font-size: 2rem;
      }
      
      .marketing-landing-page .display-6 {
        font-size: 1.5rem;
      }
      
      .marketing-landing-page .py-5 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
      }
      
      .marketing-landing-page .row {
        flex-direction: column;
      }
      
      .marketing-landing-page .col-md-3,
      .marketing-landing-page .col-md-4,
      .marketing-landing-page .col-md-5,
      .marketing-landing-page .col-md-6,
      .marketing-landing-page .col-md-7,
      .marketing-landing-page .col-md-8,
      .marketing-landing-page .col-md-9 {
        width: 100%;
        max-width: 100%;
        flex: 0 0 100%;
      }
      
      .marketing-landing-page .text-md-start {
        text-align: center !important;
      }
      
      .marketing-landing-page .justify-content-md-start {
        justify-content: center !important;
      }
      
      .marketing-landing-page .order-lg-1,
      .marketing-landing-page .order-lg-2 {
        order: 0;
      }
    }
    
    @media screen and (min-width: 768px) and (max-width: 991.98px) {
      .marketing-landing-page .hero-section h1 {
        font-size: 2.5rem;
      }
      
      .marketing-landing-page .hero-section .position-absolute {
        right: -15px;
        max-width: 170px;
      }
      
      .marketing-landing-page .display-3 {
        font-size: 3rem;
      }
      
      .marketing-landing-page .py-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
      }
    }
    
    @media screen and (min-width: 992px) {
      .marketing-landing-page .feature-card,
      .marketing-landing-page .stats-box {
        height: 100%;
      }
    }
    </style>
    
    <script>
    document.addEventListener("DOMContentLoaded", function() {
      // 获取页面元素
      const landingPage = document.querySelector('.marketing-landing-page');
      const featureCards = document.querySelectorAll('.feature-card');
      const statsBoxes = document.querySelectorAll('.stats-box');
      const infoSections = document.querySelectorAll('[data-bs-component="info-section"]');
      const heroSection = document.querySelector('.hero-section');
      
      // 初始化淡入效果的元素
      const fadeElements = [
        ...featureCards,
        ...statsBoxes,
        ...document.querySelectorAll('.testimonial-card'),
        ...document.querySelectorAll('.col-lg-6')
      ];
      
      // 设置初始状态
      fadeElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      });
      
      // 检查元素是否在视口中并应用动画
      function checkScroll() {
        fadeElements.forEach((element, index) => {
          const elementTop = element.getBoundingClientRect().top;
          const windowHeight = window.innerHeight;
          
          if (elementTop < windowHeight * 0.85) {
            setTimeout(() => {
              element.style.opacity = '1';
              element.style.transform = 'translateY(0)';
            }, index * 100); // 依次显示，产生连续动画效果
          }
        });
      }
      
      // 响应式调整
      function handleResize() {
        const windowWidth = window.innerWidth;
        const isMobile = windowWidth < 768;
        const isTablet = windowWidth >= 768 && windowWidth < 992;
        
        // 清除所有可能影响布局的类
        landingPage.classList.remove('mobile-view', 'tablet-view', 'desktop-view');
        
        if (isMobile) {
          landingPage.classList.add('mobile-view');
        } else if (isTablet) {
          landingPage.classList.add('tablet-view');
        } else {
          landingPage.classList.add('desktop-view');
        }
        
        // 强制重新计算布局
        landingPage.style.display = 'none';
        setTimeout(() => {
          landingPage.style.display = '';
          setTimeout(() => {
            checkScroll();
          }, 100);
        }, 10);
      }
      
      // 动态适应导航栏
      function handleNavbar() {
        const navbar = document.querySelector('.navbar');
        
        if (window.scrollY > 50) {
          navbar.classList.add('shadow');
          navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        } else {
          navbar.classList.remove('shadow');
          navbar.style.background = 'white';
        }
      }
      
      // 添加视差效果
      function parallaxEffect() {
        if (heroSection) {
          const scrolled = window.scrollY;
          const heroImage = heroSection.querySelector('.img-fluid');
          
          if (heroImage && window.innerWidth >= 992) {
            heroImage.style.transform = 'translateY(' + scrolled * 0.1 + 'px)';
          }
        }
      }
      
      // 为信息部分添加交错效果
      infoSections.forEach((section, index) => {
        if (index % 2 === 1) {
          section.classList.add('bg-light');
        }
      });
      
      // 初始检查
      handleResize();
      checkScroll();
      handleNavbar();
      
      // 监听事件
      window.addEventListener('scroll', () => {
        checkScroll();
        handleNavbar();
        parallaxEffect();
      });
      window.addEventListener('resize', handleResize);
      
      // 为特性卡片添加悬停效果
      featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-10px)';
          const icon = this.querySelector('svg');
          if (icon) {
            icon.style.transform = 'scale(1.1)';
            icon.style.transition = 'transform 0.5s ease';
          }
        });
        
        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0)';
          const icon = this.querySelector('svg');
          if (icon) {
            icon.style.transform = 'scale(1)';
          }
        });
      });
      
      // 统计数字增长动画
      const statsNumbers = document.querySelectorAll('.stats-box h2');
      let animated = false;
      
      function animateNumbers() {
        if (animated) return;
        
        const statsSection = document.querySelector('[data-bs-component="metrics"]');
        if (!statsSection) return;
        
        const statsSectionTop = statsSection.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        
        if (statsSectionTop < windowHeight * 0.7) {
          statsNumbers.forEach(number => {
            const targetNumber = number.innerText;
            const plusSign = targetNumber.includes('+');
            const percentSign = targetNumber.includes('%');
            let finalNumber = targetNumber;
            
            if (plusSign) finalNumber = targetNumber.replace('+', '');
            if (percentSign) finalNumber = targetNumber.replace('%', '');
            
            // 判断k标记
            const kMultiplier = finalNumber.includes('k') ? 1000 : 1;
            if (finalNumber.includes('k')) finalNumber = finalNumber.replace('k', '');
            
            const numValue = parseFloat(finalNumber) * kMultiplier;
            let startNumber = 0;
            const duration = 2000; // 2秒内完成动画
            const startTime = performance.now();
            
            function updateNumber(currentTime) {
              const elapsedTime = currentTime - startTime;
              const progress = Math.min(elapsedTime / duration, 1);
              const currentValue = Math.floor(progress * numValue);
              
              let displayValue = currentValue;
              if (kMultiplier === 1000) displayValue = (currentValue / 1000).toFixed(1) + 'k';
              if (plusSign) displayValue += '+';
              if (percentSign) displayValue += '%';
              
              number.innerText = displayValue;
              
              if (progress < 1) {
                requestAnimationFrame(updateNumber);
              }
            }
            
            requestAnimationFrame(updateNumber);
          });
          
          animated = true;
        }
      }
      
      window.addEventListener('scroll', animateNumbers);
      
      // 初始化检查动画
      animateNumbers();
    });
    </script>
</div>` 