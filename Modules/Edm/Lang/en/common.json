{"router": {"contactImport": "Import Contacts"}, "title": "Message Service", "tabs": {"emailTemplate": "Email Templates", "thirdPartyEmail": "Third-party Email Service", "emailLogs": "Email Sending Logs", "testEmail": "Test Email"}, "template": {"edit": "Edit and Preview", "types": {"notification": "Notification Templates", "verification": "Verification Templates", "password": "Password Related Templates", "emailBinding": "Email Binding Templates", "validation": "Validation Code Templates", "registerLogin": "Register/Login Verification Templates"}}, "messages": {"fetchError": "Failed to fetch template list"}, "thirdParty": {"enable": "Enable Third-party Service", "smtp": {"address": "SMTP Address", "port": "Port", "username": "Username", "password": "Password", "sender": "Sender <PERSON><PERSON>", "from": "From Email", "security": "Security", "apiKey": "API Key", "placeholder": {"address": "Please enter SMTP address", "port": "Please enter port", "username": "Please enter username", "password": "Please enter password", "sender": "Please enter sender email", "from": "Please enter from email"}}, "buttons": {"save": "Save", "saveAndTest": "Save and Send Test Email"}, "messages": {"requiredFields": "Please fill in required fields", "saveSuccess": "Save successful", "saveError": "Save failed", "testSuccess": "Test email sent successfully", "testError": "Failed to send test email"}}, "testEmail": {"form": {"smtpProvider": {"label": "SMTP Provider", "placeholder": "Please select SMTP provider"}, "subject": {"label": "Email Subject", "placeholder": "Please enter email subject"}, "recipient": {"label": "Recipient Email", "placeholder": "Please enter recipient email"}, "content": {"label": "Custom Content"}}, "buttons": {"send": "Send", "sending": "Sending..."}, "validation": {"smtpProvider": "Please select SMTP provider", "subject": {"required": "Please enter email subject", "length": "Subject length should be between 1 and 100 characters"}, "email": {"required": "Please enter email address", "invalid": "Please enter a valid email address"}, "content": {"required": "Please enter email content", "length": "Content length should be between 1 and 5000 characters"}}, "messages": {"success": "<PERSON>ail sent successfully", "error": "Failed to send email, please try again later"}}, "templateDetail": {"title": {"edit": "Edit Template", "new": "New Template"}, "buttons": {"cancel": "Cancel", "save": "Save", "resetPreview": "Reset Preview", "resetTemplate": "Reset Template", "confirm": "Confirm"}, "form": {"customTemplate": "Custom Template", "lockTip": "Please enable custom template to edit", "sender": "Sender", "subject": "Subject", "content": "Template Content", "variables": {"title": "Available Variables", "list": {"clientName": "{{client_name}}", "appName": "{{app_name}}", "userEmail": "{{user_email}}", "verifyLink": "{{verify_link}}", "expiresIn": "{{expires_in}}"}}}, "preview": {"title": "Preview", "sender": "From: ", "subject": "Subject: "}, "validation": {"name": "Please enter template name", "invoke": "Please enter invoke identifier", "sender": "Please enter sender", "subject": "Please enter subject", "content": "Please enter template content"}, "messages": {"fetchError": "Failed to fetch template details", "saveSuccess": "Save successful", "saveError": "Save failed", "resetConfirm": "Are you sure you want to reset this template? It will be restored to system default content.", "resetSuccess": "Reset successful", "resetError": "Reset failed"}}, "emailLogs": {"title": "Email Sending Logs", "buttons": {"refresh": "Refresh", "viewDetails": "View Details"}, "table": {"columns": {"id": "ID", "sender": "Sender", "recipient": "Recipient", "service": "Email Service", "size": "Size", "subject": "Subject", "status": "Status", "route": "Send Route", "sendTime": "Send Time", "actions": "Actions"}, "status": {"success": "Send Success", "failed": "Send Failed"}}, "dialog": {"title": "Email Sending Details", "fields": {"sender": "From: ", "recipient": "To: ", "service": "Email Service: ", "size": "Size: ", "subject": "Subject: ", "status": "Status: ", "route": "Route: ", "sendTime": "Send Time: ", "host": "Send Host: "}}, "messages": {"fetchError": "Failed to fetch log list"}}, "contact": {"title": "Contact Management", "import": "Import", "export": "Export", "sync": "Sync", "tableSettings": "Settings", "search": "Filter", "create": "Create", "customerType": "Customer Type", "allContacts": "All", "searchTitle": "Search", "searchPlaceholder": "Please enter contact name, email...", "reset": "Reset", "name_label": "Contact", "status_label": "Status", "blacklistStatus": "Please select blacklist status", "blacklisted": "Blacklisted", "nonBlacklisted": "Non-blacklisted", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "deleteConfirm": "Are you sure you want to delete this contact?", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "saveSuccess": "Saved successfully", "saveFailed": "Save failed", "exportSuccess": "Export successful", "exportFailed": "Export failed", "syncSuccess": "Sync successful", "syncFailed": "Sync failed", "fetchFailed": "Failed to fetch contact list", "columnSettings": {"title": "Table Column Settings", "tip": "Drag to adjust column order, check to show/hide columns", "saveSuccess": "Table settings saved"}, "columns": {"name": "Contact", "email": "Email", "phone": "Phone", "whatsapp": "WhatsApp", "subscribed": "Subscription Status", "blacklist": "Blacklist", "owner": "Owner", "updatedAt": "Updated At", "createdAt": "Created At", "actions": "Actions"}, "customerTypeDialog": {"add": "Add Customer Type", "edit": "Edit Customer Type", "deleteConfirm": "Are you sure you want to delete this customer type?", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "addSuccess": "Added successfully", "editSuccess": "Edited successfully", "operationFailed": "Operation failed", "fetchFailed": "Failed to fetch customer type list", "editTitle": "Edit Customer Type", "addTitle": "Add Customer Type", "typeName": "Type Name", "senderName": "Sender Name", "senderEmail": "Sender <PERSON><PERSON>", "replyEmail": "<PERSON><PERSON>", "customTitle": "Custom Title", "customFooter": "Custom Footer", "description": "Description", "cancel": "Cancel", "confirm": "Confirm", "placeholder": {"typeName": "Please enter type name", "senderName": "Please enter sender name", "senderEmail": "Please enter sender email", "replyEmail": "Please enter reply email", "customTitle": "Please enter custom title", "customFooter": "Please enter custom footer", "description": "Please enter description"}, "validation": {"typeName": "Please enter type name", "senderName": "Please enter sender name", "senderEmail": "Please enter sender email", "senderEmailFormat": "Please enter a valid email format", "replyEmail": "Please enter reply email", "replyEmailFormat": "Please enter a valid email format"}}, "editDialog": {"edit": "Edit Contact", "create": "New Contact", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "sms": "SMS", "whatsapp": "WhatsApp", "subscribed": "Subscription Status", "blacklist": "Blacklist", "extendId": "Extend ID", "customerTypes": "Customer Types", "timezone": "Timezone", "company": "Company", "enterFirstName": "Please enter first name", "enterLastName": "Please enter last name", "enterEmail": "Please enter email", "enterSms": "Please enter mobile number", "enterWhatsApp": "Please enter WhatsApp number", "subscribed_active": "Subscribed", "subscribed_inactive": "Unsubscribed", "blacklist_yes": "Yes", "blacklist_no": "No", "enterExtendId": "Please enter extend ID", "extendIdTip": "Maximum of 254 characters", "selectCustomerTypes": "Please select customer types", "selectTimezone": "Please select timezone", "enterCompany": "Please enter company", "cancel": "Cancel", "confirm": "Confirm", "validation": {"firstName_required": "Please enter first name", "lastName_required": "Please enter last name", "email_required": "Please enter email", "email_invalid": "Please enter a valid email address", "sms_invalid": "Please enter a valid mobile number", "extendId_max": "Extend ID cannot exceed 254 characters"}, "timezones": {"beijing": "(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi", "tokyo": "(GMT+09:00) Tokyo", "london": "(GMT+00:00) London", "newYork": "(GMT-05:00) New York", "losAngeles": "(GMT-08:00) Los Angeles"}, "countryCode": {"china": "+86 China", "us": "+1 United States", "uk": "+44 United Kingdom", "japan": "+81 Japan", "korea": "+82 South Korea"}, "fetchError": "Failed to fetch customer type list", "updateSuccess": "Updated successfully", "createSuccess": "Created successfully", "saveError": "Save failed"}, "importDialog": {"title": "Import Contacts", "dragTip": "Click or drag file to this area to upload", "formatTip": "Supports .csv, .xlsx file formats", "cancel": "Cancel", "startImport": "Start Import", "importSuccess": "Import successful", "importFailed": "Import failed"}, "importTypeDialog": {"title": "Select Import Type", "cancel": "Cancel", "continue": "Continue", "options": {"contact": {"title": "Contact Information", "description": "Import basic contact information"}, "company": {"title": "Company", "description": "Import company-related contact information"}, "message": {"title": "Contact Us", "description": "Import information from contact us forms"}, "note": {"title": "Notes", "description": "Import notes related to contacts"}}}, "companyImport": {"title": "Company Information Import", "dragTip": "Drag file here, or", "clickUpload": "click to upload", "formatTip": "Supports .csv, .xls, .xlsx file formats", "startImport": "Start Import", "importSuccess": "Import successful", "importFailed": "Import failed"}, "contactImport": {"fileTab": "File Import", "pasteTab": "Copy & Paste", "dragTip": "Drag file here, or", "clickUpload": "click to upload", "formatTip": "Supports .csv, .xls, .xlsx file formats", "pastePlaceholder": "Please copy Excel table data here, the system will automatically recognize the data format...", "previewTitle": "Data Preview", "confirm": "Confirm Import", "reset": "Reset", "startImport": "Start Import", "importSuccess": "Import successful", "importFailed": "Import failed"}, "messageImport": {"title": "Contact Form Message Import", "dragTip": "Drag file here, or", "clickUpload": "click to upload", "formatTip": "Supports .csv, .xls, .xlsx file formats", "startImport": "Start Import", "importSuccess": "Import successful", "importFailed": "Import failed"}, "noteImport": {"title": "Notes Import", "dragTip": "Drag file here, or", "clickUpload": "click to upload", "formatTip": "Supports .csv, .xls, .xlsx file formats", "startImport": "Start Import", "importSuccess": "Import successful", "importFailed": "Import failed"}, "importPage": {"back": "Back", "importSuccess": "Import successful", "types": {"contact": "Import Contact Information", "company": "Import Company Information", "message": "Import Contact Form Messages", "note": "Import Notes Information", "default": "Import Contacts"}}}, "campaigns": {"buttons": {"createFolder": "Create Folder", "createCampaign": "Create Campaign", "newFolder": "New Folder"}, "folder": {"title": "Folders", "count": "campaigns", "actions": {"moveToFolder": "Move to Folder", "rename": "<PERSON><PERSON>", "delete": "Delete"}}, "filters": {"status": "Status", "type": "Type", "search": "Search campaigns"}, "table": {"columns": {"name": "Campaign Name", "status": "Status", "statistics": "Statistics", "createdAt": "Created At"}, "statistics": {"sent": "<PERSON><PERSON>", "opened": "Opened", "clicked": "Clicked"}}, "status": {"DRAFT": "Draft", "SCHEDULED": "Scheduled", "SENDING": "Sending", "SENT": "<PERSON><PERSON>", "SUSPENDED": "Suspended"}, "type": {"EMAIL": "Email", "SMS": "SMS", "WHATSAPP": "WhatsApp", "WEB_PUSH": "Web Push"}, "dialog": {"selectCampaignType": "Select Campaign Type", "moveToFolder": "Move to Folder", "renameFolder": "<PERSON><PERSON>", "enterFolderName": "Please enter folder name", "cancel": "Cancel", "continue": "Continue", "createFolder": "Create New Folder", "folderName": "Name", "folderNamePlaceholder": "Please enter folder name", "confirm": "Confirm", "deleteConfirm": "Are you sure you want to delete this folder?", "folderNameRequired": "Please enter folder name", "folderNameLength": "Length should be between 1 and 50 characters"}, "messages": {"sendSuccess": "<PERSON>ail sent successfully", "createSuccess": "Created successfully", "deleteSuccess": "Deleted successfully", "moveSuccess": "Moved successfully", "renameSuccess": "<PERSON><PERSON> successfully", "deleteConfirm": "Are you sure you want to delete this campaign?", "renameFailed": "<PERSON><PERSON> failed"}, "create": {"email": "Create email marketing campaign", "sms": "Create SMS marketing campaign", "whatsapp": "Create WhatsApp marketing campaign", "web_push": "Create web push marketing campaign", "default": "Create campaign", "success": "Created successfully"}, "sms": {"buttons": {"back": "Back", "saveDraft": "Save Draft", "create": "Create Campaign"}, "form": {"name": "Campaign Name", "content": "SMS Content", "sendTime": "Send Time", "namePlaceholder": "Please enter campaign name", "contentPlaceholder": "Please enter SMS content", "sendNow": "Send Immediately", "sendSchedule": "Schedule Send", "selectTime": "Select send time", "validation": {"nameRequired": "Please enter campaign name", "nameLength": "Length should be between 2 and 50 characters", "contentRequired": "Please enter SMS content"}}}, "webPush": {"title": "Web Push", "buttons": {"back": "Back", "saveDraft": "Save Draft", "create": "Create Campaign"}, "form": {"name": "Campaign Name", "namePlaceholder": "Enter campaign name", "title": "Push Title", "titlePlaceholder": "Enter push title", "image": "Push Image", "content": "Push Content", "contentPlaceholder": "Enter push content", "action": "Click Action", "selectAction": "Select click action", "actionUrl": "Open URL", "actionApp": "Open App", "urlPlaceholder": "Enter URL", "sendTime": "Send Time", "sendNow": "Send Now", "sendSchedule": "Schedule", "selectTime": "Select send time", "validation": {"nameRequired": "Please enter campaign name", "nameLength": "Length should be 2 to 50 characters", "titleRequired": "Please enter push title", "contentRequired": "Please enter push content", "actionRequired": "Please select click action"}}}, "whatsapp": {"buttons": {"back": "Back", "saveDraft": "Save Draft", "create": "Create Campaign"}, "form": {"name": "Campaign Name", "namePlaceholder": "Enter campaign name", "title": "Message Title", "titlePlaceholder": "Enter message title", "image": "Message Image", "content": "Message Content", "contentPlaceholder": "Enter message content", "action": "Click Action", "selectAction": "Select click action", "actionUrl": "Open URL", "actionCall": "Make Call", "urlPlaceholder": "Enter URL", "phonePlaceholder": "Enter phone number", "sendTime": "Send Time", "sendNow": "Send Now", "sendSchedule": "Schedule", "selectTime": "Select send time", "validation": {"nameRequired": "Please enter campaign name", "nameLength": "Length should be 2 to 50 characters", "titleRequired": "Please enter message title", "contentRequired": "Please enter message content", "actionRequired": "Please select click action"}}}, "noAvailableFolders": "No available folders", "regularCampaign": {"buttons": {"back": "Back", "cancel": "Cancel", "continue": "Continue", "save": "Save", "createAbTest": "Create"}, "tabs": {"regular": {"title": "Regular Campaign", "description": "Create a single email campaign"}, "abTest": {"title": "A/B Test Campaign", "description": "Test different versions of your campaign"}}, "form": {"campaignName": "Campaign Name", "campaignNamePlaceholder": "Please enter campaign name", "testType": "Test Type", "subjectTest": {"title": "Subject Line Test", "description": "Test different subject lines to improve open rates"}, "contentTest": {"title": "Email Content Test", "description": "Test different email content to improve click rates"}}, "sender": {"title": "Sender", "setupText": "Set up sender information", "previewText": "Recipients will see this name as the sender", "manageButton": "Manage Sender", "completeButton": "Complete", "info": "Sender Information", "selectEmail": "Select Sender Email", "senderName": "Sender Name", "enterSenderName": "Please enter sender name", "preview": "Preview", "previewReceive": "Recipients will see:", "senderNamePlaceholder": "Sender Name"}, "recipient": {"title": "Recipients", "setupText": "Select recipient list", "selectedList": "Selected list", "addButton": "Add Recipients", "completeButton": "Complete", "sendTo": "Send to", "selectList": "Please select contact list", "excludeUncontacted": "Don't send to uncontacted contacts", "advancedOptions": "Advanced Options", "doNotSendTo": "Don't send to", "selectExcludeLists": "Select lists to exclude", "filterRecipients": "Filter Recipients", "addCondition": "Add Condition", "fields": {"email": "Email", "name": "Name", "tags": "Tags", "created_at": "Registration Date"}, "operators": {"contains": "Contains", "equals": "Equals", "not_equals": "Not equals", "starts_with": "Starts with"}, "enterValue": "Enter value", "delete": "Delete"}, "subject": {"title": "Subject", "setupText": "Set up email subject", "addButton": "Add Subject", "completeButton": "Complete", "emailSubject": "Email Subject", "subjectLine": "Subject Line", "enterSubject": "Please enter email subject", "subjectTip": "Use 30-50 characters for the subject line to get the best open rate", "previewText": "Preview Text", "enterPreviewText": "Add preview text (optional)", "previewTextTip": "Preview text will be displayed below the subject line in the inbox", "previewEffect": "Preview Effect", "tips": "✨ Tips:", "tipsList": {"personalized": "Personalized subject lines can improve open rates", "avoidPunctuation": "Avoid excessive punctuation and all capital letters", "complementary": "Preview text should complement the subject line content"}, "bestPractice": "Use 30-50 characters for the subject line to get the best open rate", "previewTextPlaceholder": "Add preview text (optional)", "defaultSubject": "Email Subject", "defaultPreviewText": "Preview text content", "tip1": "Personalized subject lines can improve open rates", "tip2": "Avoid excessive punctuation and all capital letters", "tip3": "Preview text should complement the subject line content"}, "design": {"title": "Design", "fromScratch": "Start from scratch", "templateSelected": "Template Selected", "selectTemplate": "Select Template", "modifyTemplate": "Modify Template", "startFromBlank": "Start from scratch"}, "otherSettings": {"title": "Other Settings", "sendNow": "Send Now", "sendScheduled": "Send Scheduled", "editButton": "Edit Settings", "completeButton": "Complete", "complete": "Complete", "editSettings": "Edit Settings", "scheduleSend": "Schedule Send", "personalization": {"title": "Personalization", "enablePersonalization": "Personalize send to field", "selectField": "Select send field", "email": "Email", "alternativeEmail": "Alternative Email", "workEmail": "Work Email"}, "sendingTracking": {"title": "Sending and Tracking", "customRecipientId": "Use different recipient ID", "enterRecipientId": "Enter recipient ID", "googleAnalytics": "Activate Google Analytics tracking", "enterGoogleAnalyticsId": "Enter Google Analytics ID", "useCustomRecipientId": "Use different recipient ID", "utmParameters": "Activate UTM ID as optional parameters", "enterUtmId": "Enter UTM ID", "ignoreListSettings": "Ignore list custom settings", "addAttachments": "Add attachments", "selectFile": "Select file", "addTags": "Add tags", "enterTags": "Add tags", "setExpiryDate": "Set expiry date", "selectExpiryDate": "Select expiry date", "activateGoogleAnalytics": "Activate Google Analytics tracking", "activateUtmParameters": "Activate UTM ID as optional parameters", "addTagsPlaceholder": "Please enter tags"}, "subscription": {"title": "Subscription", "useCustomPage": "Use custom unsubscribe page", "enterCustomPageUrl": "Enter custom unsubscribe page URL", "useProfileForm": "Use update profile form"}, "design": {"title": "Design", "editDefaultTitle": "Edit default title", "enterDefaultTitle": "Enter default title", "editDefaultFooter": "Edit default footer", "enterDefaultFooter": "Enter default footer", "enableBrowserPreview": "Enable view in browser link", "editDefaultTitlePlaceholder": "Please enter default title", "editDefaultFooterPlaceholder": "Please enter default footer"}}, "contactLists": {"allContacts": "All Contacts", "vipCustomers": "VIP Customers", "newUsers": "New Users"}, "tags": {"marketing": "Marketing", "notification": "Notification", "promotion": "Promotion", "news": "News", "update": "Update"}, "messages": {"abTestCreated": "A/B Test campaign created successfully"}}, "emptyState": {"title": "No activities yet", "description": "Create your first activity to start using", "button": "Create Activity"}}, "Router": {"templateDetail": "Template Details"}}