<template>
  <div class="table-page email-logs" v-loading="loading">
    <div class="content-box">
    <el-table :data="logs" style="width: 100%">
      <template #empty>
        <el-empty :description="t('Cms.list.no_data')" image-size="100px" />
      </template>
      <el-table-column prop="id" :label="$t('Edm.emailLogs.table.columns.id')" width="60" fixed="left" />
      <el-table-column prop="from" :label="$t('Edm.emailLogs.table.columns.sender')" min-width="230" />
      <el-table-column prop="to" :label="$t('Edm.emailLogs.table.columns.recipient')" min-width="200" />
      <el-table-column prop="service" :label="$t('Edm.emailLogs.table.columns.service')" width="120" />
      <el-table-column prop="size" :label="$t('Edm.emailLogs.table.columns.size')" width="100" />
      <el-table-column prop="subject" :label="$t('Edm.emailLogs.table.columns.subject')" min-width="110" />
      <el-table-column prop="status" :label="$t('Edm.emailLogs.table.columns.status')" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? $t('Edm.emailLogs.table.status.success') : $t('Edm.emailLogs.table.status.failed') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="route" :label="$t('Edm.emailLogs.table.columns.route')" min-width="220" />
      <el-table-column prop="created_at" :label="$t('Edm.emailLogs.table.columns.sendTime')" width="180" fixed="right">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('Edm.emailLogs.table.columns.actions')" min-width="150" fixed="right">
        <template #default="{ row }">
          <div class="bwms-operate-btn-box">
            <el-button class="bwms-operate-btn" @click="showDetail(row)">
              <el-icon><img src="/resources/admin/assets/icon/ViewIcon.png"></el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    </div>

    <!-- <div class="relative z-10 flex justify-end py-3 mt-4">
      <el-button type="primary" size="default" :icon="Refresh" @click="refreshLogs" class="mr-4 bwms-btn">{{ $t('Edm.emailLogs.buttons.refresh') }}</el-button>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div> -->
    <!-- 分页器 -->
    <div class="box-footer">
    <div class="table-pagination-style">
      <div class="pagination-left">
        <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
        <el-select
          v-model="pageSize"
          class="page-size-select"
          @change="handleSizeChange"
        >
          <el-option
            v-for="size in [10, 20, 50, 100]"
            :key="size"
            :label="size"
            :value="size"
            class="page-size-option"
          />
          <template #empty>
            <div style="text-align: center; padding: 8px 0; font-size: 12px;">
              {{ t('Cms.list.no_data') }}
            </div>
          </template>
        </el-select>
        <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
      </div>
      <div class="pagination-right">
        <el-pagination
          v-model:current-page="currentPage"
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    </div>

    <!-- 日志详情弹窗 -->
    <el-dialog class="el-dialog-common-cls" v-model="dialogVisible" :title="$t('Edm.emailLogs.dialog.title')" width="600px" destroy-on-close>
      <div v-if="selectedLog" class="log-detail">
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.sender') }}</span>
          <span>{{ selectedLog.from }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.recipient') }}</span>
          <span>{{ selectedLog.to }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.service') }}</span>
          <span>{{ selectedLog.service }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.size') }}</span>
          <span>{{ selectedLog.size }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.subject') }}</span>
          <span>{{ selectedLog.subject }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.status') }}</span>
          <el-tag :type="selectedLog.status === 1 ? 'success' : 'danger'">
            {{ selectedLog.status === 1 ? $t('Edm.emailLogs.table.status.success') : $t('Edm.emailLogs.table.status.failed') }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.route') }}</span>
          <span>{{ selectedLog.route }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.sendTime') }}</span>
          <span>{{ formatDate(selectedLog.created_at) }}</span>
        </div>
        <div class="detail-item">
          <span class="label">{{ $t('Edm.emailLogs.dialog.fields.host') }}</span>
          <span>{{ selectedLog.sender_host }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import http from '/admin/support/http'
import dayjs from 'dayjs'
import { Refresh } from '@element-plus/icons-vue'

const { t } = useI18n()

interface EmailLog {
  id: number
  from: string
  to: string
  sender_host: string
  service: string
  size: string
  route: string
  status: number
  subject: string
  creator_id: number
  created_at: string
  updated_at: string
}

const loading = ref(false)
const logs = ref<EmailLog[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const selectedLog = ref<EmailLog | null>(null)

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const fetchLogs = async () => {
  try {
    loading.value = true
    const { data } = await http.get('/edm/logs', {
      _action: 'getData',
      locale: 'zh_CN',
      items: '',
      page: currentPage.value,
      perPage: pageSize.value,
    })

    if (data.code === 200) {
      logs.value = data.data.items
      total.value = data.data.total
    } else {
      ElMessage.error(data.message || t('Edm.emailLogs.messages.fetchError'))
    }
  } catch (error) {
    console.error('获取日志列表失败:', error)
    ElMessage.error(t('Edm.emailLogs.messages.fetchError'))
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchLogs()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchLogs()
}

const showDetail = (row: EmailLog) => {
  selectedLog.value = row
  dialogVisible.value = true
}

const refreshLogs = () => {
  fetchLogs()
}

onMounted(() => {
  fetchLogs()
})
</script>

<style lang="scss" scoped>
.content-box {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
}
.log-detail {
  .detail-item {
    margin-bottom: 12px;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #4b5563;
      margin-right: 8px;
      font-weight: 500;
      display: inline-block;
      min-width: 80px;
    }
  }

  .content-preview {
    padding: 12px;
    background-color: #f8fafc;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 8px;
    font-size: 14px;
    line-height: 1.6;
  }
}

</style>
