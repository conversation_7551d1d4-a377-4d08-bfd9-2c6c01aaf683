<template>
  <div class="table-page bwms-module" v-loading="saveLoading">
    <div class="module-header">
      <!-- <div class="title">{{ isEdit ? $t('Edm.templateDetail.title.edit') : $t('Edm.templateDetail.title.new') }}</div> -->
      <div class="title"></div>
      <div class="actions">
        <el-button @click="handleCancel">{{ $t('Edm.templateDetail.buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleSave">{{ $t('Edm.templateDetail.buttons.save') }}</el-button>
        <el-button v-if="props.fromCampaign" type="success" @click="handleConfirm">
          {{ $t('Edm.templateDetail.buttons.confirm') }}
        </el-button>
      </div>
    </div>

    <div class="content-box scroll-bar-custom">
      <div class="main-content">
        <!-- 左侧编辑区 -->
        <div class="edit-section custom-scrollbar">
          <div class="section-header">
            <div class="custom-template">
              <span>{{ $t('Edm.templateDetail.form.customTemplate') }}</span>
              <el-switch v-model="useCustomTemplate" />
            </div>
          </div>

          <!-- 表单内容容器 -->
          <div class="form-wrapper">
            <div v-show="!useCustomTemplate" class="edit-mask" :style="{ height: formHeight + 'px' }">
              <div class="mask-tip">
                <el-icon class="mr-2"><Lock /></el-icon>
                {{ $t('Edm.templateDetail.form.lockTip') }}
              </div>
            </div>

            <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" class="form-container">
              <el-form-item :label="$t('Edm.templateDetail.form.sender')" prop="summary">
                <el-input v-model="formData.summary" @input="updatePreview" :disabled="!useCustomTemplate" />
              </el-form-item>

              <el-form-item :label="$t('Edm.templateDetail.form.subject')" prop="subject">
                <el-input v-model="formData.subject" @input="updatePreview" :disabled="!useCustomTemplate" />
              </el-form-item>

              <el-form-item :label="$t('Edm.templateDetail.form.content')" prop="content" class="template-content">
                <monaco-editor v-model="formData.content" language="html" height="400px" @change="updatePreview" :readonly="!useCustomTemplate" />
              </el-form-item>

              <div class="variables-hint">
                <p>{{ $t('Edm.templateDetail.form.variables.title') }}：</p>
                <div class="variables-list">
                  <el-tag v-for="variable in macros" :key="variable" class="variable-tag" @click="insertVariable(variable)">
                    {{ variable }}
                  </el-tag>
                </div>
              </div>
            </el-form>
          </div>
        </div>

        <!-- 右侧预览区 -->
        <div class="preview-section custom-scrollbar">
          <div class="section-header">
            <span class="section-title">{{ $t('Edm.templateDetail.preview.title') }}</span>
            <div class="preview-actions">
              <el-button type="primary" link @click="refreshPreview">{{ $t('Edm.templateDetail.buttons.resetPreview') }}</el-button>
              <el-button v-if="isEdit" type="warning" link @click="handleReset">{{ $t('Edm.templateDetail.buttons.resetTemplate') }}</el-button>
            </div>
          </div>
          <div class="preview-content custom-scrollbar">
            <div class="preview-email-header">
              <div class="preview-field">
                <span class="label">{{ $t('Edm.templateDetail.preview.sender') }}</span>
                <span class="value">{{ previewData.summary }}</span>
              </div>
              <div class="preview-field">
                <span class="label">{{ $t('Edm.templateDetail.preview.subject') }}</span>
                <span class="value">{{ previewData.subject }}</span>
              </div>
            </div>
            <div class="preview-email-content" v-html="previewData.content"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch, onUnmounted, computed  } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import MonacoEditor from './MonacoEditor.vue'
import { useTemplateStore } from '../stores/template'
import type { Template } from '../types'
import http from '/admin/support/http'
import { Lock } from '@element-plus/icons-vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const templateStore = useTemplateStore()
const useCustomTemplate = ref(false)

const formRef = ref()
const formData = ref({
  id: '',
  name: '欢迎邮件',
  invoke: 'edm.welcome',
  subject: '',
  summary: '新用户注册后发送的欢迎邮件',
  type: 1,
  lang: 'zh_CN',
  content: '',
  status: 1,
})

const isEdit = ref(false)
const templateId = route.params.id as string

const rules = computed(() => ({
  name: [{ required: true, message: t('Edm.templateDetail.validation.name'), trigger: 'blur' }],
  invoke: [{ required: true, message: t('Edm.templateDetail.validation.invoke'), trigger: 'blur' }],
  summary: [{ required: true, message: t('Edm.templateDetail.validation.sender'), trigger: 'blur' }],
  subject: [{ required: true, message: t('Edm.templateDetail.validation.subject'), trigger: 'blur' }],
  content: [{ required: true, message: t('Edm.templateDetail.validation.content'), trigger: 'blur' }],
}))

const availableVariables = ['{{client_name}}', '{{app_name}}', '{{user_email}}', '{{verify_link}}', '{{expires_in}}']

// 预览数据
const previewData = reactive({
  summary: '',
  subject: '',
  content: '',
})

// 模拟变量值
const previewVariables = {
  client_name: 'Authing',
  app_name: '示例应用',
  user_email: '<EMAIL>',
  verify_link: 'https://example.com/verify',
  expires_in: '24小时',
}

// 更新预览内容
const updatePreview = () => {
  let processedSummary = formData.value.summary
  let processedSubject = formData.value.subject
  let processedContent = formData.value.content

  // 替换所有变量
  Object.entries(previewVariables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g')
    processedSummary = processedSummary.replace(regex, value)
    processedSubject = processedSubject.replace(regex, value)
    processedContent = processedContent.replace(regex, value)
  })

  previewData.summary = processedSummary
  previewData.subject = processedSubject
  previewData.content = processedContent
}

// 刷新预览
const refreshPreview = () => {
  fetchTemplateDetail()
}

// 插入变量时同时更新预览
const insertVariable = (variable: string) => {
  formData.value.content += variable
  updatePreview()
}

// 添加类型定义
interface TemplateDetail {
  id: number
  name: string
  invoke: string
  subject: string
  summary: string | null
  type: number
  lang: string
  content: string
  creator_id: number
  created_at: string
  updated_at: string
}

interface TemplateResponse {
  template: TemplateDetail
  can_reset: boolean
  macros: string[]
}

// 在 setup 中添加获取模板详情的方法
const saveLoading = ref(false)
const templateDetail = ref<TemplateDetail | null>(null)
const macros = ref<string[]>([])

// 取模板详情
const fetchTemplateDetail = async () => {
  try {
    saveLoading.value = true
    const { data } = await http.get(`/edm/templates/${templateId}`)

    if (data.code === 200) {
      const { template } = data.data
      // 回填表单数据
      formData.value = {
        ...template,
        summary: template.summary || '', // 如果后端没有返回 summary，设置默认值
      }
      // 更新预览
      updatePreview()
    } else {
      ElMessage.error(data.message || t('Edm.templateDetail.messages.fetchError'))
    }
  } catch (error) {
    console.error('获取模板详情出错:', error)
    ElMessage.error(t('Edm.templateDetail.messages.fetchError'))
  } finally {
    saveLoading.value = false
  }
}

// 修改 onMounted 钩子
onMounted(async () => {
  if (templateId !== 'new') {
    isEdit.value = true
    await fetchTemplateDetail()
  }
})

const handleSave = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        saveLoading.value = true

        const params = {
          name: formData.value.name,
          invoke: formData.value.invoke,
          subject: formData.value.subject,
          summary: formData.value.summary,
          content: formData.value.content,
          type: formData.value.type,
          lang: formData.value.lang,
          status: formData.value.status,
        }

        if (isEdit.value) {
          await http.post(`/edm/templates/${templateId}`, params)
        } else {
          await http.post('/edm/templates', params)
        }

        ElMessage.success(t('Edm.templateDetail.messages.saveSuccess'))
        router.push({ name: 'templates' })
      } catch (error: any) {
        console.error('保存失败:', error)
        ElMessage.error(error?.response?.data?.message || t('Edm.templateDetail.messages.saveError'))
      } finally {
        saveLoading.value = false
      }
    }
  })
}

const handleCancel = () => {
  if (props.fromCampaign) {
    router.push({
      path: '/edm/campaigns/create',
      query: {
        templateId: templateId,
        type: 'email', // 保持原有的活动类型参数
      },
    })
  } else {
    router.push({ name: 'templates' })
  }
}

const formHeight = ref(0)

// 更新表单高度的方法
const updateFormHeight = async () => {
  await nextTick()
  if (formRef.value?.$el) {
    formHeight.value = formRef.value.$el.scrollHeight
  }
}

// 监听内容变化以更新高度
watch(
  () => formData.value.content,
  async () => {
    await updateFormHeight()
  },
  { flush: 'post' },
)

// 在组件挂载和窗口大小改变时更新高度
onMounted(() => {
  updateFormHeight()
  window.addEventListener('resize', updateFormHeight)
})

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', updateFormHeight)
})

// 添加重置模板的方法
const handleReset = async () => {
  try {
    await ElMessageBox.confirm(t('Edm.templateDetail.messages.resetConfirm'), '提示', {
      confirmButtonText: t('Edm.templateDetail.buttons.save'),
      cancelButtonText: t('Edm.templateDetail.buttons.cancel'),
      type: 'warning',
    })

    saveLoading.value = true
    const { data } = await http.post(`/edm/templates/${templateId}/reset`)

    if (data.code === 200) {
      ElMessage.success(t('Edm.templateDetail.messages.resetSuccess'))
      await fetchTemplateDetail()
      updatePreview()
    } else {
      ElMessage.error(data.message || t('Edm.templateDetail.messages.resetError'))
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('重置模板失败:', error)
      ElMessage.error(error?.response?.data?.message || t('Edm.templateDetail.messages.resetError'))
    }
  } finally {
    saveLoading.value = false
  }
}

// Add props
const props = defineProps({
  fromCampaign: {
    type: Boolean,
    default: false,
  },
  campaignPath: {
    type: String,
    default: '',
  },
})

// Add new method to handle confirm action
const handleConfirm = async () => {
  try {
    if (!formRef.value) return

    const valid = await formRef.value.validate()
    if (!valid) return
    // 返回到活动创建页面，并带上模板ID
    router.push({
      path: '/edm/campaigns/create',
      query: {
        templateId: templateId,
        type: 'email', // 保持原有的活动类型参数
      },
    })
  } catch (error) {
    console.error('确认模板选择失败:', error)
    ElMessage.error('确认模板选择失败')
  }
}
</script>
  
<style lang="scss" scoped>
.content-box {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  margin-top: 12px;
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    
    &:hover {
      background-color: transparent;
    }
  }
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  min-height: 0;
}

.edit-section,
.preview-section {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
}

.edit-section {
  border-right: 1px solid #ebeef5;
  padding-right: 40px;
}


.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
  line-height: 32px;
}

.section-title {
  font-size: 16px;
  color: #000;
}

.preview-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 126, 229, 0.2) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 126, 229, 0.2);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 126, 229, 0.4);
}

.preview-email-content {
  background-color: #fff;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  min-height: 300px;
  margin-top: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.template-content {
  margin-bottom: 16px;
}

.variables-hint {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.variables-hint p {
  margin-bottom: 12px;
  color: #000;
  font-size: 16px;
}

.variables-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.variable-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.variable-tag:hover {
  background-color: #ecf5ff;
  transform: translateY(-1px);
}





.preview-email-header {
  background-color: #fff;
  padding: 16px;
  border-radius: 8px 8px 0 0;
  border: 1px solid #ebeef5;
  border-bottom: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.preview-field {
  margin-bottom: 12px;
  font-size: 14px;
}

.preview-field:last-child {
  margin-bottom: 0;
}

.preview-field .label {
  color: #909399;
  margin-right: 8px;
  font-weight: 500;
  display: inline-block;
  min-width: 60px;
}

.preview-field .value {
  color: #303133;
}

/* 修改 MonacoEditor 容器样式 */
:deep(.monaco-editor) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.monaco-editor:hover) {
  border-color: #409eff;
}

.mask-tip {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 14px;
  background-color: rgba(144, 147, 153, 0.1);
  padding: 8px 16px;
  border-radius: 4px;
}



/* 禁用状态下的编辑器样式 */
:deep(.monaco-editor.readonly) {
  background-color: #f5f7fa;
  opacity: 0.8;
}

.form-wrapper {
  position: relative;
  flex: 1;
  min-height: 0;
}

.form-container {
  width: 100%;
  height: 100%;
}

.edit-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: height 0.3s ease;
}

.custom-template {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #000;
  font-size: 16px;
}

:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(1px);
}

.preview-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.module-header .title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}
</style> 