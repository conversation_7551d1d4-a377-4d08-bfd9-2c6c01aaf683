<template>
  <div class="table-page test-email-form">
    <el-form ref="formRef" label-position="top" :model="formData" :rules="rules">
      <el-form-item :label="$t('Edm.testEmail.form.smtpProvider.label')" prop="smtpProvider" required class="w-1/2">
        <el-select size="large"
          v-model="formData.smtpProvider"
          :placeholder="$t('Edm.testEmail.form.smtpProvider.placeholder')"
          class="w-full"
          filterable
          :filter-method="filterSmtpOptions"
          :remote-method="filterSmtpOptions"
        >
          <el-option v-for="item in filteredOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('Edm.testEmail.form.subject.label')" prop="subject" required class="w-1/2">
        <el-input size="large" v-model="formData.subject" :placeholder="$t('Edm.testEmail.form.subject.placeholder')" class="w-full" />
      </el-form-item>

      <el-form-item :label="$t('Edm.testEmail.form.recipient.label')" prop="recipientEmail" required class="w-1/2">
        <el-input size="large" v-model="formData.recipientEmail" :placeholder="$t('Edm.testEmail.form.recipient.placeholder')" class="w-full" />
      </el-form-item>

      <el-form-item :label="$t('Edm.testEmail.form.content.label')" prop="content" required class="w-full">
        <TinyMce v-model="formData.content" :api-key="apiKey" :init="config" />
      </el-form-item>

      <div class="flex justify-center">
        <el-button size="large" type="primary" :loading="loading" @click="handleSubmit">
          {{ loading ? $t('Edm.testEmail.buttons.sending') : $t('Edm.testEmail.buttons.send') }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import TinyMce from '@/module/Editor/views/components/tinyMceCFM.vue'
import http from '/admin/support/http'

const { t } = useI18n()

interface FormData {
  smtpProvider: string
  subject: string
  recipientEmail: string
  content: string
}

const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = reactive<FormData>({
  smtpProvider: '',
  subject: '',
  recipientEmail: '',
  content: '',
})

// SMTP服务商选项
const smtpOptions = [
  { label: 'Brevo', value: 'brevo' },
  { label: 'Gmail', value: 'gmail' },
  { label: 'Mailchimp', value: 'mailchimp' },
  { label: 'Other', value: 'other' },
]

const filteredOptions = ref(smtpOptions)

// 过滤SMTP选项
const filterSmtpOptions = (query: string) => {
  if (query) {
    filteredOptions.value = smtpOptions.filter(item => {
      return item.label.toLowerCase().includes(query.toLowerCase())
    })
  } else {
    filteredOptions.value = smtpOptions
  }
}

// 邮箱验证规则
const emailValidator = (rule: any, value: string, callback: any) => {
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
  if (!value) {
    callback(new Error(t('Edm.testEmail.validation.email.required')))
  } else if (!emailRegex.test(value)) {
    callback(new Error(t('Edm.testEmail.validation.email.invalid')))
  } else {
    callback()
  }
}

const rules = computed<FormRules>(() => ({
  smtpProvider: [{ required: true, message: t('Edm.testEmail.validation.smtpProvider'), trigger: 'change' }],
  subject: [
    { required: true, message: t('Edm.testEmail.validation.subject.required'), trigger: 'blur' },
    { min: 1, max: 100, message: t('Edm.testEmail.validation.subject.length'), trigger: 'blur' },
  ],
  recipientEmail: [{ required: true, validator: emailValidator, trigger: 'blur' }],
  content: [
    { required: true, message: t('Edm.testEmail.validation.content.required'), trigger: 'blur' },
    { min: 1, max: 5000, message: t('Edm.testEmail.validation.content.length'), trigger: 'blur' },
  ],
}))

// 添加 API 调用相关的类型定义
interface SmtpTestRequest {
  serviceProvider: string
  smtpTestSubject: string
  smtpTestEmail: string
  smtpTestContent: string
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    loading.value = true
    await formRef.value.validate()

    // 构造请求数据
    const requestData: SmtpTestRequest = {
      serviceProvider: formData.smtpProvider,
      smtpTestSubject: formData.subject,
      smtpTestEmail: formData.recipientEmail,
      smtpTestContent: formData.content,
    }

    // 发送测试邮件
    await http.post('/edm/smtp_test', requestData)
    ElMessage.success(t('Edm.testEmail.messages.success'))
  } catch (error) {
    console.error('发送邮件失败:', error)
    ElMessage.error(t('Edm.testEmail.messages.error'))
  } finally {
    loading.value = false
  }
}

const apiKey = (window as any).VITE_TINYMCE_KEY
const config = computed(() => {
  let toolbar: string[] = []

  toolbar = [
    'undo redo restoredraft cut copy paste pastetext forecolor backcolor bold italic underline strikethrough link anchor alignleft aligncenter alignright alignjustify outdent indent bullist numlist blockquote subscript superscript removeformat styleselect formatselect fontselect fontsizeselect table upload image axupimgs media emoticons charmap hr pagebreak insertdatetime selectall visualblocks searchreplace code print preview indent2em fullscreen',
  ]

  let plugins =
    'preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount autosave emoticons'

  plugins = 'visualblocks'

  const mediaUpload = true
  const delayInit = false

  return {
    language: 'zh_CN',
    placeholder: '在这里输入内容',
    plugins: plugins,
    toolbar: toolbar,
    branding: false,
    automatic_uploads: mediaUpload,
    init_instance_callback: function (editor: any) {
      if (delayInit) {
        editor.setProgressState(true)
        setTimeout(function () {
          editor.setProgressState(false)
        }, 1000)
      }
    },
  }
})
</script>

<style scoped lang="scss">
.test-email-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  
}
</style>