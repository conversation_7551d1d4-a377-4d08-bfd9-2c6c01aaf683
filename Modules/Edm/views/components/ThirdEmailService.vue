<template>
<div class="table-page">
  <div class="content-box">
    <el-form label-position="top">
      <el-form-item :label="$t('Edm.thirdParty.enable')">
        <el-switch v-model="isThirdPartyEnabled"></el-switch>
      </el-form-item>

      <div class="mb-8">
        <el-button-group>
          <el-button :disabled="!isThirdPartyEnabled" size="large" :type="activeTab === 'brevo' ? 'primary' : 'default'" @click="activeTab = 'brevo'">Brevo</el-button>
          <el-button :disabled="!isThirdPartyEnabled" size="large" :type="activeTab === 'mailgun' ? 'primary' : 'default'" @click="activeTab = 'mailgun'">Mailgun</el-button>
          <el-button :disabled="!isThirdPartyEnabled" size="large" :type="activeTab === 'mailchimp' ? 'primary' : 'default'" @click="activeTab = 'mailchimp'">Mailchimp</el-button>
          <el-button :disabled="!isThirdPartyEnabled" size="large" :type="activeTab === 'sendgrid' ? 'primary' : 'default'" @click="activeTab = 'sendgrid'">Sendgrid</el-button>
        </el-button-group>
      </div>
    </el-form>

    <!-- Brevo 表单 -->
    <el-form v-if="activeTab === 'brevo'" :model="edmConfig['edm.brevo']" label-position="top" :disabled="!isThirdPartyEnabled">
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.address')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.brevo'].smtpHost" :placeholder="$t('Edm.thirdParty.smtp.placeholder.address')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.port')" class="w-1/2 pl-4" required>
          <el-input size="large" v-model="edmConfig['edm.brevo'].smtpPort" :placeholder="$t('Edm.thirdParty.smtp.placeholder.port')"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.username')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.brevo'].smtpUser" :placeholder="$t('Edm.thirdParty.smtp.placeholder.username')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.password')" class="w-1/2 pl-4" required>
          <el-input size="large" v-model="edmConfig['edm.brevo'].smtpPassword" type="password" :placeholder="$t('Edm.thirdParty.smtp.placeholder.password')"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.sender')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.brevo'].smtpFrom" :placeholder="$t('Edm.thirdParty.smtp.placeholder.sender')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.security')" class="w-1/2 pl-4">
          <el-radio-group v-model="edmConfig['edm.brevo'].smtpEncryption">
            <el-radio label="ssl">SSL</el-radio>
            <el-radio label="starttls">STARTTLS</el-radio>
            <el-radio label="none">None</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="flex justify-center">
        <el-button size="large" @click="saveAndTest">{{ $t('Edm.thirdParty.buttons.saveAndTest') }}</el-button>
        <el-button size="large" type="primary" @click="saveSettings">{{ $t('Edm.thirdParty.buttons.save') }}</el-button>
      </div>
    </el-form>

    <!-- Mailgun 表单 -->
    <el-form v-if="activeTab === 'mailgun'" :model="edmConfig['edm.mailgun']" label-position="top" :disabled="!isThirdPartyEnabled">
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.address')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailgun'].smtpHost" :placeholder="$t('Edm.thirdParty.smtp.placeholder.address')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.port')" class="w-1/2 pl-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailgun'].smtpPort" :placeholder="$t('Edm.thirdParty.smtp.placeholder.port')"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.username')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailgun'].smtpUser" :placeholder="$t('Edm.thirdParty.smtp.placeholder.username')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.password')" class="w-1/2 pl-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailgun'].smtpPassword" type="password" :placeholder="$t('Edm.thirdParty.smtp.placeholder.password')"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.sender')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailgun'].smtpFrom" :placeholder="$t('Edm.thirdParty.smtp.placeholder.sender')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.security')" class="w-1/2 pl-4">
          <el-radio-group v-model="edmConfig['edm.mailgun'].smtpEncryption">
            <el-radio label="ssl">SSL</el-radio>
            <el-radio label="starttls">STARTTLS</el-radio>
            <el-radio label="none">None</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="flex justify-center">
        <el-button size="large" @click="saveAndTest">{{ $t('Edm.thirdParty.buttons.saveAndTest') }}</el-button>
        <el-button size="large" type="primary" @click="saveSettings">{{ $t('Edm.thirdParty.buttons.save') }}</el-button>
      </div>
    </el-form>

    <!-- Mailchimp 表单 -->
    <el-form v-if="activeTab === 'mailchimp'" :model="edmConfig['edm.mailchimp']" label-position="top" :disabled="!isThirdPartyEnabled">
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.address')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailchimp'].smtpHost" :placeholder="$t('Edm.thirdParty.smtp.placeholder.address')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.port')" class="w-1/2 pl-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailchimp'].smtpPort" :placeholder="$t('Edm.thirdParty.smtp.placeholder.port')"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.username')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailchimp'].smtpUser" :placeholder="$t('Edm.thirdParty.smtp.placeholder.username')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.password')" class="w-1/2 pl-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailchimp'].smtpPassword" type="password" :placeholder="$t('Edm.thirdParty.smtp.placeholder.password')"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.sender')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.mailchimp'].smtpFrom" :placeholder="$t('Edm.thirdParty.smtp.placeholder.sender')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.security')" class="w-1/2 pl-4">
          <el-radio-group v-model="edmConfig['edm.mailchimp'].smtpEncryption">
            <el-radio label="ssl">SSL</el-radio>
            <el-radio label="starttls">STARTTLS</el-radio>
            <el-radio label="none">None</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="flex justify-center">
        <el-button size="large" @click="saveAndTest">{{ $t('Edm.thirdParty.buttons.saveAndTest') }}</el-button>
        <el-button size="large" type="primary" @click="saveSettings">{{ $t('Edm.thirdParty.buttons.save') }}</el-button>
      </div>
    </el-form>

    <!-- Sendgrid 表单 -->
    <el-form v-if="activeTab === 'sendgrid'" :model="edmConfig['edm.sendgrid']" label-position="top" :disabled="!isThirdPartyEnabled">
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.address')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.sendgrid'].smtpHost" :placeholder="$t('Edm.thirdParty.smtp.placeholder.address')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.port')" class="w-1/2 pl-4" required>
          <el-input size="large" v-model="edmConfig['edm.sendgrid'].smtpPort" :placeholder="$t('Edm.thirdParty.smtp.placeholder.port')"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.username')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.sendgrid'].smtpUser" :placeholder="$t('Edm.thirdParty.smtp.placeholder.username')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.password')" class="w-1/2 pl-4" required>
          <el-input size="large" v-model="edmConfig['edm.sendgrid'].smtpPassword" type="password" :placeholder="$t('Edm.thirdParty.smtp.placeholder.password')"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Edm.thirdParty.smtp.sender')" class="w-1/2 pr-4" required>
          <el-input size="large" v-model="edmConfig['edm.sendgrid'].smtpFrom" :placeholder="$t('Edm.thirdParty.smtp.placeholder.sender')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('Edm.thirdParty.smtp.security')" class="w-1/2 pl-4">
          <el-radio-group v-model="edmConfig['edm.sendgrid'].smtpEncryption">
            <el-radio label="ssl">SSL</el-radio>
            <el-radio label="starttls">STARTTLS</el-radio>
            <el-radio label="none">None</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="flex justify-center">
        <el-button size="large" @click="saveAndTest">{{ $t('Edm.thirdParty.buttons.saveAndTest') }}</el-button>
        <el-button size="large" type="primary" @click="saveSettings">{{ $t('Edm.thirdParty.buttons.save') }}</el-button>
      </div>
    </el-form>
  </div>
</div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { env, getAuthToken } from '/admin/support/helper'

const { t } = useI18n()

interface SmtpConfig {
  serviceProvider: string
  smtpHost?: string
  smtpPort?: string
  smtpUser: string
  smtpPassword: string
  smtpEncryption?: string
  smtpFromName?: string
  smtpFrom: string
}

// 添加接口定义
interface ConfigData {
  'edm.useProvider': boolean
  [key: string]: boolean | string // 添加索引签名
}

// 在 SmtpConfig 接口定义后添加
interface EdmConfig {
  'edm.useProvider': boolean
  'edm.brevo': SmtpConfig
  'edm.mailgun': SmtpConfig
  'edm.mailchimp': SmtpConfig
  'edm.sendgrid': SmtpConfig
}

// 修改 edmConfig 的类型声明
const edmConfig = ref<EdmConfig>({
  'edm.useProvider': false,
  'edm.brevo': {
    serviceProvider: 'brevoSMTP',
    smtpHost: '',
    smtpPort: '',
    smtpUser: '',
    smtpPassword: '',
    smtpEncryption: '',
    smtpFrom: '',
  },
  'edm.mailgun': {
    serviceProvider: 'mailgunSMTP',
    smtpHost: '',
    smtpPort: '',
    smtpUser: '',
    smtpPassword: '',
    smtpEncryption: '',
    smtpFrom: '',
  },
  'edm.mailchimp': {
    serviceProvider: 'mailchimpSMTP',
    smtpHost: '',
    smtpPort: '',
    smtpUser: '',
    smtpPassword: '',
    smtpEncryption: '',
    smtpFrom: '',
  },
  'edm.sendgrid': {
    serviceProvider: 'sendgridSMTP',
    smtpHost: '',
    smtpPort: '',
    smtpUser: '',
    smtpPassword: '',
    smtpEncryption: '',
    smtpFrom: '',
  },
})

const activeTab = ref('brevo')
const isThirdPartyEnabled = computed({
  get: () => edmConfig.value['edm.useProvider'],
  set: val => (edmConfig.value['edm.useProvider'] = val),
})

// 获取配置
const getConfig = async () => {
  try {
    const response = await http.get('/edm/smtp_config')
    if (response.data.code === 200) {
      const configs = response.data.data
      edmConfig.value['edm.useProvider'] = configs['edm.useProvider']

      // 统一处理所有服务商配置
      const providers = ['mailgun', 'brevo', 'mailchimp', 'sendgrid']

      providers.forEach(provider => {
        const configKey = `edm.${provider}`
        const config = configs[configKey]

        if (config) {
          try {
            // 如果是字符串且不为空，尝试解析 JSON
            if (typeof config === 'string' && config !== '') {
              edmConfig.value[configKey] = JSON.parse(config)
            }
            // 如果是对象，直接赋值
            else if (typeof config === 'object' && config !== null) {
              edmConfig.value[configKey] = config
            }
          } catch (error) {
            console.warn(`Invalid ${provider} config format:`, error)
          }
        }
      })
    } else {
      ElMessage.error(response.data.message || t('Edm.thirdParty.messages.saveError'))
    }
  } catch (error) {
    console.error('获取配置失败:', error)
    ElMessage.error(t('Edm.thirdParty.messages.saveError'))
  }
}

// 获取当前配置的JSON格式
const getCurrentConfig = computed(() => {
  const provider = activeTab.value === 'brevo' ? 'edm.brevo' : `edm.${activeTab.value}`
  const providerConfig = edmConfig.value[provider as keyof EdmConfig] as SmtpConfig

  const configObject: SmtpConfig = {
    serviceProvider: providerConfig.serviceProvider,
    smtpHost: providerConfig.smtpHost || '',
    smtpPort: providerConfig.smtpPort || '',
    smtpUser: providerConfig.smtpUser,
    smtpPassword: providerConfig.smtpPassword,
    smtpEncryption: providerConfig.smtpEncryption || '',
    smtpFrom: providerConfig.smtpFrom,
  }

  const data: ConfigData = {
    'edm.useProvider': edmConfig.value['edm.useProvider'],
  }

  data[provider] = JSON.stringify(configObject)

  return data
})

// 保存设置
const saveSettings = async () => {
  try {
    const config = getCurrentConfig.value
    const currentProvider = activeTab.value === 'brevo' ? 'edm.brevo' : `edm.${activeTab.value}`
    const currentConfig: any = edmConfig.value[currentProvider as keyof typeof edmConfig.value]

    if (!currentConfig.smtpUser || !currentConfig.smtpPassword || !currentConfig.smtpFrom) {
      ElMessage.warning(t('Edm.thirdParty.messages.requiredFields'))
      return
    }
    const response = await http.post('/edm/smtp_config_save', config)

    if (response.data.code === 200) {
      ElMessage.success(t('Edm.thirdParty.messages.saveSuccess'))
    } else {
      ElMessage.error(response.data.message || t('Edm.thirdParty.messages.saveError'))
    }
  } catch (error: any) {
    console.error('完整错误信息:', error.response || error)
    ElMessage.error(error.response?.data?.message || t('Edm.thirdParty.messages.saveError'))
  }
}

// 保存并测试
const saveAndTest = async () => {
  try {
    await saveSettings()
    const response = await http.post('/edm/test_email', {
      service: activeTab.value,
    })

    if (response.data.code === 200) {
      ElMessage.success(t('Edm.thirdParty.messages.testSuccess'))
    } else {
      ElMessage.error(response.data.message || t('Edm.thirdParty.messages.testError'))
    }
  } catch (error) {
    ElMessage.error(t('Edm.thirdParty.messages.testError'))
    console.error(error)
  }
}

onMounted(() => {
  getConfig()
})
</script>

<style lang="scss" scoped>
.table-page {
  .content-box {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
  }
}
</style>
