import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  {
    path: '/edm',
    component: () => import('/admin/layout/index.vue'),
    meta: { title: 'EDM', icon: 'Tickets' },
    children: [
      {
        path: 'templates',
        name: 'templates',
        meta: { title: '邮箱设置', icon: 'Tickets' },
        component: () => import('./ui/index.vue'),
      },
      {
        path: 'template-detail/:id',
        name: 'templateDetail',
        meta: { title: 'Edm.Router.templateDetail', icon: 'Document', i18n: true },
        component: () => import('./components/TemplateDetail.vue'),
        props: route => ({
          fromCampaign: route.query.fromCampaign === 'true',
          campaignPath: route.query.campaignPath,
        }),
      },
      {
        path: 'contact',
        name: 'contact',
        meta: { title: '联系人', icon: 'Document' },
        component: () => import('./ui/contact/index.vue'),
      },
      {
        // '导入联系人'
        path: 'import',
        name: 'contact-import',
        meta: { title: 'Edm.router.contactImport', i18n: true },
        component: () => import('./ui/contact/pages/ContactImport.vue'),
      },
      {
        path: 'campaigns',
        children: [
          {
            path: '',
            name: 'campaigns',
            meta: { title: '营销活动' },
            component: () => import('./ui/campaigns/index.vue'),
          },
          {
            path: 'create',
            name: 'campaign-create',
            meta: { title: '创建活动' },
            component: () => import('./ui/campaigns/pages/CampaignCreate.vue'),
          },
        ],
      },
    ],
  },
]

export default router
