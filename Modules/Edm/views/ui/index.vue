<template>
  <div class="bwms-module table-page">
    <div class="module-header"></div>
    <div class="module-con">
      <!-- 邮件功能卡片区域 - 改为Tab式样式 -->
      <div class="tab-header-container">
        <div class="tab-nav">
          <div class="tab-item" 
               :class="{ active: activeTabName === '邮件模板' }"
               @click="activeTab('邮件模板')">
            {{ t('Edm.tabs.emailTemplate') }}
          </div>
          <div class="tab-item" 
               :class="{ active: activeTabName === '第三方邮件服务' }"
               @click="activeTab('第三方邮件服务')">
            {{ t('Edm.tabs.thirdPartyEmail') }}
          </div>
          <div class="tab-item" 
               :class="{ active: activeTabName === '邮件发送日志' }"
               @click="activeTab('邮件发送日志')">
            {{ t('Edm.tabs.emailLogs') }}
          </div>
          <div class="tab-item" 
               :class="{ active: activeTabName === '测试邮件' }"
               @click="activeTab('测试邮件')">
            {{ t('Edm.tabs.testEmail') }}
          </div>
        </div>
      </div>
      
      <!-- 功能内容区域 - 与当前选中标签对应 -->
      <div class="tab-content-box">
          <!-- 邮件模板内容 -->
          <div v-if="activeTabName === '邮件模板'" class="tab-content-box-scroll scroll-bar-custom">
            <!-- 骨架屏 -->
            <div v-if="loading">
              <div v-for="i in 4" :key="i" class="mb-8">
                <el-skeleton class="mb-3">
                  <template #template>
                    <el-skeleton-item variant="text" style="width: 30%; height: 24px" />
                  </template>
                </el-skeleton>
                <div class="card-grid">
                  <div v-for="j in 4" :key="j" class="card-skeleton">
                    <el-skeleton>
                      <template #template>
                        <el-skeleton-item variant="rect" style="width: 100%; height: 120px" />
                      </template>
                    </el-skeleton>
                  </div>
                </div>
              </div>
            </div>

            <!-- 实际内容 -->
            <template v-else>
              <div v-for="tmp in formattedTemplateList" :key="tmp.type" class="template-section">
                <div class="section-title">{{ tmp.displayName }}</div>
                <div class="card-grid">
                    <div v-for="item in tmp.templates" :key="item.id" 
                      class="template-card" 
                      @click="handleEditTemplate(item, tmp.displayName)">
                    <div class="template-card-icon" :class="getIconClass(item.id)">
                      <el-icon size="24">
                        <component :is="getIconType(item.id)" />
                      </el-icon>
                    </div>
                    <div class="template-card-content">
                      <div class="template-card-title">{{ item.name }}</div>
                      <div class="template-card-desc">{{ item.description || t('Edm.template.edit') }}</div>
                    </div>
                    <div class="template-card-arrow">
                      <el-icon><ArrowRight /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          
          <!-- 第三方邮件服务内容 -->
          <div v-if="activeTabName === '第三方邮件服务'" class="tab-content-box-scroll scroll-bar-custom">
            <ThirdEmailService  />
          </div>
          
          <!-- 邮件日志内容 -->
          <div v-if="activeTabName === '邮件发送日志'" class="tab-content-box-scroll scroll-bar-custom">
            <EmailLogs />
          </div>
          
          <!-- 测试邮件内容 -->
          <div v-if="activeTabName === '测试邮件'" class="tab-content-box-scroll scroll-bar-custom">
            <TestEmail />
          </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { 
  ArrowRight, Document, Message, Close, Bell, Key, Connection,
  Calendar, CreditCard, ShoppingCart, Goods, Picture, Collection,
  Histogram, Star, ChatLineSquare, Opportunity, Shop, Reading, 
  User, Trophy, TrendCharts, ScaleToOriginal, Promotion, Notebook,
  Monitor, List, Link, Refrigerator, Postcard, Phone, PieChart, Platform
} from '@element-plus/icons-vue'
import ThirdEmailService from '../components/ThirdEmailService.vue'
import EmailLogs from '../components/EmailLogs.vue'
import TestEmail from '../components/TestEmail.vue'
import { useTemplateStore } from '../stores/template'
import http from '/admin/support/http'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const templateStore = useTemplateStore()

// 活动标签和显示控制
const activeTabName = ref('邮件模板')
const showTabContent = ref(true)
const loading = ref(true)
const templateList = ref<any[]>([])

// 添加图标类型和样式的辅助函数
const getIconType = (id: number | string) => {
  // 使用固定的随机图标集，不依赖动态数据
  const icons = [
    'Document', 'Calendar', 'CreditCard', 'ShoppingCart', 'Goods',
    'Picture', 'Collection', 'Histogram', 'Star', 'ChatLineSquare',
    'Opportunity', 'Shop', 'Reading', 'User', 'Trophy', 'TrendCharts',
    'ScaleToOriginal', 'Promotion', 'Notebook', 'Monitor', 'List', 
    'Link', 'Refrigerator', 'Postcard', 'Phone', 'PieChart', 'Platform'
  ]
  
  // 将id转为数字
  const numId = typeof id === 'string' ? parseInt(id, 10) || 0 : id || 0
  
  // 根据id选择固定的图标，使同一模板始终使用相同图标
  const index = numId % icons.length
  return icons[index]
}

const getIconClass = (id: number | string) => {
  // 定义一组颜色类名
  const colorClasses = [
    'icon-blue', 'icon-orange', 'icon-green', 'icon-purple',
    'icon-cyan', 'icon-pink', 'icon-yellow', 'icon-red'
  ]
  
  // 将id转为数字
  const numId = typeof id === 'string' ? parseInt(id, 10) || 0 : id || 0
  
  // 根据id选择固定的颜色类
  const index = numId % colorClasses.length
  return colorClasses[index]
}

// 修改activeTab函数，总是显示内容
const activeTab = (tabName: string) => {
  activeTabName.value = tabName
  showTabContent.value = true
  
  if (tabName === '邮件模板' && templateList.value.length === 0) {
    fetchTemplateList()
  }
}

// 关闭标签内容
const closeTab = () => {
  showTabContent.value = false
}

// 格式化模板名称
const getDisplayName = (name: string): string => {
  const typeMap: Record<string, string> = {
    'Modules\\Edm\\Enums\\MessageType.NOTIFICATION': t('Edm.template.types.notification'),
    'Modules\\Edm\\Enums\\MessageType.VERIFICATION': t('Edm.template.types.verification'),
    'Modules\\Edm\\Enums\\MessageType.PASSWORD': t('Edm.template.types.password'),
    'Modules\\Edm\\Enums\\MessageType.EMAIL_BINDING': t('Edm.template.types.emailBinding'),
    'Modules\\Edm\\Enums\\MessageType.VALIDATION': t('Edm.template.types.validation'),
    'Modules\\Edm\\Enums\\MessageType.REGISTER_LOGIN': t('Edm.template.types.registerLogin'),
  }
  return typeMap[name] || name
}

// 格式化后的模板列表
const formattedTemplateList = computed(() => {
  return templateList.value
    .map(item => ({
      ...item,
      displayName: getDisplayName(item.name),
    }))
    .filter(item => item.templates && item.templates.length > 0);
})

// 获取模板列表
const fetchTemplateList = async () => {
  try {
    loading.value = true
    const { data } = await http.get('/edm/templates')

    if (data.code === 200) {
      templateList.value = data.data.list
    } else {
      ElMessage.error(data.message || t('Edm.messages.fetchError'))
    }
  } catch (error) {
    console.error('获取模板列表出错:', error)
    ElMessage.error(t('Edm.messages.fetchError'))
  } finally {
    loading.value = false
  }
}

// 修改模板点击处理函数
const handleEditTemplate = (template: any, category: string) => {
  const templateData = {
    id: `${template.id}`,
    name: template.name,
    category,
    isSystem: false,
    type: 'CUSTOM',
    subject: '',
    sender: '',
    hasURL: false,
    tplEngine: 'handlebar',
    content: '',
    isEnabled: true,
  }

  // 根据来源决定不同的路由参数
  router.push({
    name: 'templateDetail',
    params: {
      id: template.id === undefined ? 'new' : template.id,
    },
    query: {
      ...(isFromCampaign.value
        ? {
            fromCampaign: 'true',
          }
        : {}),
    },
    state: { template: templateData },
  })
}

// 判断是否来自活动创建页面
const isFromCampaign = computed(() => route.query.fromCampaign === 'true')

onMounted(() => {
  // 初始化时加载邮件模板列表
  fetchTemplateList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  
  .module-con {
    
    .tab-header-container {
      margin-bottom: 20px;
      
      .tab-nav {
        display: flex;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        border: 1px solid #f0f0f0;
        width: fit-content;
        
        .tab-item {
          padding: 12px 40px;
          font-size: 18px;
          color: #000;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: #f5f7fa;
          }
          
          &.active {
            background: #032f6e;
            color: #fff;
          }
        }
      }
    }
    
    .tab-content-box {
      overflow: hidden;
    }
    .tab-content-box-scroll {
      height: 100%;
      &::-webkit-scrollbar-thumb {
        background-color: transparent;
      
      &:hover {
        background-color: transparent;
      }
    }
    }
  }
}

.template-section {
  margin-bottom: 26px;
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  
  &:last-child {
    margin-bottom: 0;
  }
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
  }
  
  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
  
  .template-card {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20px;
    background: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      
      .template-card-arrow {
        opacity: 1;
        transform: translateX(0);
      }
    }
    
    .template-card-icon {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
      margin-right: 16px;
      
      &.icon-blue {
        background: #eaf4ff;
        color: #418ae2;
      }
      
      &.icon-orange {
        background: #fff0e6;
        color: #ff7a45;
      }
      
      &.icon-green {
        background: #f6ffed;
        color: #52c41a;
      }
      
      &.icon-purple {
        background: #f9f0ff;
        color: #722ed1;
      }
      
      &.icon-cyan {
        background: #e6fffb;
        color: #13c2c2;
      }
      
      &.icon-pink {
        background: #fcf4f6;
        color: #eb2f96;
      }
      
      &.icon-yellow {
        background: #fffbe6;
        color: #faad14;
      }
      
      &.icon-red {
        background: #fff1f0;
        color: #f5222d;
      }
    }
    
    .template-card-content {
      flex: 1;
      
      .template-card-title {
        font-size: 15px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }
      
      .template-card-desc {
        font-size: 13px;
        color: #888;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    
    .template-card-arrow {
      position: absolute;
      top: 50%;
      right: 16px;
      transform: translateY(-50%) translateX(5px);
      opacity: 0;
      transition: all 0.3s ease;
      color: #418ae2;
    }
  }
}

.card-skeleton {
  padding: 8px;
}
</style>
