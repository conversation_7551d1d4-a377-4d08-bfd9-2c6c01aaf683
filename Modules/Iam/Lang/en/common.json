{"login": {"title": "Welcome back! Login to your account", "register": "Register", "loading": "Loading...", "verificationCodeLogin": "Code Login", "passwordLogin": "Password Login", "emailCodeLogin": "Email Code Login", "inputPhoneOrEmail": "Please input phone number or email", "inputVerifyCode": "Please input verification code", "inputCredential": "Please input username/email/phone", "inputPassword": "Please input password", "selectLanguage": "Select Language", "inputCaptcha": "Please input captcha", "remember": "Remember me", "forgot": "Forgot password?", "submit": "<PERSON><PERSON>", "or": "OR", "continueWithGoogle": "Continue with Google", "resetPassword": "Reset Password", "nextStep": "Next Step", "backToLogin": "Back to Login", "phoneRegister": "Phone Register", "emailCodeRegister": "Email Code Register", "emailPasswordRegister": "Email Password Register", "usernamePasswordRegister": "Username Password Register", "inputEmail": "Please input email", "inputUsername": "Please input username", "confirmPassword": "Please confirm password", "passwordStrength": "Password Strength", "noAccount": "No account?", "hasAccount": "Already have an account?", "registerNow": "Register Now", "loginNow": "Login Now", "googleLoginFailed": "Google login failed", "mainland": "Simplified Chinese", "hongkong": "Traditional Chinese", "username": "Username", "password": "Password", "verifyCode": "Verification Code", "sendCode": "Send Code", "resendCode": "Resend in {time}s", "phoneLogin": "Phone Login", "emailLogin": "<PERSON><PERSON>", "passwordNotMatch": "The two passwords do not match", "registerSuccess": "Registration successful", "loginSuccess": "Login successful", "mfaVerify": "Two-step Verification", "mfaTitle": "Two-step Verification · Google Authenticator", "invalidCredential": "Please input valid phone number, email or username", "passwordLength": "Password length must be at least 6 characters", "verifyCodeLength": "Verification code must be 6 digits", "sendingCode": "Sending...", "codeSent": "Verification code sent", "sendFailed": "Send failed, please retry", "loginFailed": "<PERSON><PERSON> failed, please check input", "networkError": "Network error, please try again later", "weak": "Weak", "medium": "Medium", "strong": "Strong", "googleLogin": "Sign in with Google", "countryCode": "Country/Region Code", "welcomeBack": "Please select login method: ", "createAccount": "Please select register method: "}, "userCenter": {"title": "User Center", "menu": {"language": "Language", "profile": "Profile", "changePassword": "Change Password", "logout": "Logout"}, "sidebar": {"personalInfo": "Personal Info", "accountBinding": "Account Binding", "multiFactorAuth": "Multi-Factor Auth", "passkey": "Passkey", "socialIdentity": "Social Identity", "enterpriseIdentity": "Enterprise Identity", "accountSafety": "Account Safety", "accessLog": "Access Log"}, "personal": {"title1": "Personal Information", "edit": "Edit", "save": "Save", "cancel": "Cancel", "userId": "User ID", "username": "Username", "email": "Email", "phone": "Phone", "name": "Name", "id": "id", "title": "Title", "address": "Address", "gender": "Gender", "birthdate": "Birthdate", "company": "Company", "signed_up": "Registration Date", "registrationDate": "Registration Date", "identityNumber": "ID Number", "updateSuccess": "Update successful", "updateFailed": "Update failed, please try again later", "fetchFailed": "Failed to get user information, please try again later", "genderOptions": {"unknown": "Unknown", "male": "Male", "female": "Female"}}, "binding": {"phone": {"title": "Phone & Email", "modify": "Modify", "unbind": "Unbind", "bind": "Bind", "current": "Current Phone", "new": "New Phone", "verificationCode": "Verification Code", "send": "Send Code", "resend": "Resend in {time}s", "oldCode": "Old Phone Verification Code", "newCode": "New Phone Verification Code", "inputCode": "Please input 6-digit verification code", "inputPhone": "Please input phone number", "inputNewPhone": "Please input new phone number", "unbindTitle": "Unbind Phone", "smsCode": "SMS Verification Code"}, "email": {"title": "Email Verification", "current": "Current Email", "new": "New Email", "oldCode": "Current Email Verification Code", "newCode": "New Email Verification Code", "unbindTitle": "<PERSON><PERSON><PERSON>", "emailCode": "Email Verification Code", "inputEmail": "Please input email", "inputNewEmail": "Please input new email"}, "messages": {"fillRequired": "Please fill in all required information", "invalidPhone": "Please input valid phone number format", "invalidEmail": "Please input valid email format", "updateSuccess": "Update successful", "updateFailed": "Update failed, please check the information", "unbindSuccess": "Unbind successful", "unbindFailed": "Unbind failed, please check the verification code", "codeSent": "Verification code sent", "sendFailed": "Failed to send verification code, please try again later", "inputCode": "Please input verification code"}}, "mfa": {"title": "Multi-Factor Authentication", "sms": {"title": "SMS Verification", "description": "Use SMS verification code for login authentication", "bind": "Bind Phone Number", "inputPhone": "Input Phone Number", "verifyCode": "SMS Verification Code", "inputCode": "Please input 6-digit code", "sendCode": "Send Code", "resendCode": "Resend in {time}s"}, "email": {"title": "Email Verification", "description": "Use email verification code for login authentication", "bind": "Bind <PERSON><PERSON>", "inputEmail": "Input Email Address", "verifyCode": "Email Verification Code"}, "otp": {"title": "OTP Authentication", "description": "Use one-time password for login authentication", "setup": {"title": "Bind OTP Token", "step1": "Download Authenticator", "step2": "Scan QR Code", "step3": "Enter Security Code", "step4": "Unbind OTP"}}, "passkey": {"title": "Create Passkey", "description": "With Passkey, you can use your fingerprint, face, screen settings or physical security key", "noData": "No Data", "warning": "Please only set up Passkey on your own devices", "create": "Create Passkey", "createSuccess": "Passkey created successfully"}, "messages": {"bindSuccess": "Binding successful", "bindFailed": "Binding failed, please try again", "unbindSuccess": "Unbinding successful", "unbindFailed": "Unbinding failed, please try again", "codeSent": "Verification code sent", "sendFailed": "Failed to send verification code, please try again later", "inputRequired": "Please fill in all required information"}}, "security": {"title": "Account Security", "score": "Security Score", "level": {"low": "Low", "medium": "Medium", "high": "High"}, "password": {"title": "Password Settings", "strength": "Password Strength", "weak": "Weak", "medium": "Medium", "strong": "Strong", "modify": "Modify", "old": "Current Password", "new": "New Password", "confirm": "Confirm Password", "changeSuccess": "Password changed successfully, please login again", "changeFailed": "Password change failed, please try again", "description": "It is recommended to use a more complex password.", "modifyNow": "Modify Now"}, "deleteAccount": {"title": "Delete Account", "description": "Permanently delete account and all data, please be careful", "warning": "After deletion, all data of this account will be permanently deleted and cannot be recovered!", "button": "Delete", "confirm": "Confirm", "cancel": "Cancel", "inputAccount": "Please input current account", "inputPassword": "Please input current password", "inputPhone": "Please input phone number", "inputCode": "Please input verification code", "sendCode": "Send Code", "resendCode": "Resend in {time}s", "success": "Account deletion request submitted", "failed": "Account deletion failed, please check input information"}, "mfa": {"title": "Multi-Factor Authentication (MFA)", "enabled": "Enabled", "disabled": "Disabled", "enable": "Enable", "disable": "Disable"}, "email": {"title": "Email Binding", "bound": "Bound", "unbound": "Unbound", "modify": "Modify", "bind": "Bind"}, "phone": {"title": "Phone Binding", "bound": "Bound", "unbound": "Unbound", "modify": "Modify", "bind": "Bind"}, "loadFailed": "Failed to load security information, please try again later"}, "log": {"title": "Access Log", "time": "Login Time", "user": "Login User", "ip": "IP Address", "status": "Status", "browser": "Browser", "platform": "Platform", "success": "Success", "failed": "Failed", "fetchFailed": "Failed to fetch login history, please try again later"}, "avatar": {"uploadSuccess": "Avatar uploaded successfully", "uploadFailed": "Avatar upload failed, please try again", "formatError": "Avatar image can only be JPG/PNG/GIF/BMP/WebP format!", "sizeError": "Avatar image size cannot exceed 10MB!"}, "enterpriseIdentity": {"title": "Enterprise Accounts", "account": "Account", "status": "Status", "bindAccount": "Bound Account", "operation": "Operation", "bind": "Bind", "unbind": "Unbind", "normal": "Normal", "abnormal": "Abnormal", "notBound": "Not Bound", "syncNow": "Syncing {name}", "viewDetails": "View details of {name}", "fetchFailed": "Failed to fetch enterprise account list"}}, "home": {"header": {"congratulations": "Congratulations on completing authentication!", "description": "After reading this tutorial, you can start understanding and practicing authentication flow best practices in your new application.", "userCenter": "User Center", "logout": "Logout"}, "logout": {"title": "Confirm Logout?", "message": "After logging out, you will return to the registration/login page", "cancel": "Cancel", "confirm": "Confirm"}, "cards": {"expand": "Expand", "collapse": "Collapse", "token": {"title": "View Token Credentials", "description": "Token is a user authentication credential for accessing protected resources.", "details": "The token returned contains id_token and access_token. For differences between them, please refer to the <a href=\"#\">documentation</a>. If you need to obtain refresh_token, please refer to the <a href=\"#\">documentation</a>."}, "userInfo": {"title": "View User Information", "description": "You can obtain user information through Token. User information complies with OIDC standards.", "details": "Use access_token to call the user information endpoint to get detailed user information. For details, please refer to the <a href=\"#\">documentation</a>."}, "callback": {"title": "Handle Callbacks", "description": "If you have a backend service, it is recommended to handle callbacks on the backend.", "details": "Handling callbacks on the backend can improve security and avoid exposing sensitive information on the frontend. For detailed callback handling process, please refer to the <a href=\"#\">documentation</a>."}, "noBackend": {"title": "No Backend Scenario", "description": "Suitable for scenarios without backend, where all processing is done on the frontend.", "details": "In scenarios without backend, special attention should be paid to frontend security. It is recommended to use PKCE flow to enhance security. For details, please refer to the <a href=\"#\">documentation</a>."}, "faq": {"title": "FAQ", "description": "You can refer to these common questions or use advanced features.", "details": "We have compiled some common questions and answers to help you quickly solve problems. If you have any other questions, please contact our support team."}, "next": {"title": "What You Might Need Next", "description": "After reading this tutorial, you can try more advanced operations.", "details": "Next, you can try the following operations:<br>1. Integrate into your application<br>2. Configure custom login page<br>3. Set up multi-factor authentication<br>4. Manage user permissions"}}}, "to": "to", "startDate": "Start Date", "endDate": "End Date", "search": "Search", "account": "Account", "status": "Status", "normal": "Normal", "abnormal": "Abnormal", "bound": "Bound", "unbound": "Unbound", "otp": {"bind_otp_title": "Bind OTP Token", "step_download": "Download Authenticator", "step_scan": "Scan QR Code", "step_verify": "Enter Security Code", "step_unbind": "Unbind OTP", "scan_download_tip": "Please scan the QR code to download the authenticator", "scan_qrcode": "Scan QR Code", "scan_qrcode_tip": "Please open Google Authenticator / Microsoft Authenticator on your phone to scan and add security code", "enter_code": "Enter Security Code", "enter_code_tip": "View and enter the 6-digit security code from your phone, then proceed to the next step", "unbind_title": "Unbind OTP", "unbind_tip": "Please enter your OTP verification code to unbind the current device", "confirm_unbind": "I confirm to unbind OTP", "unbind": "Unbind OTP", "bind_success": "OTP bound successfully", "bind_failed": "OTP verification failed, please try again", "unbind_success": "OTP unbound successfully", "unbind_failed": "OTP unbinding failed, please try again", "enter_6_digits": "Please enter 6-digit verification code", "bind_phone_email_first": "Please bind your phone number and email before binding OTP", "bind_phone_first": "Please bind your phone number before binding OTP", "bind_email_first": "Please bind your email before binding OTP", "get_qrcode_failed": "Failed to get OTP QR code, please try again later", "scan_download": "Download"}, "actions": {"back": "Back", "next": "Next", "previous": "Previous"}, "routes": {"settings": "Settings", "frontend": "Frontend Settings", "backend": "Backend Settings", "identity": {"title": "Identity Sources", "enterprise": "Enterprise Identity Source", "enterpriseTmpList": "Enterprise Identity Source Templates", "enterpriseTmpListDetail": "Enterprise Identity Source Template Details", "enterpriseCreate": "Create Enterprise Identity Source", "enterpriseDetail": "Enterprise Identity Source Details", "social1": "Social Identity Source", "social": {"title": "Social Identity Sources", "emptyDescription": "Connect to WeChat, GitLab, Alipay and other social login software to enable your application to use these methods for authentication and authorization login.", "createSource": "Create Social Identity Source", "identitySource": "Identity Source", "sourceName": "Source Name", "sourceId": "Source ID", "confirmOperation": "Confirm Operation", "confirmDelete": "Are you sure you want to delete this social identity source?", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed, please try again", "fetchFailed": "Failed to fetch social identity source list"}, "socialTmpList": "Social Identity Source Templates", "socialTmpListDetail": "Social Identity Source Template Details", "socialCreate": "Create Social Identity Source", "identitySource": "Identity Source", "sourceName": "Source Name", "sourceId": "Source ID", "confirmOperation": "Confirm Operation", "confirmDelete": "Are you sure you want to delete this enterprise identity source?", "createSource": "Create Enterprise Identity Source", "emptyDescription": "Connect to standard protocols or enterprise software like Feishu, WeChat Work, DingTalk, etc., to enable authentication and authorization login for your application.", "loginMode": "Login Mode", "scenario": "<PERSON><PERSON><PERSON>", "cas": {"appLogo": "Application Logo", "logoTip": "If set, the Authing login form will display this icon on the \"Login with {Display Name}\" button, which will be displayed as 20 * 20.", "uniqueId": "Unique Identifier", "inputUniqueId": "Please enter unique identifier", "displayName": "Display Name", "inputDisplayName": "Windows AD", "casAuthUrl": "CAS Authentication URL", "casAuthUrlPlaceholder": "Please enter the authentication endpoint of the external CAS identity provider", "casTicketUrl": "CAS Ticket Validation URL", "casTicketUrlPlaceholder": "Please enter the ticket validation endpoint of the external CAS identity provider", "loginMode": "Login Mode", "loginAndRegister": "Available for login and registration", "loginOnly": "Login only", "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "uploadFailed": "Upload failed"}, "ldap": {"basicConfig": "Basic Configuration", "appLogo": "Application Logo", "logoTip": "If set, the Authing login form will display this icon on the \"Login with {Display Name}\" button, which will be displayed as 20 * 20.", "uniqueId": "Unique Identifier", "inputUniqueId": "Please enter unique identifier", "displayName": "Display Name", "inputDisplayName": "Windows AD", "ldapUrl": "LDAP Connection", "ldapUrlPlaceholder": "Please enter the LDAP server address and port, e.g.: ldap://dc.fabrikam.com:389", "bindDNPlaceholder": "Please enter the username used to connect to LDAP. This username will be used to test the connection results and search for users or user groups", "bindDNPassword": "Bind DN Password", "bindDNPasswordPlaceholder": "Please enter the password used to connect to LDAP. This password will be encrypted and stored in the database", "usersDNPlaceholder": "Define where to start searching, e.g.: dc=fabrikam,dc=local", "queryCriteria": "Query Criteria", "queryCriteriaPlaceholder": "This is an LDAP service used for testing. The query criteria of this test LDAP configuration is used to find user information to obtain user dn information.", "queryCriteriaTip": "Query criteria, which is combined with bindDN and corresponding secret to find users, used to retrieve user dn information, combined with user password for ldap authentication. Supports custom filter expressions, basic form: &(objectClass=organizationalPerson)(cn=%s), where %s will be replaced by the username filled in by the user when logging in. For example, if you want to log in through the user's cn, you can fill in &(objectClass=organizationalPerson)(cn=%s); if you want to log in through the user's mail, you can fill in &(objectClass=organizationalPerson)(mail=%s).", "loginMode": "Login Mode", "loginAndRegister": "Available for login and registration", "loginOnly": "Login only", "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "connectionTest": "LDAP Connection Test", "test": "Test", "testAccount": "Test Account", "testPassword": "Test Password", "connectivityTest": "Connectivity Test", "accountPasswordTest": "Account Password Test", "notTested": "Not Tested", "uploadFailed": "Upload failed"}, "oidc": {"appLogo": "Application Logo", "logoTip": "If set, the Authing login form will display this icon on the \"Login with {Display Name}\" button, which will be displayed as 20 * 20.", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "Please enter display name", "mode": "Mode", "modeOptions": {"frontend": "Frontend Mode", "backend": "Backend Mode"}, "modeTip": "Frontend mode uses response_mode=form_post and response_type=id_token mode, backend mode uses response_type=code authorization code mode", "issuerUrl": "Issuer URL", "issuerUrlPlaceholder": "id.mydomain.com/.well-known/openid-configuration", "issuerUrlTip": "The Issuer URL of the OpenID Connect provider you want to connect to", "clientId": "Client ID", "clientIdPlaceholder": "Please enter the Client ID of the OpenID Connect provider you want to connect to", "clientSecret": "Client Secret", "clientSecretPlaceholder": "Please enter the Client Secret of the OpenID Connect provider you want to connect to", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts."}, "saml": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "appLogo": "Application Logo", "signCert": "Signature Certificate", "signCertPlaceholder": "Please enter the SAML Response signature certificate from external SAML provider", "loginUrl": "Login URL", "loginUrlPlaceholder": "Please enter the authentication endpoint of external SAML provider", "samlRequestSign": "SAML Request Signature", "yes": "Yes", "no": "No", "samlRequestSignAlgo": "SAML Request Signature Algorithm", "samlRequestSummaryAlgo": "SAML Request Digest Algorithm", "samlRequestProtocolBind": "SAML Request Protocol Binding", "singleLogout": "Single Logout", "logoutUrl": "Logout URL", "logoutUrlBind": "Logout URL Binding", "acsUrl": "ACS URL", "metadataXml": "Metadata XML File", "fieldMappAttr": "Field Mapping Attribute", "loginMode": "Login Mode", "loginModeOptions": {"loginAndRegister": "Available for login and registration", "loginOnly": "Login only"}, "loginOnlyTip": "After enabling 'Login Only Mode', users can only login to existing accounts and cannot create new accounts. Please choose carefully.", "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "associationMethod": "Association Method", "associationMethodOptions": {"fieldMatch": "Field Match"}, "fieldType": "Field Type", "fieldTypeOptions": {"email": "Email", "phone": "Phone", "username": "Username", "externalId": "Original System ID (externalId)"}, "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}, "validation": {"uniqueIdRequired": "Please enter unique identifier", "displayNameRequired": "Please enter display name", "signRequired": "Please enter signature certificate", "loginUrlRequired": "Please enter login URL", "logoutUrlRequired": "Please enter logout URL", "logoutUrlBindRequired": "Please select logout URL binding"}}, "windows": {"appLogo": "Application Logo", "logoTip": "If set, the Authing login form will display this icon on the \"Login with {Display Name}\" button, which will be displayed as 20 * 20.", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "Windows AD", "syncToAd": "Sync to AD", "syncToAdTip": "If set, when AD authentication succeeds, the user's password in the AD domain will be synchronized to their password in Authing", "modifyPasswordSync": "Password Modification Sync", "modifyPasswordSyncTip": "If set, when the user's password in Authing is modified (including administrator password modification and user manual password reset), the user's password in AD will also be synchronized.", "validateAdPolicy": "Validate AD Password Policy When Modifying Password", "validateAdPolicyTip": "If set, when users modify their password in Authing, it will first verify if the password meets AD password policy requirements. If not, users will be prompted to re-enter the password. If it meets the requirements, the user's password in AD will also be synchronized.", "fieldMapping": "User Password Change Field Mapping", "authingField": {"label": "Authing Field", "placeholder": "Please select"}, "windowsAdField": {"label": "Windows AD Field", "placeholder": "Please select"}, "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "uploadFailed": "Upload failed"}, "windowsAd": {"appLogo": "Application Logo", "logoTip": "If set, the Authing login form will display this icon on the \"Login with {Display Name}\" button, which will be displayed as 20 * 20.", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "Windows AD", "syncToAd": "Sync to AD", "syncToAdTip": "If set, when AD authentication succeeds, the user's password in the AD domain will be synchronized to their password in Authing", "modifyPasswordSync": "Password Modification Sync", "modifyPasswordSyncTip": "If set, when the user's password in Authing is modified (including administrator password modification and user manual password reset), the user's password in AD will also be synchronized.", "validateAdPolicy": "Validate AD Password Policy When Modifying Password", "validateAdPolicyTip": "If set, when users modify their password in Authing, it will first verify if the password meets AD password policy requirements. If not, users will be prompted to re-enter the password. If it meets the requirements, the user's password in AD will also be synchronized.", "fieldMapping": "User Password Change Field Mapping", "authingField": {"label": "Authing Field", "placeholder": "Please select"}, "windowsAdField": {"label": "Windows AD Field", "placeholder": "Please select"}, "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "uploadFailed": "Upload failed"}, "amazon": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "configFileId": "Security Configuration File ID", "configFileIdPlaceholder": "Please enter security configuration file ID", "apiKeyAndroid": "API Key (Android)", "apiKeyAndroidPlaceholder": "Please enter API key for Android", "apiKeyIOS": "API Key (iOS)", "apiKeyIOSPlaceholder": "Please enter API key for iOS", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts."}, "amazonMobile": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "configFileId": "Security Configuration File ID", "configFileIdPlaceholder": "Please enter security configuration file ID", "apiKeyAndroid": "API Key (Android)", "apiKeyAndroidPlaceholder": "Please enter API key for Android", "apiKeyIOS": "API Key (iOS)", "apiKeyIOSPlaceholder": "Please enter API key for iOS", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts."}, "appleMobile": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "bundleId": "Bundle ID", "bundleIdPlaceholder": "Please enter Apple App Bundle ID", "teamId": "Team ID", "teamIdPlaceholder": "Please enter Apple Team ID", "keyId": "Key ID", "keyIdPlaceholder": "Please enter Apple Key ID", "key": "Key", "keyPlaceholder": "Please enter Apple Key", "callbackUrl": "Callback URL", "callbackTip": "You need to configure this URL in the callback address of the corresponding identity source", "scopes": "Authorization Scopes", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "appleWeb": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "servicesIdentifier": "Services Identifier", "servicesIdentifierPlaceholder": "Please enter Apple Services Identifier", "teamId": "Team ID", "teamIdPlaceholder": "Please enter Apple Team ID", "keyId": "Key ID", "keyIdPlaceholder": "Please enter Apple Key ID", "key": "Key", "keyPlaceholder": "Please enter Apple Key", "callbackUrl": "Callback URL", "callbackTip": "You need to configure this URL in the callback address of the corresponding identity source", "scopes": "Authorization Scopes", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "aws": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "authDomain": "Authentication Domain", "authDomainPlaceholder": "Please enter authentication address", "clientId": "Client ID", "clientIdPlaceholder": "Please enter client ID", "clientSecret": "Client Secret", "clientSecretPlaceholder": "Please enter AWS application client secret", "callbackUrl": "Callback URL", "callbackTip": "You need to configure this URL in the callback address of the corresponding identity source", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "facebook": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "appId": "App ID", "appIdPlaceholder": "Please enter app ID", "appSecret": "App Secret", "appSecretPlaceholder": "Please enter app secret", "callbackUrl": "Callback URL", "callbackUrlPlaceholder": "Please enter your business callback URL", "scopes": "Authorization Scopes", "callbackTip": "You need to configure this URL in the callback address of the corresponding identity source", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "facebookMobile": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "appId": "App ID", "appIdPlaceholder": "Please enter app ID", "appSecret": "App Secret", "appSecretPlaceholder": "Please enter app secret", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "github": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "clientId": "Client ID", "clientIdPlaceholder": "Please enter GitHub Client ID", "clientSecret": "Client secrets", "clientSecretPlaceholder": "Please enter GitHub Client secrets", "callbackUrl": "Callback URL", "callbackUrlPlaceholder": "Please enter your business callback URL", "scopes": "Authorization Scopes", "callbackTip": "You need to configure this URL in the callback address of the corresponding identity source", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "googleMobile": {"basicConfig": "Basic Configuration", "uniqueId": {"label": "Unique Identifier", "placeholder": "Please enter unique identifier"}, "displayName": {"label": "Display Name", "placeholder": "This name will be displayed on the login button in the user interface"}, "clientId": {"label": "Client ID", "placeholder": "Please enter Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "Please enter Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "Please enter your business callback URL"}, "callback": {"label": "Callback Address", "tip": "You need to configure this URL in the callback address of the corresponding identity source"}, "loginMode": {"label": "Login Mode", "options": {"register": "Available for login and registration", "login": "Login only"}}, "accountBinding": {"label": "Account Binding", "tip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts."}, "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "linkedin": {"basicConfig": "Basic Configuration", "uniqueId": {"label": "Unique Identifier", "placeholder": "Please enter unique identifier"}, "displayName": {"label": "Display Name", "placeholder": "This name will be displayed on the login button in the user interface"}, "clientId": {"label": "Client ID", "placeholder": "Please enter Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "Please enter Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "Please enter your business callback URL"}, "callback": {"label": "Callback Address", "tip": "You need to configure this URL in the callback address of the corresponding identity source"}, "loginMode": {"label": "Login Mode", "options": {"register": "Available for login and registration", "login": "Login only"}}, "accountBinding": {"label": "Account Binding", "tip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts."}, "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}}, "userManager": {"title": "User Management", "list": {"title": "User List", "detail": "User Details", "emptyDescription": "All users in the current user pool. You can manage users centrally here.", "createUser": "Create User", "username": "Username", "phone": "Phone", "email": "Email", "lastLoginTime": "Last Login Time", "createdAt": "Created At", "filter": "Filter", "filterConditions": "Filter Conditions", "selectAttribute": "Select Attribute", "selectRelation": "Select Relation", "inputAttributeValue": "Enter Attribute Value", "addRule": "Add Rule", "confirmOperation": "Confirm Operation", "confirmDelete": "Are you sure you want to delete these data?", "attributes": {"userId": "User ID", "name": "Name", "username": "Username", "status": "Account Status", "workStatus": "Work Status", "gender": "Gender", "phone": "Phone", "email": "Email"}, "relations": {"equal": "Equal", "notEqual": "Not Equal", "contains": "Contains"}}, "group": {"title": "User Group Management", "list": "User Group Management - List", "detail": "User Group Management - Details", "create": "User Group Management - Create", "emptyDescription": "Automatically add or remove members based on whether user attributes match the current set rules, enabling more flexible user control.", "createStaticGroup": "Create Static User Group", "createDynamicGroup": "Create Dynamic User Group", "name": "Name", "uniqueId": "Unique Identifier", "groupType": "Group Type", "dynamicGroup": "Dynamic Group", "staticGroup": "Static Group", "totalUsers": "Total Users", "authRules": "Authorization Rules", "filter": "Filter", "searchPlaceholder": "Search group name or code", "confirmOperation": "Confirm Operation", "confirmDelete": "Are you sure you want to delete these data?", "back": "Back", "groupName": "Group Name", "inputGroupName": "Please enter group name", "groupCode": "Group Identifier", "inputGroupCode": "Please enter group identifier", "description": "Description", "inputDescription": "Please enter description", "groupRules": "Group Rules", "selectAttributeCategory": "Please select attribute category", "userBasicFields": "User Basic Fields", "selectAttribute": "Please select attribute", "selectRelation": "Please select relation", "selectAttributeValue": "Please select attribute value", "inputAttributeValue": "Please enter attribute value", "addRule": "Add Rule", "groupNameRequired": "Please enter group name", "groupCodeRequired": "Please enter group identifier", "updateSuccess": "Update successful", "createSuccess": "Create successful", "updateFailed": "Update failed", "createFailed": "Create failed", "attributes": {"userId": "User ID", "name": "Name", "username": "Username", "status": "Account Status", "workStatus": "Work Status", "gender": "Gender", "phone": "Phone", "email": "Email"}, "relations": {"equal": "Equal", "notEqual": "Not Equal", "contains": "Contains"}}, "organize": {"title": "Organization Structure", "memberEntry": "Member Entry", "searchMemberDepartment": "Search Members, Departments", "create": "Create", "createOrganization": "Create Organization", "setDepartmentManager": "Set Department Manager", "addSubDepartment": "Add Sub-department", "disableOrganization": "Disable Organization", "disableDepartment": "Disable Department", "editOrganization": "Edit Organization", "editDepartment": "Edit Department", "deleteOrganization": "Delete Organization", "deleteDepartment": "Delete Department", "user": "User", "phone": "Phone", "email": "Email", "accountStatus": "Account Status", "disableAccount": "Disable Account", "processResignation": "Process Resignation", "changeDepartment": "Change Department", "setMainDepartment": "Set Main Department", "setAsManager": "Set as Manager", "setPosition": "Set Position", "confirmOperation": "Confirm Operation", "confirmDelete": "Are you sure you want to delete these data?"}, "postManagement": {"title": "Position Management", "create": "Create Position", "edit": "Edit Position", "positionName": "Position Name", "positionCode": "Position Code", "description": "Description", "departmentId": "Department ID", "createdAt": "Created Time", "updatedAt": "Last Updated Time", "confirmOperation": "Confirm Operation", "confirmDelete": "Are you sure you want to delete these data?", "back": "Back", "inputPositionName": "Please enter position name", "inputPositionCode": "Please enter position code, e.g.: admin", "inputDescription": "Please enter position description", "parentDepartment": "Parent Department", "positionNameRequired": "Please enter position name", "positionCodeRequired": "Please enter position code", "organization": "Organization"}, "form": {"resetPassword": "Reset Password", "more": "More", "disableAccount": "Disable Account", "enableAccount": "Enable Account", "deleteAccount": "Delete Account", "forceLogout": "Force Logout", "confirmOperation": "Confirm Operation", "confirmDelete": "Are you sure you want to delete this data?", "tabs": {"userInfo": "User Information", "userAffiliation": "User Affiliation", "permissionManagement": "Permission Management", "applicationAuthorization": "Application Authorization", "accessLog": "Access Log"}}, "activated": {"title": "Are you sure to activate {name}'s account?", "description": "After activation, the account status will return to normal and users can log in to the application again.", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "create": {"title": "Create User", "accountType": {"username": "Username", "phone": "Phone", "email": "Email"}, "form": {"username": {"label": "Username", "placeholder": "Please enter username", "required": "Please enter username"}, "phone": {"label": "Phone", "placeholder": "Please enter phone number", "required": "Please enter phone number"}, "email": {"label": "Email", "placeholder": "Please enter email", "required": "Please enter email"}, "password": {"label": "Password", "placeholder": "Please enter password", "required": "Please enter password", "generate": "Generate Password"}, "confirmPassword": {"label": "Confirm Password", "placeholder": "Please confirm password", "required": "Please confirm password", "notMatch": "The two passwords do not match"}, "loginUrl": {"label": "Send First Login URL"}, "selectPool": {"label": "Select User Pool or Application", "placeholder": "Select", "preview": "Preview"}, "resetPassword": {"label": "Force password change on first login"}}, "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "disable": {"title": "Are you sure to disable {name}'s account?", "description": {"line1": "Disabled accounts cannot log in to applications or reset passwords", "line2": "User information can still be edited while the account is disabled", "line3": "Disabled accounts can be restored"}, "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "resetPwd": {"title": "Reset Password", "form": {"newPassword": {"label": "New Password", "placeholder": "Please enter new password", "required": "Please enter new password", "generate": "Generate Password"}, "sendPassword": {"label": "Send reset password to user"}, "email": {"label": "Email", "placeholder": "Please enter email"}, "phone": {"label": "Phone", "placeholder": "Please enter phone number"}, "application": {"label": "Select user login application", "placeholder": "Please select"}, "forceChange": {"label": "Force password change after login"}}, "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "rights": {"title": "Data Resource Policy", "search": {"placeholder": "Search policy name, subject, or data resource"}, "authorize": "Authorize", "columns": {"selection": "Select", "policyName": "Policy Name", "policyDescription": "Policy Description", "authorizedSubject": "Authorized Subject", "relatedResource": "Related Resource", "lastEditTime": "Last Edit Time"}}}, "permission": {"title": "Permission Management", "role": {"title": "Role Management", "create": "Create Role", "edit": "Edit Role", "roleName": "Role Name", "roleCode": "Role Code", "description": "Description", "status": "Status", "enabled": "Enabled", "disabled": "Disabled", "createdAt": "Created Time", "filter": "Filter", "searchPlaceholder": "Search role name or code", "confirmOperation": "Confirm Operation", "confirmDelete": "Are you sure you want to delete these data?", "createSuccess": "Created successfully", "updateSuccess": "Updated successfully", "deleteSuccess": "Deleted successfully", "operationFailed": "Operation failed, please try again", "back": "Back", "reset": "Reset", "save": "Save", "basicInfo": "Basic Information", "inputRoleName": "Please enter role name", "inputRoleCode": "Please enter role code, e.g.: admin", "disableTime": "Role Auto-disable Time", "selectDateTime": "Select date and time", "inputDescription": "Please enter role description", "permissions": "Permissions", "selectAll": "Select All", "deselectAll": "Deselect All", "submit": "Submit", "completePermissions": "Please complete role permissions", "goToPermissions": "Go to Permissions", "roleNameRequired": "Please enter role name", "roleCodeRequired": "Please enter role code"}}, "branding": {"title": "Branding", "message": "Message Settings", "emailTemplates": "Email Templates", "thirdPartyEmailService": {"title": "Third-party Email Service", "enableThirdParty": "Enable Third-party Service", "custom": "Custom", "tencentEnterprise": "Tencent Enterprise Email", "aliyunEnterprise": "Alibaba Enterprise Email", "sendgrid": "SendGrid", "smtpAddress": "SMTP Address", "port": "Port", "username": "Username", "password": "Password", "senderEmail": "Sender <PERSON><PERSON>", "securityVerification": "Security Verification", "none": "None", "save": "Save", "saveAndTest": "Save and Send Test Email", "inputSmtpAddress": "Please enter SMTP address", "inputPort": "Please enter port", "inputUsername": "Please enter username", "inputPassword": "Please enter password", "inputSenderEmail": "Please enter sender email", "inputApiKey": "Please enter API Key"}, "smsVerificationService": "SMS Verification Service", "editAndPreview": "Edit and Preview", "templates": {"notification": "Notification Templates", "verificationCode": "Registration/Login Verification Code Templates", "pipeline": "Pipeline Related Templates", "verification": "Verification Templates", "password": "Password Related Templates", "emailBinding": "Email Binding Templates", "identityAlert": "Identity Automation Alert Templates"}, "emailItems": {"welcome": "Welcome Email", "userCreation": "First User Creation Notification", "invitation": "Invitation Email", "registerCode": "Registration Verification Code", "loginCode": "Login Verification Code", "mfaLoginCode": "MFA Login Verification Code", "infoCompletionCode": "Information Completion Verification Code", "pipelineError": "Pipeline Execution Error", "firstEmailLogin": "First Email Login Verification", "consoleVerification": "Console Initiated Verification", "passwordExpiration": "Password Expiration Reminder", "adminResetPassword": "Admin Password Reset Notification", "passwordChangeNotice": "Account Password Change Notification", "selfUnlockCode": "Self-service Unlock Verification Code", "resetPasswordCode": "Reset Password Verification Code", "emailUnbindCode": "Email Unbinding Verification Code", "emailBindCode": "Email Binding Verification Code", "workflowFailure": "Workflow Execution Failure", "workflowTimeout": "Workflow Execution Timeout", "createDataFailure": "Data Creation Failure", "updateDataFailure": "Data Update Failure", "deleteDataFailure": "Data Deletion Failure"}}, "security": {"title": "Security Settings", "mfa1": "Multi-factor Authentication", "mfa": {"simpleMode": "Simple Mode", "advancedMode": "Advanced Mode", "authFactors": "Authentication Factors", "sms": {"title": "SMS Verification", "description": "Receive verification code via SMS for login authentication"}, "email": {"title": "Email Verification", "description": "Receive verification code via email for login authentication"}, "otp": {"title": "OTP Token", "description": "Use OTP one-time password for login authentication"}, "enableSuccess": "Enabled successfully", "enableFailed": "Failed to enable, please try again", "disableSuccess": "Disabled successfully", "disableFailed": "Failed to disable, please try again"}}}, "frontend": {"back": "Back", "tabs": {"application": "Application Settings", "protocol": "Protocol Settings", "loginControl": "Login Control", "accessAuth": "Access Authorization"}}, "application": {"basicInfo": {"title": "Basic Information", "appName": "Application Name", "appDescription": "Application Description", "appLogo": "Application Logo", "visibility": "Visibility Range", "web": "Web", "mobile": "Mobile"}, "endpointInfo": {"title": "Endpoint Information", "appId": "App ID", "jwksEndpoint": "JWKS Public Key Endpoint", "appSecret": "App Secret", "authEndpoint": "Authentication Endpoint", "tokenEndpoint": "Token Endpoint", "issuer": "Issuer", "userInfoEndpoint": "User Info Endpoint", "discoveryEndpoint": "Service Discovery Address", "logoutEndpoint": "Logout Endpoint"}, "authConfig": {"title": "Authentication Configuration", "authAddress": "Authentication Address", "loginCallbackUrl": "<PERSON>gin Callback URL", "logoutCallbackUrl": "Logout Callback URL", "initiateLoginUrl": "Initiate Login URL", "multipleUrlTip": "For multiple URLs, separate them with commas"}, "buttons": {"save": "Save", "reset": "Reset"}, "validation": {"appNameRequired": "Please enter the Application Name", "authAddressRequired": "Please enter the Authentication Address", "loginCallbackRequired": "Please enter the Login Callback URL"}, "messages": {"copySuccess": "<PERSON><PERSON>d successfully", "copyFailed": "Co<PERSON> failed"}}, "loginCtrl": {"title": "Login and Registration Control", "defaultSettings": "<PERSON><PERSON><PERSON>", "registrationSettings": "Registration Settings", "loginWay": {"mergedTitle": "Merged Login & Registration", "mergedDesc": "Single page, seamless registration and login experience", "separateTitle": "Separate Login & Registration", "separateDesc": "Two separate pages, register first then login"}, "table": {"sort": "Sort", "accountField": "Account Field", "authMethod": "Authentication Method", "useForLogin": "Use for Login", "useForRegister": "Use for Registration"}, "authMethod": {"password": "Password", "verifyCode": "Verification Code"}, "placeholder": {"selectLoginField": "Please select field for login", "selectDefaultLoginMethod": "Please select default login method", "selectDefaultRegisterMethod": "Please select default registration method"}, "buttons": {"addAccountField": "Add Account Field", "save": "Save", "reset": "Reset"}, "defaultLoginMethod": {"title": "De<PERSON><PERSON>", "description": "The login method users see by default when logging in"}, "defaultRegisterMethod": {"title": "Default Registration Method", "description": "The registration method users see by default when registering"}, "socialLogin": {"title": "Social Login Methods", "addIdentitySource": "Add Identity Source"}, "relatedApps": {"title": "Related Client Applications", "noApps": "No client applications available, please create a client application first"}, "defaultLoginType": {"title": "Default <PERSON>", "description": "The login type users see by default when logging in", "normal": "Normal Login", "qrCode": "QR Code Login"}, "onlineUsers": {"title": "Online Users", "columns": {"userInfo": "User Information", "phone": "Phone Number", "email": "Email", "loginCount": "Login <PERSON>", "lastLoginTime": "Last Login Time"}}, "autoRegisterLogin": {"title": "Auto Register and Login", "description": "When users log in with an unregistered account, the system will automatically register an account for them and log them in"}, "mergeLoginRegister": {"title": "Merge <PERSON>gin and Registration", "description": "When enabled, the login interface will include registration options, and users can complete registration directly on the login interface"}, "loginMethodsSort": {"title": "Login Method Sorting", "description": "Drag to adjust the display order of login methods", "columns": {"sort": "Sort", "loginMethod": "Login Method", "operations": "Operations"}, "buttons": {"moveUp": "Move Up", "moveDown": "Move Down"}}, "passwordLoginConfig": {"title": "Password Login Configuration", "description": "Configure available methods for password login", "options": {"phonePassword": "Phone Number & Password", "emailPassword": "Email & Password", "usernamePassword": "Username & Password"}}, "verifyCodeLoginConfig": {"title": "Verification Code Login Configuration", "description": "Configure available methods for verification code login", "options": {"phoneCode": "Phone Number Verification Code", "emailCode": "Email Verification Code"}}, "loginOptions": {"phoneCode": "Phone Verification Code", "password": "Password"}, "registerOptions": {"phone": "Phone Number", "emailCode": "Email Verification Code", "phonePassword": "Phone Number & Password", "usernamePassword": "Username & Password"}, "methodLabels": {"phoneCode": "Phone Verification Code", "emailCode": "Email Verification Code", "phonePassword": "Phone Number & Password", "emailPassword": "Email & Password", "usernamePassword": "Username & Password"}, "fieldOptions": {"identityNumber": "ID Number-identityNumber", "externalId": "Original System ID-externalId"}, "messages": {"saveSuccess": "Saved successfully", "saveFailed": "Save failed", "saveError": "Save failed, please try again later"}}, "protocol": {"title": "Protocol Configuration", "selectProtocol": "Select Protocol", "save": "Save", "reset": "Reset", "add": "Add", "delete": "Delete", "protocolTypes": {"OIDC": "OpenID Connect", "OAUTH": "OAuth 2.0", "SAML": "SAML 2.0", "CAS": "CAS"}, "enableProtocol": "Enable Protocol", "tabs": {"basic": "Basic Settings", "jwt": "JWT Settings", "claim": "Claim Configuration", "scope": "Scope Configuration", "endpoint": "Endpoint Settings", "advanced": "Advanced Settings"}, "oidc": {"title": "OIDC Configuration", "tokenExpiry": "Token Expiry Time", "authorizationCode": "Authorization Code", "idToken": "ID Token", "accessToken": "Access Token", "refreshToken": "Refresh <PERSON>", "exchangeVerificationMethod": "Exchange Verification Method", "inspectionVerificationMethod": "Inspection Verification Method", "withdrawalVerificationMethod": "Withdrawal Verification Method", "authorizationCodeTime": "Authorization Code Time", "IdTokenTime": "id_token Expiry Time", "AccessTokenTime": "access_token Expiry Time", "RefreshTokenTime": "refresh_token Expiry Time", "claimMapping": "<PERSON><PERSON>m <PERSON>ping", "configureScope": "Configure <PERSON>", "addMappingField": "Add Mapping Field", "selectClaims": "Select Claims", "createCustomScope": "Create Custom Scope", "units": {"seconds": "Seconds", "minutes": "Minutes", "hours": "Hours", "days": "Days"}, "grantTypes": {"title": "Grant Types", "authorizationCode": "Authorization Code", "implicit": "Implicit", "password": "Password", "clientCredentials": "Client Credentials", "refreshToken": "Refresh <PERSON>"}, "responseTypes": {"title": "Response Types", "code": "Code", "token": "Token", "idToken": "ID Token"}, "pkce": {"title": "PKCE Settings", "required": "Required", "optional": "Optional", "disabled": "Disabled"}, "authorizationModes": "Authorization Modes", "options": {"enableRefreshTokenRotation": "Enable refresh_token rotation", "refreshTokenLifetime": "Refresh the refresh_token lifetime when rotating", "noForceHttpsForImplicit": "Don't force HTTPS for implicit mode callback URLs", "enableIdTokenEncryption": "Enable id_token encryption", "userConsentPage": "User Consent Page", "idTokenPayloadEncryption": "ID Token Payload Symmetric Encryption Algorithm", "idTokenKeyEncryption": "ID Token Payload Key Asymmetric Encryption Algorithm", "asymmetricPublicKey": "Asymmetric Encryption Public Key"}, "encryption": {"algorithm": {"a128cbcHs256": "A128CBC-HS256", "a128gcm": "A128GCM", "a256cbcHs512": "A256CBC-HS512", "a256gcm": "A256GCM"}, "method": {"rsaOaep": "RSA-OAEP", "ecdhEs": "ECDH-ES"}}}, "saml": {"title": "SAML Configuration", "entityId": "Entity ID", "acsConfig": {"title": "ACS Address Configuration", "responseMode": "Response Mode", "address": "Address", "addACS": "Add ACS Address", "modes": {"httpPost": "HTTP-POST", "httpRedirect": "HTTP-REDIRECT"}}, "customAttributes": {"title": "Custom Attributes", "label": "Label", "way": "Method", "value": "Value", "addAttribute": "Add Attribute", "ways": {"basic": "Basic", "advanced": "Advanced"}}, "cert": {"title": "Certificate Configuration", "upload": "Upload Certificate", "generate": "Generate Certificate"}, "signatureAlgorithm": "Signature Algorithm", "algorithms": {"sha1": "SHA-1", "sha256": "SHA-256", "sha512": "SHA-512"}}, "claim": {"title": "Claim Configuration", "add": "<PERSON><PERSON>", "name": "Name", "value": "Value", "tips": {"duplicateName": "Name already exists", "required": "Required field"}}, "scope": {"title": "Scope Configuration", "add": "<PERSON>d <PERSON>", "name": "Name", "description": "Description", "claims": "Included Claims", "tips": {"duplicateName": "Name already exists", "required": "Required field"}}, "jwt": {"title": "JWT Configuration", "algorithm": "Signature Algorithm", "keyPair": "Key Pair", "generate": "Generate New Key", "algorithms": {"rs256": "RS256", "rs384": "RS384", "rs512": "RS512", "hs256": "HS256", "hs384": "HS384", "hs512": "HS512"}}, "buttons": {"save": "Save", "reset": "Reset", "add": "Add", "delete": "Delete"}, "messages": {"saveSuccess": "Saved successfully", "saveFailed": "Save failed", "confirmDelete": "Confirm deletion of this item?", "generateSuccess": "Generated successfully", "generateFailed": "Generation failed"}, "alert": {"oidcInfo": "When using Authing's authentication function, the OIDC (OpenID Connect) protocol is used by default for authentication.", "whichAuthMode": "Not sure which authorization mode to choose?", "oauthDeprecated": "[Important] OAuth2.0 is no longer recommended, please switch to OIDC, see the documentation:", "implementSSO": "Implementing Single Sign-On", "samlConfig": "Configure your application as a SAML identity source. If you have any questions, please", "clickForHelp": "click here for help documentation", "casConfig": "Configure your application as a CAS identity source. For detailed information, please", "viewDocs": "view the documentation"}, "oauth": {"enableOAuth2": "Enable OAuth 2.0", "selectClaims": "Select Claims", "createCustomScope": "Create Custom Scope", "scopeExists": "Scope already exists", "configureScope": "Configure <PERSON>", "selectUserField": "Select User Field", "grantTypes": "Grant Types", "tokenAuthentication": "Token Authentication", "tokenEndpointAuthMethod": "Token Endpoint Auth Method", "clientAuthentication": "Client Authentication", "clientSecret": "Client Secret", "clientId": "Client ID", "withdrawTokenAuthentication": "Withdraw Token Authentication", "withdrawTokenEndpointAuthMethod": "Withdraw Token Endpoint Auth Method", "withdrawClientAuthentication": "Withdraw Client Authentication", "withdrawClientSecret": "Withdraw Client Secret", "withdrawClientId": "Withdraw Client ID"}, "cas": {"casVersion": "CAS Version", "enableCAS": "Enable CAS", "loginUrl": "Login URL", "logoutUrl": "Logout URL", "loginEndpoint": "Login Endpoint", "logoutEndpoint": "Logout Endpoint", "serviceValidateEndpoint": "Service Validate Endpoint", "serviceValidateEndpoint2": "Service Validate Endpoint 2", "serviceTicketExpiry": "Service Ticket Expiry", "userId": "User ID Field", "customResponseFields": "Custom Response Fields", "addField": "Add Field", "logoutUri": "Logout URI", "serverPrefix": "Server Prefix", "ticketValidateVersion": "Ticket Validate Version", "ticketValidatePath": "Ticket Validate Path", "ticketExpiry": "Ticket Expiry", "tgtExpiry": "TGT Expiry", "enterFieldName": "Please enter field name", "enterFieldValue": "Please enter field value", "userIdPlaceholder": "Please enter user ID"}}, "enabled": "Enabled", "disabled": "Disabled", "operations": "Operations", "cancel": "Cancel", "confirm": "Confirm", "create": "Create", "azureAd": {"basicConfig": "Basic Configuration", "appLogo": "Application Logo", "logoTip": "If set, the Authing login form will display this icon on the \"Login with {Display Name}\" button, which will be displayed as 20 * 20.", "uniqueId": "Unique Identifier", "inputUniqueId": "Please enter unique identifier", "displayName": "Display Name", "domainName": "Identity Source Domain", "tenantIdPlaceholder": "Please enter the Tenant ID where the Azure Active Directory application is located. If not filled, the default authentication account type is organizations", "clientIdPlaceholder": "Please enter the Client ID of the Azure Active Directory application", "clientSecretPlaceholder": "Please enter the Client Secret of the Azure Active Directory application", "emailVerificationSync": "Email Verification Sync Policy", "emailVerificationFalse": "Always set emailVerified to false", "emailVerificationTrue": "Always set emailVerified to true", "userInteractionType": "User Interaction Type", "interactionNoPrompt": "NoPrompt - No prompt parameter option", "interactionLogin": "login - Force the user to enter credentials on the request, canceling single sign-on", "interactionNone": "none - Opposite to login, ensures that no interactive prompts are shown to the user. If the request cannot be completed in a no-prompt manner through single sign-on, the Microsoft identity platform will return an interaction_required error", "interactionConsent": "consent - After the user logs in, requires the user to grant permissions to the application", "interactionSelectAccount": "select_account - Display all accounts remembered in the session", "interactionTip": "Prompt the identity provider to take actions during the user's authentication and authorization process.", "callbackUrl": "Callback URL", "callbackTip": "You need to set this link as the callback link for your Azure AD application. Please refer to the Azure AD documentation.", "loginMode": "Login Mode", "loginAndRegister": "Available for login and registration", "loginOnly": "Login only", "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "uploadFailed": "Upload failed"}, "department": {"createDepartment": "Create Department", "editDepartment": "Edit Department", "departmentName": "Department Name", "inputDepartmentName": "Please enter department name", "departmentCode": "Department Code", "inputDepartmentCode": "Please enter department code", "codeTooltip": "The unique identifier of the department, unique at the same level, can be used for data exchange.", "departmentDescription": "Department Description", "inputDepartmentDescription": "Please enter department description", "associatedPositions": "Associated Positions", "selectAssociatedPositions": "Please select associated positions", "parentDepartment": "Parent Department", "pleaseEnterDepartmentName": "Please enter department name", "pleaseSelectParentDepartment": "Please select parent department", "parentDepartmentCannotBeEmpty": "Parent department cannot be empty", "organizationalStructure": "Organizational Structure", "setDepartmentManager": "Set Department Manager", "searchOrSelectMembers": "Search or select members below", "selectedMembers": "Selected: {count} members"}, "common": {"selectAll": "Select All", "clear": "Clear", "cancel": "Cancel", "confirm": "Confirm"}, "member": {"memberEntry": "Member Onboarding", "name": "Name", "inputName": "Please enter name", "phoneNumber": "Phone Number", "inputPhoneNumber": "Please enter phone number", "email": "Email", "inputEmail": "Please enter email", "password": "Password", "inputPassword": "Please enter password", "generatePassword": "Generate Password", "position": "Position", "selectPosition": "Please select position", "forceResetPasswordOnFirstLogin": "Force user to change password on first login", "nameRequired": "Please enter name", "nameLengthLimit": "Name length should be between 2 and 20 characters", "phoneRequired": "Please enter phone number", "emailRequired": "Please enter email", "passwordRequired": "Please enter password", "passwordLengthLimit": "Password length cannot be less than 8 characters", "positionRequired": "Please select position", "departmentRequired": "Please select at least one department"}, "organization": {"createOrganization": "Create Organization", "editOrganization": "Edit Organization", "name": "Organization Name", "inputName": "Please enter organization name", "code": "Organization Code", "inputCode": "Please enter organization code", "codeTooltip": "The unique identifier of the organization, unique at the same level, can be used for data exchange.", "description": "Organization Description", "inputDescription": "Please enter organization description", "relatedPositions": "Related Positions", "selectRelatedPositions": "Please select related positions", "nameRequired": "Please enter organization name", "codeRequired": "Please enter organization code"}, "identity": {"oauth2": {"appLogo": "Application Logo", "logoTip": "If set, the Authing login form will display this icon on the \"Login with {displayName}\" button, which will be displayed as 20 * 20.", "uniqueId": "Unique Identifier", "inputUniqueId": "Please enter unique identifier", "displayName": "Display Name", "inputDisplayName": "Please enter display name", "authorizeUrl": "Authorization URL", "inputAuthorizeUrl": "https://idp.example.com/authorize", "tokenUrl": "Token URL", "inputTokenUrl": "https://idp.example.com/token", "authorizeScope": "Scope Authorization Range", "inputAuthorizeScope": "Please enter Scope authorization range", "scopeTip": "Please use spaces to separate different authorization domains", "clientId": "Client ID", "inputClientId": "Please enter OAuth2 application Client ID", "clientSecret": "Client Secret", "inputClientSecret": "Please enter Client Secret", "clientSecretTip": "You can use macros like ${authEndPoint}, ${tokenEndPoint}, ${scope}, ${clientId}, ${clientSecret} to concatenate the authorization URL here. For example: ${authEndPoint}?client_id=${clientId}&scope=${scope}&response_type=code&state=12345", "codeToTokenScript": "Code to <PERSON><PERSON>", "tokenToUserInfoScript": "Token to User Info Script", "loginMode": "Login Mode", "loginAndRegister": "Available for login and registration", "loginOnly": "Login only", "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "uploadFailed": "Upload failed"}, "facebookMobile": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "appId": "App ID", "appIdPlaceholder": "Please enter app ID", "appSecret": "App Secret", "appSecretPlaceholder": "Please enter app secret", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "github": {"basicConfig": "Basic Configuration", "uniqueId": "Unique Identifier", "uniqueIdPlaceholder": "Please enter unique identifier", "displayName": "Display Name", "displayNamePlaceholder": "This name will be displayed on the button in the user login interface", "clientId": "Client ID", "clientIdPlaceholder": "Please enter GitHub Client ID", "clientSecret": "Client secrets", "clientSecretPlaceholder": "Please enter GitHub Client secrets", "callbackUrl": "Callback URL", "callbackUrlPlaceholder": "Please enter your business callback URL", "scopes": "Authorization Scopes", "callbackTip": "You need to configure this URL in the callback address of the corresponding identity source", "loginMode": "Login Mode", "loginModeOptions": {"register": "Available for login and registration", "login": "Login only"}, "accountBinding": "Account Binding", "accountBindingTip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts.", "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "googleMobile": {"basicConfig": "Basic Configuration", "uniqueId": {"label": "Unique Identifier", "placeholder": "Please enter unique identifier"}, "displayName": {"label": "Display Name", "placeholder": "This name will be displayed on the login button in the user interface"}, "clientId": {"label": "Client ID", "placeholder": "Please enter Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "Please enter Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "Please enter your business callback URL"}, "callback": {"label": "Callback Address", "tip": "You need to configure this URL in the callback address of the corresponding identity source"}, "loginMode": {"label": "Login Mode", "options": {"register": "Available for login and registration", "login": "Login only"}}, "accountBinding": {"label": "Account Binding", "tip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts."}, "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "googleWeb": {"basicConfig": "Basic Configuration", "uniqueId": {"label": "Unique Identifier", "placeholder": "Please enter unique identifier"}, "displayName": {"label": "Display Name", "placeholder": "This name will be displayed on the login button in the user interface"}, "clientId": {"label": "Client ID", "placeholder": "Please enter Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "Please enter Client Secret"}, "scopes": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Scopes needed for Google 2.0 authorization, separated by commas, default is email, profile, openid"}, "domainVerification": {"fileName": {"label": "Domain Verification File Name", "placeholder": "Please enter Google domain verification HTML file name, e.g. xxxx.html"}, "fileContent": {"label": "Domain Verification File Content", "placeholder": "Please enter Google domain verification HTML file content"}}, "accessType": {"label": "Get Refresh Token access_type", "tip": "Offline configuration object configures access token, client object will refresh access token as needed", "options": {"online": "Online", "offline": "Offline"}}, "prompt": {"label": "Login Authorization Prompt", "tip": "Show authorization prompt to user again", "options": {"firstTime": "Show only first time", "none": "none", "consent": "consent", "selectAccount": "select_account"}}, "incrementalAuth": {"label": "Incremental Authorization", "tip": "Incremental authorization, request resource authorization when needed.", "options": {"true": "True", "false": "False"}}, "callbackUrl": {"label": "Callback URL", "placeholder": "Please enter your business callback URL"}, "callback": {"label": "Callback Address", "tip": "You need to configure this URL in the callback address of the corresponding identity source"}, "loginMode": {"label": "Login Mode", "options": {"register": "Available for login and registration", "login": "Login only"}}, "accountBinding": {"label": "Account Binding", "tip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts."}, "associationMethod": {"label": "Association Method", "options": {"fieldMatch": "Field Match"}}, "fieldType": {"label": "Field Type", "options": {"email": "Email"}}, "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}, "linkedin": {"basicConfig": "Basic Configuration", "uniqueId": {"label": "Unique Identifier", "placeholder": "Please enter unique identifier"}, "displayName": {"label": "Display Name", "placeholder": "This name will be displayed on the login button in the user interface"}, "clientId": {"label": "Client ID", "placeholder": "Please enter Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "Please enter Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "Please enter your business callback URL"}, "callback": {"label": "Callback Address", "tip": "You need to configure this URL in the callback address of the corresponding identity source"}, "loginMode": {"label": "Login Mode", "options": {"register": "Available for login and registration", "login": "Login only"}}, "accountBinding": {"label": "Account Binding", "tip": "For accounts already existing in the user pool, users can bind this social login method to their existing accounts."}, "copy": {"success": "Copy successful", "failed": "Co<PERSON> failed"}}}, "permission": {"role": {"role": {"back": "Back", "edit": "Edit Role", "basicInfo": "Basic Information", "roleName": "Role Name", "roleNamePlaceholder": "Please enter role name", "roleCode": "Role Code", "roleCodePlaceholder": "Please enter role code, e.g.: admin", "description": "Description", "descriptionPlaceholder": "Please enter role description", "expirationDate": "Role Auto-disable Time", "selectPermissions": "Select Permissions", "cancel": "Cancel", "permissionGroup": {"selectAll": "Select All", "deselectAll": "Deselect All"}, "update": "Update", "create": "Create", "validation": {"roleNameRequired": "Please enter role name", "roleCodeRequired": "Please enter role code"}}, "form": {"title": {"create": "Create Role", "edit": "Edit Role"}, "roleName": "Role Name", "roleNamePlaceholder": "Please enter role name", "roleCode": "Role Code", "roleCodePlaceholder": "Please enter role code, e.g.: admin", "permissionSpace": "Permission Space", "permissionSpacePlaceholder": "Please select permission space", "permissionSpace1": "Permission Space 1", "permissionSpace2": "Permission Space 2", "description": "Description", "descriptionPlaceholder": "Please enter role description", "expirationDate": "Role Auto-disable Time", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "list": {"searchPlaceholder": "Search role name or code", "search": "Search", "columns": {"id": "ID", "roleName": "Role Name", "roleCode": "Role Code", "description": "Description", "status": "Status", "createdAt": "Created Time", "operations": "Operations"}, "status": {"enabled": "Enabled", "disabled": "Disabled"}, "operations": {"edit": "Edit", "delete": "Delete"}, "deleteConfirm": {"title": "Warning", "message": "Are you sure you want to delete role \"{name}\"?", "confirm": "Confirm", "cancel": "Cancel"}, "messages": {"deleteSuccess": "Successfully deleted role {name}", "deleteFailed": "Failed to delete role", "fetchFailed": "Failed to fetch role list"}}}}, "userGroup": {"info": "User Group Information", "form": {"groupName": {"label": "Group Name", "placeholder": "Please enter group name", "required": "Please enter group name"}, "groupIdentifier": {"label": "Group Identifier", "placeholder": "Please enter group identifier", "required": "Please enter group identifier"}, "description": {"label": "Description", "placeholder": "Please enter description"}, "rules": {"title": "Group Rules", "category": {"label": "Please select attribute category", "userBasic": "User Basic Fields"}, "attribute": {"label": "Please select attribute"}, "relation": {"label": "Please select relation", "equal": "Equal", "notEqual": "Not Equal", "contains": "Contains"}, "value": {"label": "Please select attribute value", "placeholder": "Please enter attribute value"}}, "addRule": "Add Rule", "confirm": "Confirm", "create": "Create", "buttons": {"confirm": "Confirm", "create": "Create"}}}, "userAffi": {"department": {"title": "Departments", "entry": "Process Entry", "empty": "No department information", "dialog": {"title": "Change Department", "changeTip": "Change {name}'s department to:", "organization": "Organization", "selected": "Selected · {count} departments", "clear": "Clear", "mainDepartment": "Main Department", "setMain": "Set as Main", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}}, "post": {"title": "Positions", "setPost": "Set Position", "empty": "No positions", "dialog": {"title": "Set Position", "placeholder": "Please select associated positions", "emptyTip": "No related positions", "create": "Create", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}}, "tenant": {"title": "Tenants", "columns": {"selection": "Select", "info": "Tenant Info", "department": "Tenant Department", "application": "Application Info"}, "dialog": {"title": "No Groups", "tip": "There are no groups yet. You can create a group first, then add this user to the group", "buttons": {"cancel": "Cancel", "create": "Create Now"}}}, "userGroup": {"title": "User Groups", "addStatic": "Add Static User Group", "columns": {"name": "Group Name", "code": "Identifier", "description": "Description", "type": "Group Type", "operations": "Operations"}, "type": {"dynamic": "Dynamic Group", "static": "Static Group"}, "dialog": {"title": "Select User Group", "label": "Select User Group:", "placeholder": "Select user group", "buttons": {"cancel": "Cancel", "confirm": "Confirm", "create": "Create New"}}, "delete": {"title": "Operation", "confirm": "Are you sure you want to delete this data?"}}, "userRole": {"title": "User Roles", "addRole": "Add Role", "columns": {"roleId": "Role ID", "roleCode": "Role Code", "roleName": "Role Name", "description": "Description", "operations": "Operations"}, "dialog": {"title": "User Role", "label": "Select Role:", "placeholder": "Select role", "rules": {"required": "Please select a role"}, "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "delete": {"title": "Operation", "confirm": "Are you sure you want to delete this data?"}}}, "userManager": {"userInfo": {"accountInfo": {"title": "Account Information", "createdTime": "Created Time", "lastLoginTime": "Last Login Time", "lastLoginIp": "Last Login IP", "loginCount": "Login <PERSON>", "userSource": "User Source", "userType": "User Type", "registerAlias": "<PERSON> Ali<PERSON>", "registerDevice": "Register Device", "passwordExpireTime": "Password Expiry Time"}, "thirdParty": {"title": "Bound Third-party Accounts", "empty": "No third-party accounts bound. You can bind third-party accounts for quick login"}, "loginHistory": {"title": "Login History Applications"}, "personalInfo": {"title": "Personal Information", "form": {"name": {"label": "Name", "placeholder": "Please enter name"}, "username": {"label": "Username", "placeholder": "Please enter username"}, "email": {"label": "Email", "placeholder": "Please enter email", "sendVerify": "Send Verification"}, "gender": {"label": "Gender", "placeholder": "Please select", "options": {"male": "Male", "female": "Female", "unknown": "Unknown"}}, "birthdate": {"label": "Birthday", "placeholder": "Please enter birthday"}, "phone": {"label": "Phone", "placeholder": "Please enter phone number"}, "country": {"label": "Country", "placeholder": "Please enter country code"}, "city": {"label": "City", "placeholder": "Please enter city"}, "company": {"label": "Company", "placeholder": "Please enter company name"}, "province": {"label": "Province", "placeholder": "Please enter province/region"}, "address": {"label": "Street Address", "placeholder": "Please enter street address"}, "externalId": {"label": "Original System ID", "placeholder": "Please enter original system ID (externalId)"}, "postalCode": {"label": "Postal Code", "placeholder": "Please enter postal code"}}, "buttons": {"save": "Save", "reset": "Reset"}}, "jsonInfo": {"title": "Personal Information", "copy": "Copy", "copySuccess": "Copy successful", "copyFailed": "Co<PERSON> failed"}}}}