{"login": {"title": "欢迎回来！登入您的账户", "register": "注册", "loading": "加载中...", "verificationCodeLogin": "验证码登录", "passwordLogin": "密码登录", "emailCodeLogin": "邮箱验证码登录", "inputPhoneOrEmail": "请输入手机号或邮箱", "inputVerifyCode": "请输入验证码", "inputCredential": "请输入用户名/邮箱/手机号", "inputPassword": "请输入密码", "selectLanguage": "选择语言", "inputCaptcha": "请输入验证码", "remember": "记住我", "forgot": "忘记密码？", "submit": "登录", "or": "或", "continueWithGoogle": "使用 Google 账号继续", "resetPassword": "重置密码", "nextStep": "下一步", "backToLogin": "返回登录", "phoneRegister": "手机注册", "emailCodeRegister": "邮箱验证码注册", "emailPasswordRegister": "邮箱密码注册", "usernamePasswordRegister": "用户名密码注册", "inputEmail": "请输入邮箱", "inputUsername": "请输入用户名", "confirmPassword": "请确认密码", "passwordStrength": "密码强度", "noAccount": "没有账号？", "hasAccount": "已有账号？", "registerNow": "立即注册", "loginNow": "立即登录", "googleLoginFailed": "Google 登录失败", "mainland": "简体中文", "hongkong": "繁体中文", "username": "用户名", "password": "密码", "verifyCode": "验证码", "sendCode": "发送验证码", "resendCode": "{time}s后重新发送", "phoneLogin": "手机号登录", "emailLogin": "邮箱登录", "passwordNotMatch": "两次输入的密码不一致", "registerSuccess": "注册成功", "loginSuccess": "登录成功", "mfaVerify": "两步验证", "mfaTitle": "两步验证 · 谷歌身份验证器", "invalidCredential": "请输入有效的手机号、邮箱或用户名", "passwordLength": "密码长度不能小于6个字符", "verifyCodeLength": "验证码长度应为6位", "sendingCode": "发送中...", "codeSent": "验证码已发送", "sendFailed": "发送失败，请重试", "loginFailed": "登录失败，请检查输入", "networkError": "网络错误，请稍后重试", "weak": "弱", "medium": "中", "strong": "强", "googleLogin": "使用 Google 账号登录", "countryCode": "国家/地区代码", "welcomeBack": "请选择登入方法: ", "createAccount": "请选择注册方法: "}, "userCenter": {"title": "个人中心", "menu": {"language": "语言", "profile": "个人中心", "changePassword": "修改密码", "logout": "退出登录"}, "sidebar": {"personalInfo": "个人信息", "accountBinding": "账号绑定", "multiFactorAuth": "多因素认证", "passkey": "Passkey", "socialIdentity": "社会化身份源管理", "enterpriseIdentity": "企业化身份源管理", "accountSafety": "账号安全", "accessLog": "访问日志"}, "personal": {"title1": "个人信息", "edit": "编辑", "save": "确认", "cancel": "取消", "userId": "用户 ID", "username": "用户名", "email": "邮箱", "phone": "手机号", "name": "姓名", "title": "职称", "address": "住址", "gender": "性别", "birthdate": "生日", "id": "id", "company": "公司", "signed_up": "注册时间", "registrationDate": "注册时间", "identityNumber": "居民身份证号", "updateSuccess": "更新成功", "updateFailed": "更新失败，请稍后重试", "fetchFailed": "获取用户信息失败，请稍后重试", "genderOptions": {"unknown": "未知", "male": "男", "female": "女"}}, "binding": {"phone": {"title": "手机号和邮箱", "modify": "修改", "unbind": "解绑", "bind": "绑定", "current": "当前手机号", "new": "新手机号", "verificationCode": "验证码", "send": "发送验证码", "resend": "{time}s后重新发送", "oldCode": "旧手机号验证码", "newCode": "新手机号验证码", "inputCode": "请输入6位验证码", "inputPhone": "请输入手机号", "inputNewPhone": "请输入新手机号", "unbindTitle": "解绑手机号", "smsCode": "短信验证码"}, "email": {"title": "邮箱验证", "current": "当前邮箱", "new": "新邮箱", "oldCode": "当前邮箱验证码", "newCode": "新邮箱验证码", "unbindTitle": "解绑邮箱", "emailCode": "邮箱验证码", "inputEmail": "请输入邮箱", "inputNewEmail": "请输入新邮箱"}, "messages": {"fillRequired": "请填写所有必要信息", "invalidPhone": "请输入正确的手机号格式", "invalidEmail": "请输入正确的邮箱格式", "updateSuccess": "更新成功", "updateFailed": "更新失败，请检查信息是否正确", "unbindSuccess": "解绑成功", "unbindFailed": "解绑失败，请检查验证码是否正确", "codeSent": "验证码已发送", "sendFailed": "发送验证码失败，请稍后重试", "inputCode": "请输入验证码"}}, "mfa1": "多因素认证", "mfa": {"sms": {"title": "短信验证码", "description": "使用短信形式收验证码认证登录", "bind": "绑定手机号", "inputPhone": "输入手机号", "verifyCode": "短信验证码", "inputCode": "请输入6位验证码", "sendCode": "发送验证码", "resendCode": "{time}s后重新发送"}, "email": {"title": "电子邮箱验证", "description": "使用邮件形式接收验证码认证登录", "bind": "绑定邮箱", "inputEmail": "输入邮箱账号", "verifyCode": "邮箱验证码"}, "otp": {"title": "OTP 口令验证", "description": "使用 OTP 一次性口令认证登录", "setup": {"title": "绑定 OTP 口令", "step1": "下载验证器", "step2": "扫描二维码", "step3": "输入安全码", "step4": "解绑 OTP"}}, "passkey": {"title": "创建 Passkey", "description": "借助 Passkey，您可以使用自己的指纹、面孔、屏幕设置或实体安密钥录制的账号", "noData": "暂无数据", "warning": "请仅在您自有的设备上设置 Passkey", "create": "创建 Passkey", "createSuccess": "Passkey 创建成功"}, "messages": {"bindSuccess": "绑定成功", "bindFailed": "绑定失败，请重试", "unbindSuccess": "解绑成功", "unbindFailed": "解绑失败，请重试", "codeSent": "验证码已发送", "sendFailed": "发送验证码失败，请稍后重试", "inputRequired": "请填写所有必要信息"}, "simpleMode": "简单模式", "advancedMode": "高级模式", "authFactors": "身份认证因素", "enableSuccess": "开启成功", "enableFailed": "开启失败，请重试", "disableSuccess": "关闭成功", "disableFailed": "关闭失败，请重试"}, "security": {"title": "账号安全", "score": "安全评分", "level": {"low": "低", "medium": "中", "high": "高"}, "password": {"title": "密码设置", "strength": "密码强度", "weak": "弱", "medium": "中", "strong": "强", "modify": "修改", "old": "原始密码", "new": "新密码", "confirm": "确认密码", "changeSuccess": "密码修改成功，请重新登录", "changeFailed": "密码修改失败，请重试", "description": "建议用较为复杂的密码。", "modifyNow": "立即修改"}, "deleteAccount": {"title": "账号注销", "description": "永久删除账号和所有数据，请谨慎操作", "warning": "注销后，此账号的所有数据都将被删除且不可逆，请谨慎操作！", "button": "注销", "confirm": "确认", "cancel": "取消", "inputAccount": "请输入当前账号", "inputPassword": "请输入当前密码", "inputPhone": "请输入手机号", "inputCode": "请输入验证码", "sendCode": "发送验证码", "resendCode": "{time}s后重新发送", "success": "账号注销请求已提交", "failed": "账号注销失败，请检查输入信息是否正确"}, "mfa": {"title": "多因素认证 (MFA)", "enabled": "已开启", "disabled": "未开启", "enable": "开启", "disable": "关闭"}, "email": {"title": "邮箱绑定", "bound": "已绑定", "unbound": "未绑定", "modify": "修改", "bind": "绑定"}, "phone": {"title": "手机绑定", "bound": "已绑定", "unbound": "未绑定", "modify": "修改", "bind": "绑定"}, "loadFailed": "获取安全信息失败，请稍后重试"}, "log": {"title": "访问日志", "time": "登录时间", "user": "登录用户", "ip": "IP 地址", "status": "状态", "browser": "浏览器", "platform": "平台", "success": "成功", "failed": "失败", "fetchFailed": "获取登录历史失败，请稍后重试"}, "avatar": {"uploadSuccess": "头像上传成功", "uploadFailed": "头像上传失败，请重试", "formatError": "上传头像图片只能是 JPG/PNG/GIF/BMP/WebP 格式!", "sizeError": "上传头像图大小不能超过 10MB!"}, "enterpriseIdentity": {"title": "企业化账号", "account": "账号", "status": "状态", "bindAccount": "绑定账号", "operation": "操作", "bind": "绑定", "unbind": "解除绑定", "normal": "正常", "abnormal": "异常", "notBound": "未绑定", "syncNow": "正在同步 {name}", "viewDetails": "查看 {name} 的详细信息", "fetchFailed": "获取企业账号列表失败"}}, "home": {"header": {"congratulations": "恭喜你完成了认证！", "description": "阅读完本教程后，你可以开始到新应用中理解实践认证流程的最佳实践。", "userCenter": "个人中心", "logout": "退出登录"}, "logout": {"title": "确认退出应用？", "message": "退出登录后，将返回注册/登录页面", "cancel": "取消", "confirm": "确定"}, "cards": {"expand": "展开", "collapse": "收起", "token": {"title": "看 Token 凭证", "description": "Token 是用户认证凭据，用于访问受保护资源的身份认证。", "details": "返回的 token 中有 id_token 和 access_token，二者之间区别请参考<a href=\"#\">文</a>。如果你需要获取 refresh_token 请参考<a href=\"#\">文档</a>。"}, "userInfo": {"title": "查看用户信息", "description": "你可以通过 Token 获取用户信息，用户信息符合 OIDC 标准。", "details": "使用 access_token 调用用户信息端点可以获取用户的详细信息。详情请参考<a href=\"#\">文档</a>。"}, "callback": {"title": "处理回调", "description": "如果你有后端服务，建议在后端处理回调。", "details": "在后端处理回调可以提高安全性，避免敏感信息暴露在前端。详细的回调处理流程请参考<a href=\"#\">文档</a>。"}, "noBackend": {"title": "无后端场景", "description": "适用于无后端的场景，全部处理作业都在前端进行。", "details": "在无后端场景下，需要特别注意前端安全性。建议使用 PKCE 流程增强安全性。详情请参考<a href=\"#\">文档</a>。"}, "faq": {"title": "常见问题", "description": "你可以参考这些常见问题，或者使用高级功能。", "details": "我们整理了一些常见问题和解答，希望能帮助你快速解决问题。如果还有其他疑问，欢迎联系我们的支持团队。"}, "next": {"title": "接下来你可能需要", "description": "阅读完本教程后，你可以尝试更多进阶操。", "details": "接下来你可以尝试以下操作：<br>1. 集成到你的应用<br>2. 配置自定义登录页面<br>3. 设置多因素认证<br>4. 管理用户权限"}}}, "to": "至", "startDate": "开始日期", "endDate": "结束日期", "search": "搜索", "account": "账号", "status": "状态", "normal": "正常", "abnormal": "异常", "bound": "已绑定", "unbound": "未绑定", "otp": {"bind_otp_title": "绑定 OTP 口令", "step_download": "下载验证器", "step_scan": "扫描二维码", "step_verify": "输入安全码", "step_unbind": "解绑 OTP", "scan_download_tip": "请使用手机扫码下载验证器", "scan_qrcode": "扫描二维码", "scan_qrcode_tip": "请在手机打开 Google Authenticator / Microsoft Authenticator 扫码添加安全码", "enter_code": "输入安全码", "enter_code_tip": "在手机查看并输入 6 位数字安全码，完成后进入下一步", "unbind_title": "解绑 OTP", "unbind_tip": "请输入您的 OTP 验证码以解绑当前设备", "confirm_unbind": "我确认要解绑 OTP", "unbind": "解绑 OTP", "bind_success": "OTP 绑定成功", "bind_failed": "OTP 验证失败，请重试", "unbind_success": "OTP 解绑成功", "unbind_failed": "OTP 解绑失败，请重试", "enter_6_digits": "请输入6位数字验证码", "bind_phone_email_first": "请先绑定手机号和邮箱后再尝试绑定 OTP", "bind_phone_first": "请先绑定手机号后再尝试绑定 OTP", "bind_email_first": "请先绑定邮箱后再尝试绑定 OTP", "get_qrcode_failed": "获取 OTP 二维码失败，请稍后重试", "scan_download": "下载"}, "actions": {"back": "返回", "next": "下一步", "previous": "上一步"}, "routers": {}, "routes": {"settings": "设置", "frontend": "前台设置", "backend": "后台设置", "identity": {"title": "身份源", "enterprise": "企业身份源", "enterpriseTmpList": "企业身份源模版列表", "enterpriseTmpListDetail": "企业身份源模版列表详情", "enterpriseCreate": "创建企业身份源", "enterpriseDetail": "企业身份源详情", "social1": "社会化身份源", "social": {"title": "社会化身份源", "emptyDescription": "对接微信、Git<PERSON>ab、支付宝等社交类登录软件，使你的应用支持使用该方式进行认证、授权登录。", "createSource": "创建社会化身份源", "identitySource": "身份源", "sourceName": "身份源名称", "sourceId": "身份源 ID", "confirmOperation": "操作确认", "confirmDelete": "确认删除该社会化身份源吗?", "deleteSuccess": "删除成功", "deleteFailed": "删除失败，请重试", "fetchFailed": "获取社会化身份源列表失败"}, "socialTmpList": "社会化身份源模版列表", "socialTmpListDetail": "社会化身份源模版列表详情", "socialCreate": "创建社会化身份源", "emptyDescription": "对接标准协议或飞书、企业微信、钉钉等企业软件，使你的应用支持使用该方式进行认证、授权登录。", "identitySource": "身份源", "sourceName": "身份源名称", "sourceId": "身份源 ID", "confirmOperation": "操作确认", "confirmDelete": "确认删除该企业身份源吗?", "createSource": "创建企业身份源", "loginMode": "登录模式", "scenario": "场景", "cas": {"appLogo": "应用 Logo", "logoTip": "如果设置，Authing 登录表单将在 \"使用 {Display Name} 登录\" 的按钮上显示此图标，该图标会展示为 20 * 20。", "uniqueId": "唯一标识", "inputUniqueId": "请输入唯一标识", "displayName": "显示名称", "inputDisplayName": "Windows AD", "casAuthUrl": "CAS 认证 URL", "casAuthUrlPlaceholder": "请输入外部 CAS 身份提供商的发起认证端点", "casTicketUrl": "CAS Ticket 检验 URL", "casTicketUrlPlaceholder": "请输入外部 CAS 身份提供商 Ticket 验证端点", "loginMode": "登录模式", "loginAndRegister": "可用于登录注册", "loginOnly": "仅用于登录", "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "uploadFailed": "上传失败"}, "ldap": {"basicConfig": "基础配置", "appLogo": "应用 Logo", "logoTip": "如果设置，Authing 登录表单将在 \"使用 {Display Name} 登录\" 的按钮上显示此图标，该图标会展示为 20 * 20。", "uniqueId": "唯一标识", "inputUniqueId": "请输入唯一标识", "displayName": "显示名称", "inputDisplayName": "Windows AD", "ldapUrl": "LDAP 链接", "ldapUrlPlaceholder": "请输入 LDAP 服务器的地址与端口, 如： ldap://dc.fabrikam.com:389", "bindDNPlaceholder": "请输入用于连接 LDAP 的用户名, 此用户名将用于测试连接结果和搜索用户或用户组", "bindDNPassword": "Bind DN 密码", "bindDNPasswordPlaceholder": "请输入用于连接 LDAP 的密码, 该密码将会被加密存储到数据库中", "usersDNPlaceholder": "定义从哪个目录开始搜索, 如： dc=fabrikam,dc=local", "queryCriteria": "查询条件", "queryCriteriaPlaceholder": "这是一个用来测试的 LDAP 服务。此测试 LDAP 配置的查询条件用于查找用户信息以取得用户 dn 信息。", "queryCriteriaTip": "查询条件，该条件结合 bindDN 以及对应的 secret 进行用户查找，用于检索用户的 dn 信息，结合用户密码进行 ldap 认证。支持自定义 filter 表达式，基本形式为：&(objectClass=organizationalPerson)(cn=%s), 其中 %s 会被用户在登录时填写的用户名替换。例如你想通过用户的 cn 登录，可以填写 &(objectClass=organizationalPerson)(cn=%s)；如果你想通过用户的 mail 登录，可以填写 &(objectClass=organizationalPerson)(mail=%s)。", "loginMode": "登录模式", "loginAndRegister": "可用于登录注册", "loginOnly": "仅用于登录", "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "connectionTest": "LDAP 连接测试", "test": "测试", "testAccount": "测试账号", "testPassword": "测试密码", "connectivityTest": "连通性测试", "accountPasswordTest": "账号密码测试", "notTested": "未进行", "uploadFailed": "上传失败"}, "oidc": {"appLogo": "应用 Logo", "logoTip": "如果设置，Authing 登录表单将在 \"使用 {Display Name} 登录\" 的按钮上显示此图标，该图标会展示为 20 * 20。", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "请输入显示名称", "mode": "模式", "modeOptions": {"frontend": "前端模式", "backend": "后端模式"}, "modeTip": "前端模式会使用 response_mode=form_post 和 response_type=id_token 模式，后端模式会使用 response_type=code 授权码模式", "issuerUrl": "Issuer URL", "issuerUrlPlaceholder": "id.mydomain.com/.well-known/openid-configuration", "issuerUrlTip": "你想要连接的 OpenID Connect provider 的 Issuer URL", "clientId": "Client ID", "clientIdPlaceholder": "请输入想要连接的 OpenID Connect provider 的 Client ID", "clientSecret": "Client Secret", "clientSecretPlaceholder": "请输入想要连接的 OpenID Connect provider 的 Client Secret", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。"}, "saml": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "appLogo": "应用 Logo", "signCert": "验签证书", "signCertPlaceholder": "请输入外部 SAML 提供商的 SAML Response 验签证书", "loginUrl": "登录 URL", "loginUrlPlaceholder": "请输入外部 SAML 提供商的发起认证端点", "samlRequestSign": "SAML 请求签名", "yes": "是", "no": "否", "samlRequestSignAlgo": "SAML 请求签名算法", "samlRequestSummaryAlgo": "SAML 请求摘要算法", "samlRequestProtocolBind": "SAML 请求协议绑定", "singleLogout": "单点登出", "logoutUrl": "登出 URL", "logoutUrlBind": "登出 URL Binding", "acsUrl": "ACS URL", "metadataXml": "元数据 XML 文件", "fieldMappAttr": "字段映射属性", "loginMode": "登录模式", "loginModeOptions": {"loginAndRegister": "可用于登录注册", "loginOnly": "仅用于登录"}, "loginOnlyTip": "开启「仅登录模式」后，只能登录既有账号，不能创建新账号，请谨慎选择。", "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "associationMethod": "关联方式", "associationMethodOptions": {"fieldMatch": "字段匹配"}, "fieldType": "字段类型", "fieldTypeOptions": {"email": "邮箱", "phone": "手机号", "username": "用户名", "externalId": "原系统 ID(externalId)"}, "copy": {"success": "复制成功", "failed": "复制失败"}, "validation": {"uniqueIdRequired": "请输入唯一标识", "displayNameRequired": "请输入显示名称", "signRequired": "请输入验签证书", "loginUrlRequired": "请输入登录 URL", "logoutUrlRequired": "请输入登出 URL", "logoutUrlBindRequired": "请选择登出 URL Binding"}}, "windows": {"appLogo": "应用 Logo", "logoTip": "如果设置，Authing 登录表单将在 \"使用 {Display Name} 登录\" 的按钮上显示此图标，该图标会展示为 20 * 20。", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "Windows AD", "domainLoginAddress": "域内登录地址", "domainLoginAddressPlaceholder": "https://xxx.example.com", "terminalId": "终端识别编号", "generateKey": "自动生成密钥", "autoLogin": "自动登录", "autoLoginTip": "如果设置，在加入了配置 AD 域的电脑中打开 Authing 登录页，会直接登录成功", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "uploadFailed": "上传失败"}, "windowsAd": {"appLogo": "应用 Logo", "logoTip": "如果设置，Authing 登录表单将在 \"使用 {Display Name} 登录\" 的按钮上显示此图标，该图标会展示为 20 * 20。", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "Windows AD", "syncToAd": "同步到 AD", "syncToAdTip": "如果设置，当 AD 认证成功时，会将用户在 AD 域的密码同步至其在 Authing 的密码", "modifyPasswordSync": "修改密码同步", "modifyPasswordSyncTip": "如果设置，当用户在 Authing 的密码被修改之后（包含管理员修改密码和用户自己手动重置密码），会将用户在 AD 中的密码也同步修改。", "validateAdPolicy": "修改密码同时校验 AD 密码策略", "validateAdPolicyTip": "如果设置，则当用户在 Authing 修改密码时会先校验密码是否符合 AD 密码策略要求，不符合会提示用户重新输入密码，符合则会将用户在 AD 中的密码也同步修改。", "fieldMapping": "用户改密字段映射", "authingField": {"label": "Authing 字段", "placeholder": "请选择"}, "windowsAdField": {"label": "Windows AD 字段", "placeholder": "请选择"}, "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "uploadFailed": "上传失败"}, "amazon": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "clientId": "客户端 ID", "clientIdPlaceholder": "请输入客户端 ID", "clientSecret": "客户端密钥", "clientSecretPlaceholder": "请输入 Amazon 应用的客户端密钥", "callbackUrl": "回调地址", "callbackUrlTip": "你需要将此链接配置到对应身份源的回调地址中", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}, "amazonMobile": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "configFileId": "安全配置文件 ID", "configFileIdPlaceholder": "请输入安全配置文件 ID", "apiKeyAndroid": "API 密钥 (安卓)", "apiKeyAndroidPlaceholder": "请输入安卓的 API 密钥", "apiKeyIOS": "API 密钥 (iOS)", "apiKeyIOSPlaceholder": "请输入 iOS 的 API 密钥", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。"}}, "appleMobile": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "bundleId": "Bundle ID", "bundleIdPlaceholder": "请输入 Apple App Bundle ID", "teamId": "Team ID", "teamIdPlaceholder": "请输入 Apple Team ID", "keyId": "Key ID", "keyIdPlaceholder": "请输入 Apple Key ID", "key": "Key", "keyPlaceholder": "请输入 Apple Key", "callbackUrl": "回调地址", "callbackTip": "你需要将此链接配置到对应身份源的回调地址中", "scopes": "授权范围", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}}, "appleWeb": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "servicesIdentifier": "Services Identifier", "servicesIdentifierPlaceholder": "请输入 Apple Services Identifier", "teamId": "Team ID", "teamIdPlaceholder": "请输入 Apple Team ID", "keyId": "Key ID", "keyIdPlaceholder": "请输入 Apple Key ID", "key": "Key", "keyPlaceholder": "请输入 Apple Key", "callbackUrl": "回调地址", "callbackTip": "你需要将此链接配置到对应身份源的回调地址中", "scopes": "授权范围", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}}, "aws": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "authDomain": "认证域", "authDomainPlaceholder": "请输入认证地址", "clientId": "客户端 ID", "clientIdPlaceholder": "请输入客户端 ID", "clientSecret": "客户端密钥", "clientSecretPlaceholder": "请输入 AWS 应用的客户端密钥", "callbackUrl": "回调地址", "callbackTip": "你需要将此链接配置到对应身份源的回调地址中", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}}, "facebook": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "appId": "应用编号", "appIdPlaceholder": "请输入应用编号", "appSecret": "应用密钥", "appSecretPlaceholder": "请输入应用密钥", "callbackUrl": "回调地址", "callbackUrlPlaceholder": "请输入你的业务回调链接", "scopes": "授权范围", "callbackTip": "你需要将此链接配置到对应身份源的回调地址中", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}}, "github": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "clientId": "Client ID", "clientIdPlaceholder": "请输入 GitHub 提供的 Client ID", "clientSecret": "Client secrets", "clientSecretPlaceholder": "请输入 GitHub 提供的 Client secrets", "callbackUrl": "回调地址", "callbackUrlPlaceholder": "请输入你的业务回调链接", "scopes": "授权范围", "callbackTip": "你需要将此链接配置到对应身份源的回调地址中", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}}, "linkedin": {"basicConfig": "基础配置", "uniqueId": {"label": "唯一标识", "placeholder": "请输入唯一标识"}, "displayName": {"label": "显示名称", "placeholder": "该名称将会展示在用户端登录界面的按钮上"}, "clientId": {"label": "Client ID", "placeholder": "请输入 Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "请输入 Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "请输入你的业务回调链接"}, "callback": {"label": "回调地址", "tip": "你需要将此链接配置到对应身份源的回调地址中"}, "loginMode": {"label": "登录模式", "options": {"register": "可用于登录注册", "login": "仅用于登录"}}, "accountBinding": {"label": "账号绑定", "tip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。"}, "copy": {"success": "复制成功", "failed": "复制失败"}}}, "userManager": {"title": "用户管理", "list": {"title": "用户列表", "detail": "用户详情", "emptyDescription": "当前用户池的所有用户，在这里你可以对用户进行统一管理。", "createUser": "创建用户", "username": "用户名", "phone": "手机号", "email": "邮箱", "lastLoginTime": "最后登录时间", "createdAt": "创建时间", "filter": "筛选", "filterConditions": "筛选条件", "selectAttribute": "请选择属性", "selectRelation": "请选择关系", "inputAttributeValue": "请输入属性值", "addRule": "添加规则", "confirmOperation": "操作确认", "confirmDelete": "确认删除这些数据吗?", "attributes": {"userId": "用户ID", "name": "姓名", "username": "用户名", "status": "账号状态", "workStatus": "工作状态", "gender": "性别", "phone": "手机号", "email": "邮箱"}, "relations": {"equal": "等于", "notEqual": "不等于", "contains": "包含"}}, "group": {"title": "用户组管理", "list": "用户组管理 - 列表", "detail": "用户组管理 - 详情", "create": "用户组管理 - 创建", "emptyDescription": "根据用户的属性是否符合当前设定的规则自动添加或移除成员，实现更灵活的用户管控。", "createStaticGroup": "创建静态用户组", "createDynamicGroup": "创建动态用户组", "name": "名称", "uniqueId": "唯一标识", "groupType": "用户组类型", "dynamicGroup": "动态用户组", "staticGroup": "静态用户组", "totalUsers": "全部用户数", "authRules": "授权规则数", "filter": "筛选", "searchPlaceholder": "搜索用户组名称、Code", "confirmOperation": "操作确认", "confirmDelete": "确认删除这些数据吗?", "back": "返回", "groupName": "用户组名称", "inputGroupName": "请输入用户组名称", "groupCode": "用户组标识符", "inputGroupCode": "请输入用户组标识符", "description": "描述", "inputDescription": "请输入描述", "groupRules": "用户组规则", "selectAttributeCategory": "请选择属性分类", "userBasicFields": "用户基础字段", "selectAttribute": "请选择属性", "selectRelation": "请选择关系", "selectAttributeValue": "请选择属性值", "inputAttributeValue": "请输入属性值", "addRule": "添加规则", "groupNameRequired": "请输入用户组名称", "groupCodeRequired": "请输入用户组标识符", "updateSuccess": "更新成功", "createSuccess": "创建成功", "updateFailed": "更新失败", "createFailed": "创建失败", "attributes": {"userId": "用户ID", "name": "姓名", "username": "用户名", "status": "账号状态", "workStatus": "工作状态", "gender": "性别", "phone": "手机号", "email": "邮箱"}, "relations": {"equal": "等于", "notEqual": "不等于", "contains": "包含"}}, "organize": {"title": "组织机构", "memberEntry": "成员入职", "searchMemberDepartment": "搜索成员、部门", "create": "新建", "createOrganization": "新建组织", "setDepartmentManager": "设置部门负责人", "addSubDepartment": "添加子部门", "disableOrganization": "禁用组织", "disableDepartment": "禁用部门", "editOrganization": "编辑组织", "editDepartment": "编辑部门", "deleteOrganization": "删除组织", "deleteDepartment": "删除部门", "user": "用户", "phone": "手机号", "email": "邮箱", "accountStatus": "账号状态", "disableAccount": "禁用账号", "processResignation": "办理离职", "changeDepartment": "变更部门", "setMainDepartment": "设置主部门", "setAsManager": "设为负责人", "setPosition": "设置岗位", "confirmOperation": "操作确认", "confirmDelete": "确认删除这些数据吗?"}, "postManagement": {"title": "岗位管理", "create": "创建岗位", "edit": "编辑岗位", "positionName": "岗位名称", "positionCode": "岗位代码", "description": "描述", "departmentId": "部门ID", "createdAt": "创建时间", "updatedAt": "最新编辑时间", "confirmOperation": "操作确认", "confirmDelete": "确认删除这些数据吗?", "back": "返回", "inputPositionName": "请输入岗位名称", "inputPositionCode": "请输入岗位代码，例如：admin", "inputDescription": "请输入岗位描述", "parentDepartment": "上级部门", "positionNameRequired": "请输入岗位名称", "positionCodeRequired": "请输入岗位代码", "organization": "组织机构"}, "form": {"resetPassword": "重置密码", "more": "更多", "disableAccount": "禁用账号", "enableAccount": "启用账号", "deleteAccount": "删除账号", "forceLogout": "强制下线", "confirmOperation": "操作确认", "confirmDelete": "确认删除这些数据吗?", "tabs": {"userInfo": "用户信息", "userAffiliation": "用户归属", "permissionManagement": "权限管理", "applicationAuthorization": "应用授权", "accessLog": "访问日志"}}, "activated": {"title": "确定启用 \"{name}\" 的账号吗？", "description": "启用账号，账号状态将恢复正常，用户可以重新登录应用。", "buttons": {"cancel": "取消", "confirm": "确定"}}, "create": {"title": "创建用户", "accountType": {"username": "用户名", "phone": "手机号", "email": "邮箱"}, "form": {"username": {"label": "用户名", "placeholder": "请输入用户名", "required": "请输入用户名"}, "phone": {"label": "手机号", "placeholder": "请输入手机号", "required": "请输入手机号"}, "email": {"label": "邮箱", "placeholder": "请输入邮箱", "required": "请输入邮箱"}, "password": {"label": "密码", "placeholder": "请输入密码", "required": "请输入密码", "generate": "自动生成密码"}, "confirmPassword": {"label": "确认密码", "placeholder": "请再次输入密码", "required": "请再次输入密码", "notMatch": "两次输入的密码不一致"}, "loginUrl": {"label": "发送首次登录地址"}, "selectPool": {"label": "选择用户池或应用", "placeholder": "Select", "preview": "预览"}, "resetPassword": {"label": "强制用户在首次登录时修改密码"}}, "buttons": {"cancel": "取消", "confirm": "确认"}}, "disable": {"title": "确定禁用 \"{name}\" 的账号吗？", "description": {"line1": "禁用的账号将无法登录应用以及重置密码", "line2": "账号禁用期间，仍可编辑用户信息", "line3": "禁用账号可以恢复"}, "buttons": {"cancel": "取消", "confirm": "确定"}}, "resetPwd": {"title": "重置密码", "form": {"newPassword": {"label": "新密码", "placeholder": "请输入新密码", "required": "请输入新密码", "generate": "自动生成密码"}, "sendPassword": {"label": "发送重置后的密码给用户"}, "email": {"label": "邮箱", "placeholder": "请输入邮箱"}, "phone": {"label": "手机号", "placeholder": "请输入手机号"}, "application": {"label": "选择用户登录的应用", "placeholder": "请选择"}, "forceChange": {"label": "强制用户登录后修改密码"}}, "buttons": {"cancel": "取消", "confirm": "确定"}}, "rights": {"title": "数据资源策略", "search": {"placeholder": "搜索数据策略名称、主体、数据资源"}, "authorize": "授权", "columns": {"selection": "选择", "policyName": "数据策略名称", "policyDescription": "数据策略描述", "authorizedSubject": "被授权主体", "relatedResource": "关联数据资源", "lastEditTime": "最新编辑时间"}}}, "permission": {"title": "权限管理", "role": {"title": "角色管理", "create": "创建角色", "edit": "编辑角色", "roleName": "角色名称", "roleCode": "角色代码", "description": "描述", "status": "状态", "enabled": "启用", "disabled": "禁用", "createdAt": "创建时间", "filter": "筛选", "searchPlaceholder": "搜索角色名称、Code", "confirmOperation": "操作确认", "confirmDelete": "确认删除这些数据吗?", "createSuccess": "创建成功", "updateSuccess": "更新成功", "deleteSuccess": "删除成功", "operationFailed": "操作失败，请重试", "back": "返回", "reset": "重置", "save": "保存", "basicInfo": "基本信息", "inputRoleName": "请输入角色名称", "inputRoleCode": "请输入角色 code，例如：admin", "disableTime": "角色自动禁用时间", "selectDateTime": "选择日期时间", "inputDescription": "请输入角色描述", "permissions": "权限", "selectAll": "全选", "deselectAll": "取消全选", "submit": "提交", "completePermissions": "请完善角色权限", "goToPermissions": "前往", "roleNameRequired": "请输入角色名称", "roleCodeRequired": "请输入角色 Code"}}, "branding": {"title": "品牌化", "message": "消息设置", "emailTemplates": "邮件模板", "thirdPartyEmailService": {"title": "第三方邮件服务", "enableThirdParty": "开启第三方服务", "custom": "自定义", "tencentEnterprise": "腾讯企业邮箱", "aliyunEnterprise": "阿里邮箱企业版", "sendgrid": "SendGrid", "smtpAddress": "SMTP 地址", "port": "端口", "username": "用户名", "password": "密码", "senderEmail": "发件者邮箱", "securityVerification": "安全验证", "none": "无", "save": "保存", "saveAndTest": "保存并发送测试邮件", "inputSmtpAddress": "请输入 SMTP 地址", "inputPort": "请输入端口", "inputUsername": "请输入用户名", "inputPassword": "请输入密码", "inputSenderEmail": "请输入发件者邮箱", "inputApiKey": "请输入 API Key"}, "smsVerificationService": "短信验证服务", "editAndPreview": "编辑并预览", "templates": {"notification": "通知模版", "verificationCode": "注册/登录验证码模版", "pipeline": "Pipeline 相关模板", "verification": "验证模版", "password": "密码相关模板", "emailBinding": "邮箱绑定模版", "identityAlert": "身份自动化告警模板"}, "emailItems": {"welcome": "欢迎邮件", "userCreation": "首次创建用户通知", "invitation": "邀请邮件", "registerCode": "注册验证码", "loginCode": "登录验证码", "mfaLoginCode": "MFA 登录验证码", "infoCompletionCode": "信息补全验证码", "pipelineError": "Pipeline 执行报错", "firstEmailLogin": "首次邮箱登录验证", "consoleVerification": "控制台发起验证", "passwordExpiration": "密码到期提醒", "adminResetPassword": "管理员重置密码提醒", "passwordChangeNotice": "账户密码修改提醒", "selfUnlockCode": "自助解锁验证码", "resetPasswordCode": "重置密码验证码", "emailUnbindCode": "邮箱解绑验证码", "emailBindCode": "邮箱绑定验证码", "workflowFailure": "工作流执行失败", "workflowTimeout": "工作流运行超时", "createDataFailure": "创建数据失败", "updateDataFailure": "更新数据失败", "deleteDataFailure": "删除数据失败"}}, "security": {"title": "安全设置", "mfa1": "多因素认证", "mfa": {"simpleMode": "简单模式", "advancedMode": "高级模式", "authFactors": "身份认证因素", "sms": {"title": "短信验证码", "description": "使用短信形式接收验证码认证登录"}, "email": {"title": "电子邮箱验证", "description": "使用邮件形式接收验证码认证登录"}, "otp": {"title": "OTP 口令", "description": "使用 OTP 一次性口令密码认证登录"}, "enableSuccess": "开启成功", "enableFailed": "开启失败，请重试", "disableSuccess": "关闭成功", "disableFailed": "关闭失败，请重试"}}}, "frontend": {"back": "返回", "tabs": {"application": "应用配置", "protocol": "协议配置", "loginControl": "登录控制", "accessAuth": "访问授权"}}, "application": {"basicInfo": {"title": "基本信息", "appName": "应用名称", "appDescription": "应用描述", "appLogo": "应用 logo", "visibility": "可见范围", "web": "Web 端", "mobile": "移动端"}, "endpointInfo": {"title": "端点信息", "appId": "App ID", "jwksEndpoint": "JWKS 公钥端点", "appSecret": "App Secret", "authEndpoint": "认证端点", "tokenEndpoint": "Token 端点", "issuer": "Issuer", "userInfoEndpoint": "用户信息端点", "discoveryEndpoint": "服务发现地址", "logoutEndpoint": "登出端点"}, "authConfig": {"title": "认证配置", "authAddress": "认证地址", "loginCallbackUrl": "登录回调 URL", "logoutCallbackUrl": "登出回调 URL", "initiateLoginUrl": "发起登录 URL", "multipleUrlTip": "多个 URL 用英文逗号「,」隔开"}, "buttons": {"save": "保存", "reset": "重置"}, "validation": {"appNameRequired": "请输入「应用名称」", "authAddressRequired": "请输入「认证地址」", "loginCallbackRequired": "请输入「登录回调 URL」"}, "messages": {"copySuccess": "复制成功", "copyFailed": "复制失败"}}, "loginCtrl": {"title": "登录注册控制", "defaultSettings": "默认设置", "registrationSettings": "注册设置", "loginWay": {"mergedTitle": "登录注册合并", "mergedDesc": "单一页面，无缝的注册和登录体验", "separateTitle": "登录注册分页", "separateDesc": "独立两页面，先注册再登录"}, "table": {"sort": "排序", "accountField": "账号字段", "authMethod": "认证方式", "useForLogin": "用于登录", "useForRegister": "用于注册"}, "authMethod": {"password": "密码", "verifyCode": "验证码"}, "placeholder": {"selectLoginField": "请选择用于登录的字段", "selectDefaultLoginMethod": "请选择默认登录方式", "selectDefaultRegisterMethod": "请选择默认注册方式"}, "buttons": {"addAccountField": "新增账号字段", "save": "保存", "reset": "重置"}, "defaultLoginMethod": {"title": "默认登录方式", "description": "用户登录时默认看到的登录方式"}, "defaultRegisterMethod": {"title": "默认注册方式", "description": "用户注册时默认看到的注册方式"}, "socialLogin": {"title": "社交登录方式", "addIdentitySource": "添加身份源"}, "relatedApps": {"title": "关联客户端应用", "noApps": "暂无客户端应用可关联，请先创建客户端应用"}, "defaultLoginType": {"title": "默认登录方式", "description": "用户登录时默认看到的登录方式", "normal": "常规登录", "qrCode": "扫码登录"}, "onlineUsers": {"title": "在线用户", "columns": {"userInfo": "用户信息", "phone": "手机号", "email": "邮箱", "loginCount": "登录次数", "lastLoginTime": "最后登录时间"}}, "autoRegisterLogin": {"title": "自动注册并登录", "description": "当用户使用未注册的账号登录时，系统会自动为其注册账号并登录"}, "mergeLoginRegister": {"title": "登录注册合并", "description": "开启后，登录界面将包含注册选项，用户可以直接在登录界面完成注册"}, "loginMethodsSort": {"title": "登录方式排序", "description": "拖动调整登录方式的显示顺序", "columns": {"sort": "排序", "loginMethod": "登录方式", "operations": "操作"}, "buttons": {"moveUp": "上移", "moveDown": "下移"}}, "passwordLoginConfig": {"title": "密码登录配置", "description": "配置可用于密码登录的方式", "options": {"phonePassword": "手机号密码", "emailPassword": "邮箱密码", "usernamePassword": "用户名密码"}}, "verifyCodeLoginConfig": {"title": "验证码登录配置", "description": "配置可用于验证码登录的方式", "options": {"phoneCode": "手机号验证码", "emailCode": "邮箱验证码"}}, "loginOptions": {"phoneCode": "手机号验证码", "password": "密码"}, "registerOptions": {"phone": "手机号", "emailCode": "邮箱验证码", "phonePassword": "手机号密码", "usernamePassword": "用户名密码"}, "methodLabels": {"phoneCode": "手机号验证码", "emailCode": "邮箱验证码", "phonePassword": "手机号密码", "emailPassword": "邮箱密码", "usernamePassword": "用户名密码"}, "fieldOptions": {"identityNumber": "居民身份证号-identityNumber", "externalId": "原系统ID-externalId"}, "messages": {"saveSuccess": "保存成功", "saveFailed": "保存失败", "saveError": "保存失败，请稍后重试"}}, "protocol": {"title": "协议配置", "selectProtocol": "选择协议", "save": "保存", "reset": "重置", "add": "添加", "delete": "删除", "protocolTypes": {"OIDC": "OpenID Connect", "OAUTH": "OAuth 2.0", "SAML": "SAML 2.0", "CAS": "CAS"}, "enableProtocol": "启用协议", "tabs": {"basic": "基础设置", "jwt": "JWT 设置", "claim": "Claim 配置", "scope": "Scope 配置", "endpoint": "端点设置", "advanced": "高级设置"}, "oidc": {"title": "OIDC 配置", "tokenExpiry": "令牌过期时间", "authorizationCode": "授权码", "idToken": "身份令牌", "accessToken": "访问令牌", "refreshToken": "刷新令牌", "exchangeVerificationMethod": "换取 token 身份验证方式", "inspectionVerificationMethod": "检验 token 身份验证方式", "withdrawalVerificationMethod": "撤回 token 身份验证方式", "authorizationCodeTime": "授权码过期时间", "IdTokenTime": "id_token 过期时间", "AccessTokenTime": "access_token 过期时间", "RefreshTokenTime": "refresh_token 过期时间", "claimMapping": "声明映射", "addMappingField": "添加映射字段", "configureScope": "配置作用域", "selectClaims": "选择声明", "createCustomScope": "创建自定义作用域", "units": {"seconds": "秒", "minutes": "分钟", "hours": "小时", "days": "天"}, "grantTypes": {"title": "授权类型", "authorizationCode": "授权码", "implicit": "隐式授权", "password": "密码模式", "clientCredentials": "客户端凭证", "refreshToken": "刷新令牌"}, "responseTypes": {"title": "响应类型", "code": "授权码", "token": "令牌", "idToken": "ID 令牌"}, "pkce": {"title": "PKCE 设置", "required": "必需", "optional": "可选", "disabled": "禁用"}, "authorizationModes": "授权模式", "options": {"enableRefreshTokenRotation": "启用 refresh_token 轮换", "refreshTokenLifetime": "轮换 refresh_token 时，同时刷新 refresh_token 有效时间", "noForceHttpsForImplicit": "不强制 implicit 模式回调链接为 https", "enableIdTokenEncryption": "启用 id_token 加密", "userConsentPage": "用户知情同意页面", "idTokenPayloadEncryption": "ID Token Payload 的对称加密算法", "idTokenKeyEncryption": "ID Token Payload 加密密鑰的非對稱加密算法", "asymmetricPublicKey": "非對稱加密使用的公鑰"}, "encryption": {"algorithm": {"a128cbcHs256": "A128CBC-HS256", "a128gcm": "A128GCM", "a256cbcHs512": "A256CBC-HS512", "a256gcm": "A256GCM"}, "method": {"rsaOaep": "RSA-OAEP", "ecdhEs": "ECDH-ES"}}}, "saml": {"title": "SAML 配置", "entityId": "实体 ID", "acsConfig": {"title": "ACS 地址配置", "responseMode": "响应模式", "address": "地址", "addACS": "添加 ACS 地址", "modes": {"httpPost": "HTTP-POST", "httpRedirect": "HTTP-REDIRECT"}}, "customAttributes": {"title": "自定义属性", "label": "标签", "way": "方法", "value": "值", "addAttribute": "添加属性", "ways": {"basic": "基础", "advanced": "高级"}}, "cert": {"title": "证书配置", "upload": "上传证书", "generate": "生成证书"}, "signatureAlgorithm": "签名算法", "algorithms": {"sha1": "SHA-1", "sha256": "SHA-256", "sha512": "SHA-512"}}, "claim": {"title": "声明配置", "add": "添加声明", "name": "名称", "value": "值", "tips": {"duplicateName": "名称已存在", "required": "必填项"}}, "scope": {"title": "作用域配置", "add": "添加作用域", "name": "名称", "description": "描述", "claims": "包含的声明", "tips": {"duplicateName": "名称已存在", "required": "必填项"}}, "jwt": {"title": "JWT 配置", "algorithm": "签名算法", "keyPair": "密钥对", "generate": "生成新密钥", "algorithms": {"rs256": "RS256", "rs384": "RS384", "rs512": "RS512", "hs256": "HS256", "hs384": "HS384", "hs512": "HS512"}}, "buttons": {"save": "保存", "reset": "重置", "add": "添加", "delete": "删除"}, "messages": {"saveSuccess": "保存成功", "saveFailed": "保存失败", "confirmDelete": "确认删除此项？", "generateSuccess": "生成成功", "generateFailed": "生成失败"}, "alert": {"oidcInfo": "当你使用 Authing 的身份认证功能时，默认使用 OIDC（OpenID Connect）协议进行认证。", "whichAuthMode": "不清楚应该选用哪种授权模式？", "oauthDeprecated": "【重要提示】不再推荐使用 OAuth2.0，建议切换到 OIDC，具体请查看文档：", "implementSSO": "实现单点登录", "samlConfig": "将你的应用配置成为 SAML 身份源，若有任何问题，请", "clickForHelp": "点此查看帮助文档", "casConfig": "将你的应用配置成为 CAS 身份源，详细信息请", "viewDocs": "查看文档"}, "oauth": {"enableOAuth2": "启用 OAuth 2.0", "selectClaims": "选择声明", "createCustomScope": "创建自定义作用域", "scopeExists": "作用域已存在", "configureScope": "配置作用域", "selectUserField": "选择用户字段", "grantTypes": "授权类型", "tokenAuthentication": "令牌认证方式", "tokenEndpointAuthMethod": "令牌端点认证方式", "clientAuthentication": "客户端认证方式", "clientSecret": "客户端密钥", "clientId": "客户端 ID", "withdrawTokenAuthentication": "撤回令牌认证方式", "withdrawTokenEndpointAuthMethod": "撤回令牌端点认证方式", "withdrawClientAuthentication": "撤回客户端认证方式", "withdrawClientSecret": "撤回客户端密钥", "withdrawClientId": "撤回客户端 ID"}, "cas": {"casVersion": "CAS 版本", "enableCAS": "启用 CAS", "loginUrl": "登录 URL", "logoutUrl": "登出 URL", "loginEndpoint": "登录端点", "logoutEndpoint": "登出端点", "serviceValidateEndpoint": "服务验证端点", "serviceValidateEndpoint2": "服务验证端点 2", "serviceTicketExpiry": "服务票证过期时间", "userId": "用户 ID 字段", "customResponseFields": "自定义响应字段", "addField": "添加字段", "logoutUri": "登出 URI", "serverPrefix": "服务器前缀", "ticketValidateVersion": "票证验证版本", "ticketValidatePath": "票证验证路径", "ticketExpiry": "票证过期时间", "tgtExpiry": "TGT 过期时间", "enterFieldName": "请输入字段名称", "enterFieldValue": "请输入字段值", "userIdPlaceholder": "请输入用户 ID"}}, "azureAd": {"basicConfig": "基础配置", "appLogo": "应用 Logo", "logoTip": "如果设置，Authing 登录表单将在使用 {Display Name} 登录的按钮上显示此图标，该图标会展示为 20 * 20。", "uniqueId": "唯一标识", "inputUniqueId": "请输入唯一标识", "displayName": "显示名称", "domainName": "身份源域名", "tenantIdPlaceholder": "请输入 Azure Active Directory 应用所在的 Tenant ID，不填写默认选择认证账户类型为 organizations", "clientIdPlaceholder": "请输入 Azure Active Directory 应用的 Client ID", "clientSecretPlaceholder": "请输入 Azure Active Directory 应用的 Client Secret", "emailVerificationSync": "邮箱验证同步策略", "emailVerificationFalse": "始终设置 emailVerified 为 false", "emailVerificationTrue": "始终设置 emailVerified 为 true", "userInteractionType": "用户交互类型", "interactionNoPrompt": "NoPrompt - 无 prompt 参数选项", "interactionLogin": "login - 强制用户在该请求上输入凭据，取消单一登录", "interactionNone": "none - 与 login 相反，确保不向用户显示任何交互式提示。如果请求无法通过单一登录以无提示方式完成，则 Microsoft 标识平台将返回 interaction_required 错误", "interactionConsent": "consent - 用户登录之后，要求用户向应用授予权限", "interactionSelectAccount": "select_account - 显示在会话中记住的所有账户", "interactionTip": "提示身份提供商在用户进行身份验证和授权过程时需要采取的操作。", "callbackUrl": "回调地址", "callbackTip": "你需要将此链接设置为 Azure AD 应用的回调链接，请查看 Azure AD 的文档说明。", "loginMode": "登录模式", "loginAndRegister": "可用于登录注册", "loginOnly": "仅用于登录", "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "uploadFailed": "上传失败"}, "enabled": "启用", "disabled": "禁用", "operations": "操作", "cancel": "取消", "confirm": "确认", "create": "创建", "department": {"createDepartment": "新建部门", "editDepartment": "编辑部门", "departmentName": "部门名称", "inputDepartmentName": "请输入部门名称", "departmentCode": "部门Code", "inputDepartmentCode": "请输入部门Code", "codeTooltip": "部门的唯一标识，同一级别下唯一，可用于数据交换。", "departmentDescription": "部门描述", "inputDepartmentDescription": "请输入部门描述", "associatedPositions": "关联岗位", "selectAssociatedPositions": "请选择关联岗位", "parentDepartment": "上级部门", "pleaseEnterDepartmentName": "请输入部门名称", "pleaseSelectParentDepartment": "请选择上级部门", "parentDepartmentCannotBeEmpty": "上级部门不能为空", "organizationalStructure": "组织机构", "setDepartmentManager": "设置部门负责人", "searchOrSelectMembers": "搜索或选择下方成员", "selectedMembers": "已选：{count} 名成员"}, "common": {"selectAll": "全选", "clear": "清空", "cancel": "取消", "confirm": "确定"}, "member": {"memberEntry": "成员入职", "name": "姓名", "inputName": "请输入姓名", "phoneNumber": "手机号", "inputPhoneNumber": "请输入手机号", "email": "邮箱", "inputEmail": "请输入邮箱", "password": "密码", "inputPassword": "请输入密码", "generatePassword": "自动生成密码", "position": "所属岗位", "selectPosition": "请选择关联岗位", "forceResetPasswordOnFirstLogin": "强制用户在首次登录时修改密码", "nameRequired": "请输入姓名", "nameLengthLimit": "姓名长度应在 2 到 20 个字符之间", "phoneRequired": "请输入手机号", "emailRequired": "请输入邮箱", "passwordRequired": "请输入密码", "passwordLengthLimit": "密码长度不能少于 8 个字符", "positionRequired": "请选择所属岗位", "departmentRequired": "请至少选择一个部门"}, "organization": {"createOrganization": "新建组织", "editOrganization": "编辑组织", "name": "组织名称", "inputName": "请输入组织名称", "code": "组织Code", "inputCode": "请输入组织Code", "codeTooltip": "组织的唯一标识符，同一级别下唯一，可用于数据交换。", "description": "组织描述", "inputDescription": "请输入组织描述", "relatedPositions": "关联岗位", "selectRelatedPositions": "请选择关联岗位", "nameRequired": "请输入组织名称", "codeRequired": "请输入组织Code"}, "identity": {"oauth2": {"appLogo": "应用 Logo", "logoTip": "如果设置，Authing 登录表单将在「使用 {displayName} 登录」的按钮上显示此图标，该图标会展示为 20 * 20。", "uniqueId": "唯一标识", "inputUniqueId": "请输入唯一标识", "displayName": "显示名称", "inputDisplayName": "请输入显示名称", "authorizeUrl": "授权 URL", "inputAuthorizeUrl": "https://idp.example.com/authorize", "tokenUrl": "Token URL", "inputTokenUrl": "https://idp.example.com/token", "authorizeScope": "Scope 授权范围", "inputAuthorizeScope": "请输入 Scope 授权范围", "scopeTip": "请使用空格分隔不同的授权域", "clientId": "Client ID", "inputClientId": "请输入 OAuth2 应用的 Client ID", "clientSecret": "Client Secret", "inputClientSecret": "请输入 Client Secret", "clientSecretTip": "你可以使用 ${authEndPoint}、${tokenEndPoint}、${scope}、${clientId}、${clientSecret} 这些宏在此拼接授权 URL。例如：${authEndPoint}?client_id=${clientId}&scope=${scope}&response_type=code&state=12345", "codeToTokenScript": "Code 换 Token 脚本", "tokenToUserInfoScript": "Token 换用户信息脚本", "loginMode": "登录模式", "loginAndRegister": "可用于登录注册", "loginOnly": "仅用于登录", "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "uploadFailed": "上传失败"}, "facebook": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "appId": "应用编号", "appIdPlaceholder": "请输入应用编号", "appSecret": "应用密钥", "appSecretPlaceholder": "请输入应用密钥", "callbackUrl": "回调地址", "callbackUrlPlaceholder": "请输入你的业务回调链接", "scopes": "授权范围", "callbackTip": "你需要将此链接配置到对应身份源的回调地址中", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}}, "facebookMobile": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "appId": "应用编号", "appIdPlaceholder": "请输入应用编号", "appSecret": "应用密钥", "appSecretPlaceholder": "请输入应用密钥", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}}, "github": {"basicConfig": "基础配置", "uniqueId": "唯一标识", "uniqueIdPlaceholder": "请输入唯一标识", "displayName": "显示名称", "displayNamePlaceholder": "该名称将会展示在用户端登录界面的按钮上", "clientId": "Client ID", "clientIdPlaceholder": "请输入 GitHub 提供的 Client ID", "clientSecret": "Client secrets", "clientSecretPlaceholder": "请输入 GitHub 提供的 Client secrets", "callbackUrl": "回调地址", "callbackUrlPlaceholder": "请输入你的业务回调链接", "scopes": "授权范围", "callbackTip": "你需要将此链接配置到对应身份源的回调地址中", "loginMode": "登录模式", "loginModeOptions": {"register": "可用于登录注册", "login": "仅用于登录"}, "accountBinding": "账号绑定", "accountBindingTip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。", "copy": {"success": "复制成功", "failed": "复制失败"}}, "githubMobile": {"basicConfig": "基础配置", "uniqueId": {"label": "唯一标识", "placeholder": "请输入唯一标识"}, "displayName": {"label": "显示名称", "placeholder": "该名称将会展示在用户端登录界面的按钮上"}, "clientId": {"label": "Client ID", "placeholder": "请输入 GitHub 提供的 Client ID"}, "clientSecret": {"label": "Client secrets", "placeholder": "请输入 GitHub 提供的 Client secrets"}, "loginMode": {"label": "登录模式", "options": {"register": "可用于登录注册", "login": "仅用于登录"}}, "accountBinding": {"label": "账号绑定", "tip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。"}, "callback": {"label": "回调地址", "tip": "你需要将此链接配置到对应身份源的回调地址中"}, "copy": {"success": "复制成功", "failed": "复制失败"}}, "googleMobile": {"basicConfig": "基础配置", "uniqueId": {"label": "唯一标识", "placeholder": "请输入唯一标识"}, "displayName": {"label": "显示名称", "placeholder": "该名称将会展示在用户端登录界面的按钮上"}, "clientId": {"label": "Client ID", "placeholder": "请输入 Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "请输入 Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "请输入你的业务回调链接"}, "callback": {"label": "回调地址", "tip": "你需要将此链接配置到对应身份源的回调地址中"}, "loginMode": {"label": "登录模式", "options": {"register": "可用于登录注册", "login": "仅用于登录"}}, "accountBinding": {"label": "账号绑定", "tip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。"}, "copy": {"success": "复制成功", "failed": "复制失败"}}, "googleWeb": {"basicConfig": "基础配置", "uniqueId": {"label": "唯一标识", "placeholder": "请输入唯一标识"}, "displayName": {"label": "显示名称", "placeholder": "该名称将会展示在用户端登录界面的按钮上"}, "clientId": {"label": "Client ID", "placeholder": "请输入 Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "请输入 Client Secret"}, "scopes": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "授权 Google 2.0 范围时需要的 scope， 以英文逗号分隔，默认为 email, profile, openid"}, "domainVerification": {"fileName": {"label": "Domain Verification File Name", "placeholder": "请输入 Google 域名验证 HTML 文件名，如 xxxx.html"}, "fileContent": {"label": "Domain Verification File Content", "placeholder": "请输入 Google 域名验证 HTML 文件内容"}}, "accessType": {"label": "是否获取刷新令牌 access_type", "tip": "离线配置对象配置访问令牌，客户端对象就会根据需要刷新访问令牌", "options": {"online": "Online", "offline": "Offline"}}, "prompt": {"label": "登录授权提示 prompt", "tip": "再次向用户显示授权提示", "options": {"firstTime": "仅首次展示", "none": "none", "consent": "consent", "selectAccount": "select_account"}}, "incrementalAuth": {"label": "增量授权", "tip": "增量授权，在您需要时请求资源授权。", "options": {"true": "True", "false": "False"}}, "callbackUrl": {"label": "Callback URL", "placeholder": "请输入你的业务回调链接"}, "callback": {"label": "回调地址", "tip": "你需要将此链接配置到对应身份源的回调地址中"}, "loginMode": {"label": "登录模式", "options": {"register": "可用于登录注册", "login": "仅用于登录"}}, "accountBinding": {"label": "账号绑定", "tip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。"}, "associationMethod": {"label": "关联方式", "options": {"fieldMatch": "字段匹配"}}, "fieldType": {"label": "字段类型", "options": {"email": "邮箱"}}, "copy": {"success": "复制成功", "failed": "复制失败"}}, "linkedin": {"basicConfig": "基础配置", "uniqueId": {"label": "唯一标识", "placeholder": "请输入唯一标识"}, "displayName": {"label": "显示名称", "placeholder": "该名称将会展示在用户端登录界面的按钮上"}, "clientId": {"label": "Client ID", "placeholder": "请输入 Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "请输入 Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "请输入你的业务回调链接"}, "callback": {"label": "回调地址", "tip": "你需要将此链接配置到对应身份源的回调地址中"}, "loginMode": {"label": "登录模式", "options": {"register": "可用于登录注册", "login": "仅用于登录"}}, "accountBinding": {"label": "账号绑定", "tip": "对于用户池中已存在的账号，用户可以将这种社交登录方式绑定至已有账号。"}, "copy": {"success": "复制成功", "failed": "复制失败"}}}, "permission": {"role": {"role": {"back": "返回", "edit": "编辑角色", "basicInfo": "基本信息", "roleName": "角色名称", "roleNamePlaceholder": "请输入角色名称", "roleCode": "角色 Code", "roleCodePlaceholder": "请输入角色 code，例如：admin", "description": "角色描述", "descriptionPlaceholder": "请输入角色描述", "expirationDate": "角色自动禁用时间", "selectPermissions": "选择权限", "cancel": "取消", "permissionGroup": {"selectAll": "全选", "deselectAll": "取消全选"}, "update": "更新", "create": "创建", "validation": {"roleNameRequired": "请输入角色名称", "roleCodeRequired": "请输入角色 Code"}}, "form": {"title": {"create": "创建角色", "edit": "编辑角色"}, "roleName": "角色名称", "roleNamePlaceholder": "请输入角色名称", "roleCode": "角色 Code", "roleCodePlaceholder": "请输入角色 code，例如：admin", "permissionSpace": "权限空间", "permissionSpacePlaceholder": "请选择权限空间", "permissionSpace1": "权限空间1", "permissionSpace2": "权限空间2", "description": "角色描述", "descriptionPlaceholder": "请输入角色描述", "expirationDate": "角色自动禁用时间", "buttons": {"cancel": "取消", "confirm": "确定"}}}, "list": {"searchPlaceholder": "搜索角色名称、Code", "search": "搜索", "columns": {"id": "ID", "roleName": "角色名称", "roleCode": "角色代码", "description": "描述", "status": "状态", "createdAt": "创建时间", "operations": "操作"}, "status": {"enabled": "启用", "disabled": "禁用"}, "operations": {"edit": "编辑", "delete": "删除"}, "deleteConfirm": {"title": "警告", "message": "确定要删除角色 \"{name}\" 吗？", "confirm": "确定", "cancel": "取消"}, "messages": {"deleteSuccess": "成功删除角色 {name}", "deleteFailed": "删除角色失败", "fetchFailed": "获取角色列表失败"}}}, "userGroup": {"info": "用户组信息", "form": {"groupName": {"label": "用户组名称", "placeholder": "请输入用户组名称", "required": "请输入用户组名称"}, "groupIdentifier": {"label": "用户组标识符", "placeholder": "请输入用户组标识符", "required": "请输入用户组标识符"}, "description": {"label": "描述", "placeholder": "请输入描述"}, "rules": {"title": "用户组规则", "category": {"label": "请选择属性分类", "userBasic": "用户基础字段"}, "attribute": {"label": "请选择属性"}, "relation": {"label": "请选择关系", "equal": "等于", "notEqual": "不等于", "contains": "包含"}, "value": {"label": "请选择属性值", "placeholder": "请输入属性值"}}, "addRule": "添加规则", "confirm": "确认", "create": "创建", "buttons": {"confirm": "确认", "create": "创建"}}}, "userAffi": {"department": {"title": "所属部门", "entry": "办理入职", "empty": "暂无部门信息", "dialog": {"title": "变更部门", "changeTip": "将\"{name}\"的部门变更为:", "organization": "组织机构", "selected": "已选 · {count}个部门", "clear": "清空", "mainDepartment": "主部门", "setMain": "设为主部门", "buttons": {"cancel": "取消", "confirm": "确定"}}}, "post": {"title": "所属岗位", "setPost": "设置岗位", "empty": "暂无岗位", "dialog": {"title": "设置岗位", "placeholder": "请选择关联岗位", "emptyTip": "暂无相关岗位", "create": "去创建", "buttons": {"cancel": "取消", "confirm": "确定"}}}, "tenant": {"title": "所属租户", "columns": {"selection": "选择", "info": "租户信息", "department": "租户部门", "application": "应用信息"}, "dialog": {"title": "暂无分组", "tip": "暂时没有分组信息，您可以创建分组，新建完成后再给此用户添加分组", "buttons": {"cancel": "取消", "create": "前往新建"}}}, "userGroup": {"title": "所属用户组", "addStatic": "添加静态用户组", "columns": {"name": "组名", "code": "唯一标识", "description": "简介", "type": "用户组类型", "operations": "操作"}, "type": {"dynamic": "动态用户组", "static": "静态用户组"}, "dialog": {"title": "选择用户组", "label": "选择用户组:", "placeholder": "选择用户组", "buttons": {"cancel": "取消", "confirm": "确定", "create": "前往新建"}}, "delete": {"title": "操作", "confirm": "确定删除该数据吗?"}}, "userRole": {"title": "用户角色", "addRole": "添加角色", "columns": {"roleId": "角色 ID", "roleCode": "角色 Code", "roleName": "角色名称", "description": "角色描述", "operations": "操作"}, "dialog": {"title": "用户角色", "label": "选择角色:", "placeholder": "选择角色", "rules": {"required": "请选择角色"}, "buttons": {"cancel": "取消", "confirm": "确定"}}, "delete": {"title": "操作", "confirm": "确定删除该数据吗?"}}}, "userManager": {"userInfo": {"accountInfo": {"title": "账号信息", "createdTime": "创建时间", "lastLoginTime": "最后登录时间", "lastLoginIp": "最后登录 IP", "loginCount": "登录次数", "userSource": "用户来源", "userType": "用户类型", "registerAlias": "注册别名", "registerDevice": "注册设备", "passwordExpireTime": "密码过期时间"}, "thirdParty": {"title": "已绑定第三方账号信息", "empty": "未绑定任何第三方账号，您可以绑定第三方账号进行快捷登录"}, "loginHistory": {"title": "历史登录应用"}, "personalInfo": {"title": "个人信息", "form": {"name": {"label": "姓名", "placeholder": "请输入姓名"}, "username": {"label": "用户名", "placeholder": "请输入用户名"}, "email": {"label": "邮箱", "placeholder": "请输入邮箱", "sendVerify": "发送验证"}, "gender": {"label": "性别", "placeholder": "请选择", "options": {"male": "男", "female": "女", "unknown": "未知"}}, "birthdate": {"label": "生日", "placeholder": "请输入生日"}, "phone": {"label": "手机号", "placeholder": "请输入手机号"}, "country": {"label": "国家", "placeholder": "请输入国家代码"}, "city": {"label": "城市", "placeholder": "请输入城市"}, "company": {"label": "公司", "placeholder": "请输入公司名称"}, "province": {"label": "省份", "placeholder": "请输入省/区"}, "address": {"label": "街道地址", "placeholder": "请输入街道地址"}, "externalId": {"label": "原系统ID", "placeholder": "请输入原系统ID (externalId)"}, "postalCode": {"label": "邮政编码", "placeholder": "请输入邮政编码"}}, "buttons": {"save": "保存", "reset": "重置"}}, "jsonInfo": {"title": "个人信息", "copy": "复制", "copySuccess": "复制成功", "copyFailed": "复制失败"}}}}