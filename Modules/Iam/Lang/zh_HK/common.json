{"login": {"title": "歡迎回來！登入您的帳戶", "register": "註冊", "loading": "加載中...", "verificationCodeLogin": "驗證碼登錄", "passwordLogin": "密碼登錄", "emailCodeLogin": "郵箱驗證碼登錄", "inputPhoneOrEmail": "請輸入手機號或郵箱", "inputVerifyCode": "請輸入驗證碼", "inputCredential": "請輸入用戶名/郵箱/手機號", "inputPassword": "請輸入密碼", "selectLanguage": "選擇語言", "inputCaptcha": "請輸入驗證碼", "remember": "記住我", "forgot": "忘記密碼？", "submit": "登錄", "or": "或", "continueWithGoogle": "使用 Google 賬號繼續", "resetPassword": "重置密碼", "nextStep": "下一步", "backToLogin": "返回登錄", "phoneRegister": "手機註冊", "emailCodeRegister": "郵箱驗證碼註冊", "emailPasswordRegister": "郵箱密碼註冊", "usernamePasswordRegister": "用戶名密碼註冊", "inputEmail": "請輸入郵箱", "inputUsername": "請輸入用戶名", "confirmPassword": "請確認密碼", "passwordStrength": "密碼強度", "noAccount": "沒有賬號？", "hasAccount": "已有賬號？", "registerNow": "立即註冊", "loginNow": "立即登錄", "googleLoginFailed": "Google 登錄失敗", "mainland": "簡體中文", "hongkong": "繁體中文", "username": "用戶名", "password": "密碼", "verifyCode": "驗證碼", "sendCode": "發送驗證碼", "resendCode": "{time}秒後重新發送", "phoneLogin": "手機號登錄", "emailLogin": "郵箱登錄", "passwordNotMatch": "兩次輸入的密碼不一致", "registerSuccess": "註冊成功", "loginSuccess": "登錄成功", "mfaVerify": "兩步驗證", "mfaTitle": "兩步驗證 · 谷歌身份驗證器", "invalidCredential": "請輸入有效的手機號、郵箱或用戶名", "passwordLength": "密碼長度不能小於6個字元", "verifyCodeLength": "驗證碼長度應為6位", "sendingCode": "發送中...", "codeSent": "驗證碼已發送", "sendFailed": "發送失敗，請重試", "loginFailed": "登錄失敗，請檢查輸入", "networkError": "網絡錯誤，請稍後重試", "weak": "弱", "medium": "中", "strong": "強", "googleLogin": "使用 Google 賬號登錄", "countryCode": "國家/地區代碼", "welcomeBack": "請選擇登入方法: ", "createAccount": "請選擇註冊方法: "}, "userCenter": {"title": "個人中心", "menu": {"language": "語言", "profile": "個人中心", "changePassword": "修改密碼", "logout": "退出登錄"}, "sidebar": {"personalInfo": "個人資訊", "accountBinding": "賬號綁定", "multiFactorAuth": "多因素認證", "passkey": "Passkey", "socialIdentity": "社會化身份源管理", "enterpriseIdentity": "企業化身份源管理", "accountSafety": "賬號安全", "accessLog": "訪問日誌"}, "personal": {"title1": "個人資訊", "edit": "編輯", "save": "確認", "cancel": "取消", "userId": "用戶 ID", "username": "用戶名", "email": "郵箱", "phone": "手機號", "name": "姓名", "title": "職稱", "address": "住址", "id": "id", "gender": "性別", "birthdate": "生日", "company": "公司", "signed_up": "註冊時間", "registrationDate": "註冊時間", "identityNumber": "居民身份證號", "updateSuccess": "更新成功", "updateFailed": "更新失敗，請稍後重試", "fetchFailed": "獲取用戶資訊失敗，請稍後重試", "genderOptions": {"unknown": "未知", "male": "男", "female": "女"}}, "binding": {"phone": {"title": "手機號和郵箱", "modify": "修改", "unbind": "解綁", "bind": "綁定", "current": "當前手機號", "new": "新手機號", "verificationCode": "驗證碼", "send": "發送驗證碼", "resend": "{time}s後重新發送", "oldCode": "舊手機號驗證碼", "newCode": "新手機號驗證碼", "inputCode": "請輸入6位驗證碼", "inputPhone": "請輸入手機號", "inputNewPhone": "請輸入新手機號", "unbindTitle": "解綁手機號", "smsCode": "短信驗證碼"}, "email": {"title": "郵箱驗證", "current": "當前郵箱", "new": "新郵箱", "oldCode": "當前郵箱驗證碼", "newCode": "新郵箱驗證碼", "unbindTitle": "解綁郵箱", "emailCode": "郵箱驗證碼", "inputEmail": "請輸入郵箱", "inputNewEmail": "請輸入新郵箱"}, "messages": {"fillRequired": "請填寫所有必要資訊", "invalidPhone": "請輸入正確的手機號格式", "invalidEmail": "請輸入正確的郵箱格式", "updateSuccess": "更新成功", "updateFailed": "更新失敗，請檢查資訊是否正確", "unbindSuccess": "解綁成功", "unbindFailed": "解綁失敗，請檢查驗證碼是否正確", "codeSent": "驗證碼已發送", "sendFailed": "發送驗證碼失敗，請稍後重試", "inputCode": "請輸入驗證碼"}}, "mfa": {"title": "多因素認證", "sms": {"title": "短信驗證碼", "description": "使用短信形式接收驗證碼認證登錄", "bind": "綁定手機號", "inputPhone": "輸入手機號", "verifyCode": "短信驗證碼", "inputCode": "請輸入6位驗證碼", "sendCode": "發送驗證碼", "resendCode": "{time}s後重新發送"}, "email": {"title": "電子郵箱驗證", "description": "使用郵件形式接收驗證碼認證登錄", "bind": "綁定郵箱", "inputEmail": "輸入郵箱賬號", "verifyCode": "郵箱驗證碼"}, "otp": {"title": "OTP 口令驗證", "description": "使用 OTP 一次性口令認證登錄", "setup": {"title": "綁定 OTP 口令", "step1": "下載驗證器", "step2": "掃描二維碼", "step3": "輸入安全碼", "step4": "解綁 OTP"}}, "passkey": {"title": "創建 Passkey", "description": "借助 Passkey，您可以使用自己的指紋、面孔幕設置或實體安全密鑰錄制的賬號", "noData": "暫無數據", "warning": "請僅在您自有的設備上設置 Passkey", "create": "創建 Passkey", "createSuccess": "Passkey 創建成功"}, "messages": {"bindSuccess": "綁定成功", "bindFailed": "綁定失敗，請重試", "unbindSuccess": "解綁成功", "unbindFailed": "解綁失敗，請重試", "codeSent": "驗證碼已發送", "sendFailed": "發送驗證碼失敗，請稍後重試", "inputRequired": "請填寫所有必要資訊"}, "simpleMode": "簡單模式", "advancedMode": "高級模式", "authFactors": "身份認證因素", "enableSuccess": "開啟成功", "enableFailed": "開啟失敗，請重試", "disableSuccess": "關閉成功", "disableFailed": "關閉失敗，請重試"}, "security": {"title": "賬號安全", "score": "安全評分", "level": {"low": "低", "medium": "中", "high": "高"}, "password": {"title": "密碼設置", "strength": "密碼強度", "weak": "弱", "medium": "中", "strong": "強", "modify": "修改", "old": "原始密碼", "new": "新密碼", "confirm": "確認密碼", "changeSuccess": "密碼修改成功，請重新登錄", "changeFailed": "密碼修改失敗，請重試", "description": "建議使用較為複雜的密碼。", "modifyNow": "立即修改"}, "deleteAccount": {"title": "賬號註銷", "description": "永久刪除賬號和所有數據，請謹慎操作", "warning": "註銷後，此賬號的所有數據都將被刪除且不可逆，請謹慎操作！", "button": "註銷", "confirm": "確認", "cancel": "取消", "inputAccount": "請輸入當前賬號", "inputPassword": "請輸入當前密碼", "inputPhone": "請輸入手機號", "inputCode": "請輸入驗證碼", "sendCode": "發送驗證碼", "resendCode": "{time}s後重新發送", "success": "賬號註銷請求已提交", "failed": "賬號註銷失敗，請檢查輸入資訊是否正確"}, "mfa": {"title": "多因素認證 (MFA)", "enabled": "已開啟", "disabled": "未開啟", "enable": "開啟", "disable": "關閉"}, "email": {"title": "郵箱綁定", "bound": "已綁定", "unbound": "未綁定", "modify": "修改", "bind": "綁定"}, "phone": {"title": "手機綁定", "bound": "已綁定", "unbound": "未綁定", "modify": "修改", "bind": "綁定"}, "loadFailed": "獲取安全資訊失敗，請稍後重試"}, "log": {"title": "訪問日誌", "time": "登錄時間", "user": "登錄用戶", "ip": "IP 地址", "status": "狀態", "browser": "瀏覽器", "platform": "平台", "success": "成功", "failed": "失敗", "fetchFailed": "獲取登錄歷史失敗，請稍後重試"}, "avatar": {"uploadSuccess": "頭像上傳成功", "uploadFailed": "頭像上傳失敗，請重試", "formatError": "上傳頭像圖片只能是 JPG/PNG/GIF/BMP/WebP 格式!", "sizeError": "上傳頭像圖片大小不能超過 10MB!"}, "enterpriseIdentity": {"title": "企業化賬號", "account": "賬號", "status": "狀態", "bindAccount": "綁定賬號", "operation": "操作", "bind": "綁定", "unbind": "解除綁定", "normal": "正常", "abnormal": "異常", "notBound": "未綁定", "syncNow": "正在同步 {name}", "viewDetails": "查看 {name} 的詳細資訊", "fetchFailed": "獲取企業賬號列表失敗"}}, "home": {"header": {"congratulations": "恭喜你完成了認證！", "description": "閱讀完本教程後，你可以開始到新應用中理解實踐認證流程的最佳實踐。", "userCenter": "個人中心", "logout": "退出登錄"}, "logout": {"title": "確認退出應用？", "message": "退出錄後，將返回註冊/登錄頁面", "cancel": "取消", "confirm": "確定"}, "cards": {"expand": "展開", "collapse": "收起", "token": {"title": "查看 Token 憑證", "description": "Token 是用戶認證憑據，用於訪問受保護資源的身份認證。", "details": "回的 token 中有 id_token 和 access_token，二者之間區別請參考<a href=\"#\">文檔</a>。如果你需要獲取 refresh_token 請參考<a href=\"#\">文檔</a>。"}, "userInfo": {"title": "查看用戶資訊", "description": "你可以通過 Token 獲取用戶資訊，用戶資訊符合 OIDC 標準。", "details": "使用 access_token 調用用戶資訊端點可以獲取用戶的詳細資訊。詳情請參考<a href=\"#\">文檔</a>。"}, "callback": {"title": "處理回調", "description": "如果你有後端服務，建議在後端處理回調。", "details": "在後端處理回調可以提高安全性，避免敏感資訊暴露在前端。詳細的回調處理流程請參考<a href=\"#\">文檔</a>。"}, "noBackend": {"title": "無後端場景", "description": "適用於無後端的場景，全部處理作業都在前端進行。", "details": "在無後端場景下，需要特別注意前端安全性。建議使用 PKCE 流程增強安全性。詳情請參考<a href=\"#\">文檔</a>。"}, "faq": {"title": "常見問題", "description": "你可以參考這些常見問題，或者使用高級功能。", "details": "我們整理了一些常見問題和解答，希望能幫助你快速解決問題。如果還有其他疑問，歡迎聯繫我們的支持團隊。"}, "next": {"title": "接下來你可能需要", "description": "閱讀完本教程後，你可以嘗試更多進階操作。", "details": "接下來你可以嘗試以下操作：<br>1. 集成到你的應用<br>2. 配置自定義登錄頁面<br>3. 設置多因素認證<br>4. 管理用戶權限"}}}, "to": "至", "startDate": "開始日期", "endDate": "結束日期", "search": "搜索", "account": "賬號", "status": "狀態", "normal": "正常", "abnormal": "異常", "bound": "已綁定", "unbound": "未綁定", "otp": {"bind_otp_title": "綁定 OTP 口令", "step_download": "下載驗證器", "step_scan": "掃描二維碼", "step_verify": "輸入安全碼", "step_unbind": "解綁 OTP", "scan_download_tip": "請使用手機掃碼下載驗證器", "scan_qrcode": "掃描二維碼", "scan_qrcode_tip": "請在手機打開 Google Authenticator / Microsoft Authenticator 掃碼新增安全碼", "enter_code": "輸入安全碼", "enter_code_tip": "在手機查看並輸入 6 位數字安全碼，完成後進入下一步", "unbind_title": "解綁 OTP", "unbind_tip": "請輸入您的 OTP 驗證碼以解綁當前設備", "confirm_unbind": "我確認要解綁 OTP", "unbind": "解綁 OTP", "bind_success": "OTP 綁定成功", "bind_failed": "OTP 驗證失敗，請重試", "unbind_success": "OTP 解綁成功", "unbind_failed": "OTP 解綁失敗，請重試", "enter_6_digits": "請輸入6位數字驗證碼", "bind_phone_email_first": "請先綁定手機號和郵箱後再嘗試綁定 OTP", "bind_phone_first": "請先綁定手機號後再嘗試綁定 OTP", "bind_email_first": "請先綁定郵箱後再嘗試綁定 OTP", "get_qrcode_failed": "獲取 OTP 二維碼失敗，請稍後重試", "scan_download": "下載"}, "actions": {"back": "返回", "next": "下一步", "previous": "上一步"}, "routers": {}, "routes": {"settings": "設定", "frontend": "前台設定", "backend": "後台設定", "identity": {"title": "身份源", "enterprise": "企業身份源", "enterpriseTmpList": "企業身份源模版列表", "enterpriseTmpListDetail": "企業身份源模版列表詳情", "enterpriseCreate": "創建企業身份源", "enterpriseDetail": "企業身份源詳情", "social1": "社會化身份源", "social": {"title": "社會化身份源", "emptyDescription": "對接微信、Git<PERSON>ab、支付寶等社交類登錄軟件，使你的應用支持使用該方式進行認證、授權登錄。", "createSource": "創建社會化身份源", "identitySource": "身份源", "sourceName": "身份源名稱", "sourceId": "身份源 ID", "confirmOperation": "操作確認", "confirmDelete": "確認刪除該社會化身份源嗎?", "deleteSuccess": "刪除成功", "deleteFailed": "刪除失敗，請重試", "fetchFailed": "獲取社會化身份源列表失敗"}, "socialTmpList": "社會化身份源模版列表", "socialTmpListDetail": "社會化身份源模版列表詳情", "socialCreate": "創建社會化身份源", "identitySource": "身份源", "sourceName": "身份源名稱", "sourceId": "身份源 ID", "confirmOperation": "操作確認", "confirmDelete": "確認刪除該企業身份源嗎?", "createSource": "創建企業身份源", "emptyDescription": "對接標準協議或飛書、企業微信、釘釘等企業軟件，使你的應用支持使用該方式進行認證、授權登錄。", "loginMode": "登錄模式", "scenario": "場景", "cas": {"appLogo": "應用 Logo", "logoTip": "如果設置，Authing 登錄表單將在 \"使用 {Display Name} 登錄\" 的按鈕上顯示此圖標，該圖標會展示為 20 * 20。", "uniqueId": "唯一標識", "inputUniqueId": "請輸入唯一標識", "displayName": "顯示名稱", "inputDisplayName": "Windows AD", "casAuthUrl": "CAS 認證 URL", "casAuthUrlPlaceholder": "請輸入外部 CAS 身份提供商的發起認證端點", "casTicketUrl": "CAS Ticket 檢驗 URL", "casTicketUrlPlaceholder": "請輸入外部 CAS 身份提供商 Ticket 驗證端點", "loginMode": "登錄模式", "loginAndRegister": "可用於登錄註冊", "loginOnly": "僅用於登錄", "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "uploadFailed": "上傳失敗"}, "ldap": {"basicConfig": "基礎配置", "appLogo": "應用 Logo", "logoTip": "如果設置，Authing 登錄表單將在 \"使用 {Display Name} 登錄\" 的按鈕上顯示此圖標，該圖標會展示為 20 * 20。", "uniqueId": "唯一標識", "inputUniqueId": "請輸入唯一標識", "displayName": "顯示名稱", "inputDisplayName": "Windows AD", "ldapUrl": "LDAP 鏈接", "ldapUrlPlaceholder": "請輸入 LDAP 服務器的地址與端口, 如： ldap://dc.fabrikam.com:389", "bindDNPlaceholder": "請輸入用於連接 LDAP 的用戶名, 此用戶名將用於測試連接結果和搜索用戶或用戶組", "bindDNPassword": "Bind DN 密碼", "bindDNPasswordPlaceholder": "請輸入用於連接 LDAP 的密碼, 該密碼將會被加密存儲到數據庫中", "usersDNPlaceholder": "定義從哪個目錄開始搜索, 如： dc=fabrikam,dc=local", "queryCriteria": "查詢條件", "queryCriteriaPlaceholder": "這是一個用來測試的 LDAP 服務。此測試 LDAP 配置的查詢條件用於查找用戶資訊以取得用戶 dn 資訊。", "queryCriteriaTip": "查詢條件，該條件結合 bindDN 以及對應的 secret 進行用戶查找，用於檢索用戶的 dn 資訊，結合用戶密碼進行 ldap 認證。支持自定義 filter 表達式，基本形式為：&(objectClass=organizationalPerson)(cn=%s), 其中 %s 會被用戶在登錄時填寫的用戶名替換。例如你想通過用戶的 cn 登錄，可以填寫 &(objectClass=organizationalPerson)(cn=%s)；如果你想通過用戶的 mail 登錄，可以填寫 &(objectClass=organizationalPerson)(mail=%s)。", "loginMode": "登錄模式", "loginAndRegister": "可用於登錄註冊", "loginOnly": "僅用於登錄", "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "connectionTest": "LDAP 連接測試", "test": "測試", "testAccount": "測試帳號", "testPassword": "測試密碼", "connectivityTest": "連通性測試", "accountPasswordTest": "帳號密碼測試", "notTested": "未進行", "uploadFailed": "上傳失敗"}, "oidc": {"appLogo": "應用 Logo", "logoTip": "如果設置，Authing 登錄表單將在 \"使用 {Display Name} 登錄\" 的按鈕上顯示此圖標，該圖標會展示為 20 * 20。", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "請輸入顯示名稱", "mode": "模式", "modeOptions": {"frontend": "前端模式", "backend": "後端模式"}, "modeTip": "前端模式會使用 response_mode=form_post 和 response_type=id_token 模式，後端模式會使用 response_type=code 授權碼模式", "issuerUrl": "Issuer URL", "issuerUrlPlaceholder": "id.mydomain.com/.well-known/openid-configuration", "issuerUrlTip": "你想要連接的 OpenID Connect provider 的 Issuer URL", "clientId": "Client ID", "clientIdPlaceholder": "請輸入想要連接的 OpenID Connect provider 的 Client ID", "clientSecret": "Client Secret", "clientSecretPlaceholder": "請輸入想要連接的 OpenID Connect provider 的 Client Secret", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。"}, "saml": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "appLogo": "應用 Logo", "signCert": "驗簽證書", "signCertPlaceholder": "請輸入外部 SAML 提供商的 SAML Response 驗簽證書", "loginUrl": "登錄 URL", "loginUrlPlaceholder": "請輸入外部 SAML 提供商的發起認證端點", "samlRequestSign": "SAML 請求簽名", "yes": "是", "no": "否", "samlRequestSignAlgo": "SAML 請求簽名算法", "samlRequestSummaryAlgo": "SAML 請求摘要算法", "samlRequestProtocolBind": "SAML 請求協議綁定", "singleLogout": "單點登出", "logoutUrl": "登出 URL", "logoutUrlBind": "登出 URL Binding", "acsUrl": "ACS URL", "metadataXml": "元數據 XML 文件", "fieldMappAttr": "字段映射屬性", "loginMode": "登錄模式", "loginModeOptions": {"loginAndRegister": "可用於登錄註冊", "loginOnly": "僅用於登錄"}, "loginOnlyTip": "開啟「僅登錄模式」後，只能登錄既有賬號，不能創建新賬號，請謹慎選擇。", "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "associationMethod": "關聯方式", "associationMethodOptions": {"fieldMatch": "字段匹配"}, "fieldType": "字段類型", "fieldTypeOptions": {"email": "郵箱", "phone": "手機號", "username": "用戶名", "externalId": "原系統 ID(externalId)"}, "copy": {"success": "複製成功", "failed": "複製失敗"}, "validation": {"uniqueIdRequired": "請輸入唯一標識", "displayNameRequired": "請輸入顯示名稱", "signRequired": "請輸入驗簽證書", "loginUrlRequired": "請輸入登錄 URL", "logoutUrlRequired": "請輸入登出 URL", "logoutUrlBindRequired": "請選擇登出 URL Binding"}}, "windows": {"appLogo": "應用 Logo", "logoTip": "如果設置，Authing 登錄表單將在 \"使用 {Display Name} 登錄\" 的按鈕上顯示此圖標，該圖標會展示為 20 * 20。", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "Windows AD", "domainLoginAddress": "域內登錄地址", "domainLoginAddressPlaceholder": "https://xxx.example.com", "terminalId": "終端識別編號", "generateKey": "自動生成密鑰", "autoLogin": "自動登錄", "autoLoginTip": "如果設置，在加入了配置 AD 域的電腦中打開 Authing 登錄頁，會直接登錄成功", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "uploadFailed": "上傳失敗"}, "windowsAd": {"appLogo": "應用 Logo", "logoTip": "如果設置，Authing 登錄表單將在 \"使用 {Display Name} 登錄\" 的按鈕上顯示此圖標，該圖標會展示為 20 * 20。", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "Windows AD", "syncToAd": "同步到 AD", "syncToAdTip": "如果設置，當 AD 認證成功時，會將用戶在 AD 域的密碼同步至其在 Authing 的密碼", "modifyPasswordSync": "修改密碼同步", "modifyPasswordSyncTip": "如果設置，當用戶在 Authing 的密碼被修改之後（包含管理員修改密碼和用戶自己手動重置密碼），會將用戶在 AD 中的密碼也同步修改。", "validateAdPolicy": "修改密碼同時校驗 AD 密碼策略", "validateAdPolicyTip": "如果設置，則當用戶在 Authing 修改密碼時會先校驗密碼是否符合 AD 密碼策略要求，不符合會提示用戶重新輸入密碼，符合則會將用戶在 AD 中的密碼也同步修改。", "fieldMapping": "用戶改密字段映射", "authingField": {"label": "Authing 字段", "placeholder": "請選擇"}, "windowsAdField": {"label": "Windows AD 字段", "placeholder": "請選擇"}, "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "uploadFailed": "上傳失敗"}, "amazon": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "clientId": "客戶端 ID", "clientIdPlaceholder": "請輸入客戶端 ID", "clientSecret": "客戶端密鑰", "clientSecretPlaceholder": "請輸入 Amazon 應用的客戶端密鑰", "callbackUrl": "回調地址", "callbackUrlTip": "你需要將此鏈接配置到對應身份源的回調地址中", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "amazonMobile": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "configFileId": "安全配置文件 ID", "configFileIdPlaceholder": "請輸入安全配置文件 ID", "apiKeyAndroid": "API 密鑰 (安卓)", "apiKeyAndroidPlaceholder": "請輸入安卓的 API 密鑰", "apiKeyIOS": "API 密鑰 (iOS)", "apiKeyIOSPlaceholder": "請輸入 iOS 的 API 密鑰", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。"}, "appleMobile": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "bundleId": "Bundle ID", "bundleIdPlaceholder": "請輸入 Apple App Bundle ID", "teamId": "Team ID", "teamIdPlaceholder": "請輸入 Apple Team ID", "keyId": "Key ID", "keyIdPlaceholder": "請輸入 Apple Key ID", "key": "Key", "keyPlaceholder": "請輸入 Apple Key", "callbackUrl": "回調地址", "callbackTip": "你需要將此鏈接配置到對應身份源的回調地址中", "scopes": "授權範圍", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "appleWeb": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "servicesIdentifier": "Services Identifier", "servicesIdentifierPlaceholder": "請輸入 Apple Services Identifier", "teamId": "Team ID", "teamIdPlaceholder": "請輸入 Apple Team ID", "keyId": "Key ID", "keyIdPlaceholder": "請輸入 Apple Key ID", "key": "Key", "keyPlaceholder": "請輸入 Apple Key", "callbackUrl": "回調地址", "callbackTip": "你需要將此鏈接配置到對應身份源的回調地址中", "scopes": "授權範圍", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "aws": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "authDomain": "認證域", "authDomainPlaceholder": "請輸入認證地址", "clientId": "客戶端 ID", "clientIdPlaceholder": "請輸入客戶端 ID", "clientSecret": "客戶端密鑰", "clientSecretPlaceholder": "請輸入 AWS 應用的客戶端密鑰", "callbackUrl": "回調地址", "callbackTip": "你需要將此鏈接配置到對應身份源的回調地址中", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "facebook": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "appId": "應用編號", "appIdPlaceholder": "請輸入應用編號", "appSecret": "應用密鑰", "appSecretPlaceholder": "請輸入應用密鑰", "callbackUrl": "回調地址", "callbackUrlPlaceholder": "請輸入你的業務回調鏈接", "scopes": "授權範圍", "callbackTip": "你需要將此鏈接配置到對應身份源的回調地址中", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "github": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "clientId": "Client ID", "clientIdPlaceholder": "請輸入 GitHub 提供的 Client ID", "clientSecret": "Client secrets", "clientSecretPlaceholder": "請輸入 GitHub 提供的 Client secrets", "callbackUrl": "回調地址", "callbackUrlPlaceholder": "請輸入你的業務回調鏈接", "scopes": "授權範圍", "callbackTip": "你需要將此鏈接配置到對應身份源的回調地址中", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "linkedin": {"basicConfig": "基礎配置", "uniqueId": {"label": "唯一標識", "placeholder": "請輸入唯一標識"}, "displayName": {"label": "顯示名稱", "placeholder": "該名稱將會展示在用戶端登錄界面的按鈕上"}, "clientId": {"label": "Client ID", "placeholder": "請輸入 Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "請輸入 Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "請輸入你的業務回調鏈接"}, "callback": {"label": "回調地址", "tip": "你需要將此鏈接配置到對應身份源的回調地址中"}, "loginMode": {"label": "登錄模式", "options": {"register": "可用於登錄註冊", "login": "僅用於登錄"}}, "accountBinding": {"label": "賬號綁定", "tip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。"}, "copy": {"success": "複製成功", "failed": "複製失敗"}}}, "userManager": {"title": "用戶管理", "list": {"title": "用戶列表", "detail": "用戶詳情", "emptyDescription": "當前用戶池的所有用戶，在這裡你可以對用戶進行統一管理。", "createUser": "創建用戶", "username": "用戶名", "phone": "手機號", "email": "郵箱", "lastLoginTime": "最後登錄時間", "createdAt": "創建時間", "filter": "篩選", "filterConditions": "篩選條件", "selectAttribute": "請選擇屬性", "selectRelation": "請選擇關係", "inputAttributeValue": "請輸入屬性值", "addRule": "添加規則", "confirmOperation": "操作確認", "confirmDelete": "確認刪除這些數據嗎?", "attributes": {"userId": "用戶ID", "name": "姓名", "username": "用戶名", "status": "賬號狀態", "workStatus": "工作狀態", "gender": "性別", "phone": "手機號", "email": "郵箱"}, "relations": {"equal": "等於", "notEqual": "不等於", "contains": "包含"}}, "group": {"title": "用戶組管理", "list": "用戶組管理 - 列表", "detail": "用戶組管理 - 詳情", "create": "用戶組管理 - 創建", "emptyDescription": "根據用戶的屬性是否符合當前設定的規則自動新增或移除成員，實現更靈活的用戶管控。", "createStaticGroup": "創建靜態用戶組", "createDynamicGroup": "創建動態用戶組", "name": "名稱", "uniqueId": "唯一標識", "groupType": "用戶組類型", "dynamicGroup": "動態用戶組", "staticGroup": "靜態用戶組", "totalUsers": "全部用戶數", "authRules": "授權規則數", "filter": "篩選", "searchPlaceholder": "搜索用戶組名稱、Code", "confirmOperation": "操作確認", "confirmDelete": "確認刪除這些數據嗎?", "back": "返回", "groupName": "用戶組名稱", "inputGroupName": "請輸入用戶組名稱", "groupCode": "用戶組標識符", "inputGroupCode": "請輸入用戶組標識符", "description": "描述", "inputDescription": "請輸入描述", "groupRules": "用戶組規則", "selectAttributeCategory": "請選擇屬性分類", "userBasicFields": "用戶基礎字段", "selectRelation": "請選擇關係", "selectAttributeValue": "請選擇屬性值", "selectAttribute": "請選擇屬性", "inputAttributeValue": "請輸入屬性值", "addRule": "新增規則", "groupNameRequired": "請輸入用戶組名稱", "groupCodeRequired": "請輸入用戶組標識符", "updateSuccess": "更新成功", "createSuccess": "創建成功", "updateFailed": "更新失敗", "createFailed": "創建失敗", "attributes": {"userId": "用戶ID", "name": "姓名", "username": "用戶名", "status": "賬號狀態", "workStatus": "工作狀態", "gender": "性別", "phone": "手機號", "email": "郵箱"}, "relations": {"equal": "等於", "notEqual": "不等於", "contains": "包含"}}, "organize": {"title": "組織機構", "memberEntry": "成員入職", "searchMemberDepartment": "搜索成員、部門", "create": "新建", "createOrganization": "新建組織", "setDepartmentManager": "設置部門負責人", "addSubDepartment": "新增子部門", "disableOrganization": "停用組織", "disableDepartment": "停用部門", "editOrganization": "編輯組織", "editDepartment": "編輯部門", "deleteOrganization": "刪除組織", "deleteDepartment": "刪除部門", "user": "用戶", "phone": "手機號", "email": "郵箱", "accountStatus": "賬號狀態", "disableAccount": "停用賬號", "processResignation": "辦理離職", "changeDepartment": "變更部門", "setMainDepartment": "設置主部門", "setAsManager": "設為負責人", "setPosition": "設置崗位", "confirmOperation": "操作確認", "confirmDelete": "確認刪除這些數據嗎?"}, "postManagement": {"title": "崗位管理", "create": "創建崗位", "edit": "編輯崗位", "positionName": "崗位名稱", "positionCode": "崗位代碼", "description": "描述", "departmentId": "部門ID", "createdAt": "創建時間", "updatedAt": "最新編輯時間", "confirmOperation": "操作確認", "confirmDelete": "確認刪除這些數據嗎?", "back": "返回", "inputPositionName": "請輸入崗位名稱", "inputPositionCode": "請輸入崗位代碼，例如：admin", "inputDescription": "請輸入崗位描述", "parentDepartment": "上級部門", "positionNameRequired": "請輸入崗位名稱", "positionCodeRequired": "請輸入崗位代碼", "organization": "組織機構"}, "form": {"resetPassword": "重設密碼", "more": "更多", "disableAccount": "停用賬號", "enableAccount": "啟用賬號", "deleteAccount": "刪除賬號", "forceLogout": "強制下線", "confirmOperation": "操作確認", "confirmDelete": "確認刪除這些數據嗎?", "tabs": {"userInfo": "用戶資訊", "userAffiliation": "用戶歸屬", "permissionManagement": "權限管理", "applicationAuthorization": "應用授權", "accessLog": "訪問日誌"}}, "userAffi": {"userRole": {"title": "用戶角色", "addRole": "添加角色", "columns": {"roleId": "角色 ID", "roleCode": "角色代碼", "roleName": "角色名稱", "description": "描述", "operations": "操作"}, "dialog": {"title": "添加角色", "label": "角色", "placeholder": "請選擇角色", "buttons": {"cancel": "取消", "confirm": "確認"}}, "delete": {"title": "刪除角色", "confirm": "確認刪除該角色嗎？"}}}, "activated": {"title": "確定啟用 \"{name}\" 的賬號嗎？", "description": "啟用賬號，賬號狀態將恢復正常，用戶可以重新登錄應用。", "buttons": {"cancel": "取消", "confirm": "確定"}}, "create": {"title": "創建用戶", "accountType": {"username": "用戶名", "phone": "手機號", "email": "郵箱"}, "form": {"username": {"label": "用戶名", "placeholder": "請輸入用戶名", "required": "請輸入用戶名"}, "phone": {"label": "手機號", "placeholder": "請輸入手機號", "required": "請輸入手機號"}, "email": {"label": "郵箱", "placeholder": "請輸入郵箱", "required": "請輸入郵箱"}, "password": {"label": "密碼", "placeholder": "請輸入密碼", "required": "請輸入密碼", "generate": "自動生成密碼"}, "confirmPassword": {"label": "確認密碼", "placeholder": "請再次輸入密碼", "required": "請再次輸入密碼", "notMatch": "兩次輸入的密碼不一致"}, "loginUrl": {"label": "發送首次登錄地址"}, "selectPool": {"label": "選擇用戶池或應用", "placeholder": "Select", "preview": "預覽"}, "resetPassword": {"label": "強制用戶在首次登錄時修改密碼"}}, "buttons": {"cancel": "取消", "confirm": "確認"}}, "disable": {"title": "確定禁用 \"{name}\" 的賬號嗎？", "description": {"line1": "禁用的賬號將無法登錄應用以及重置密碼", "line2": "賬號禁用期間，仍可編輯用戶資訊", "line3": "禁用賬號可以恢復"}, "buttons": {"cancel": "取消", "confirm": "確定"}}, "resetPwd": {"title": "重設密碼", "form": {"newPassword": {"label": "新密碼", "placeholder": "請輸入新密碼", "required": "請輸入新密碼", "generate": "自動生成密碼"}, "sendPassword": {"label": "發送重置後的密碼給用戶"}, "email": {"label": "郵箱", "placeholder": "請輸入郵箱"}, "phone": {"label": "手機號", "placeholder": "請輸入手機號"}, "application": {"label": "選擇用戶登錄的應用", "placeholder": "請選擇"}, "forceChange": {"label": "強制用戶登錄後修改密碼"}}, "buttons": {"cancel": "取消", "confirm": "確定"}}, "rights": {"title": "數據資源策略", "search": {"placeholder": "搜索數據策略名稱、主體、數據資源"}, "authorize": "授權", "columns": {"selection": "選擇", "policyName": "數據策略名稱", "policyDescription": "數據策略描述", "authorizedSubject": "被授權主體", "relatedResource": "關聯數據資源", "lastEditTime": "最新編輯時間"}}}, "permission": {"title": "權限管理", "role": {"title": "角色管理", "create": "創建角色", "edit": "編輯角色", "roleName": "角色名稱", "roleCode": "角色代碼", "description": "描述", "status": "狀態", "enabled": "啟用", "disabled": "停用", "createdAt": "創建時間", "filter": "篩選", "searchPlaceholder": "搜索角色名稱、Code", "confirmOperation": "操作確認", "confirmDelete": "確認刪除這些數據嗎?", "createSuccess": "創建成功", "updateSuccess": "更新成功", "deleteSuccess": "刪除成功", "operationFailed": "操作失敗，請重試", "back": "返回", "reset": "重設", "save": "保存", "basicInfo": "基本信息", "inputRoleName": "請輸入角色名稱", "inputRoleCode": "請輸入角色 code，例如：admin", "disableTime": "角色自動禁用時間", "selectDateTime": "選擇日期時間", "inputDescription": "請輸入角色描述", "permissions": "權限", "selectAll": "全選", "deselectAll": "取消全選", "submit": "提交", "completePermissions": "請完善角色權限", "goToPermissions": "前往", "roleNameRequired": "請輸入角色名稱", "roleCodeRequired": "請輸入角色 Code"}}, "branding": {"title": "品牌化", "message": "消息設置", "emailTemplates": "郵件模板", "thirdPartyEmailService": {"title": "第三方郵件服務", "enableThirdParty": "開啟第三方服務", "custom": "自定義", "tencentEnterprise": "騰訊企業郵箱", "aliyunEnterprise": "阿里郵箱企業版", "sendgrid": "SendGrid", "smtpAddress": "SMTP 地址", "port": "端口", "username": "用戶名", "password": "密碼", "senderEmail": "發件者郵箱", "securityVerification": "安全驗證", "none": "無", "save": "保存", "saveAndTest": "保存並發送測試郵件", "inputSmtpAddress": "請輸入 SMTP 地址", "inputPort": "請輸入端口", "inputUsername": "請輸入用戶名", "inputPassword": "請輸入密碼", "inputSenderEmail": "請輸入發件者郵箱", "inputApiKey": "請輸入 API Key"}, "smsVerificationService": "短信驗證服務", "editAndPreview": "編輯並預覽", "templates": {"notification": "通知模版", "verificationCode": "註冊/登錄驗證碼模版", "pipeline": "Pipeline 相關模板", "verification": "驗證模版", "password": "密碼相關模板", "emailBinding": "郵箱綁定模版", "identityAlert": "身份自動化告警模板"}, "emailItems": {"welcome": "歡迎郵件", "userCreation": "首次創建用戶通知", "invitation": "邀請郵件", "registerCode": "註冊驗證碼", "loginCode": "登錄驗證碼", "mfaLoginCode": "MFA 登錄驗證碼", "infoCompletionCode": "信息補全驗證碼", "pipelineError": "Pipeline 執行報錯", "firstEmailLogin": "首次郵箱登錄驗證", "consoleVerification": "控制台發起驗證", "passwordExpiration": "密碼到期提醒", "adminResetPassword": "管理員重置密碼提醒", "passwordChangeNotice": "賬戶密碼修改提醒", "selfUnlockCode": "自助解鎖驗證碼", "resetPasswordCode": "重置密碼驗證碼", "emailUnbindCode": "郵箱解綁驗證碼", "emailBindCode": "郵箱綁定驗證碼", "workflowFailure": "工作流執行失敗", "workflowTimeout": "工作流運行超時", "createDataFailure": "創建數據失敗", "updateDataFailure": "更新數據失敗", "deleteDataFailure": "刪除數據失敗"}}, "security": {"title": "安全設定", "mfa1": "多因素認證", "mfa": {"simpleMode": "簡單模式", "advancedMode": "高級模式", "authFactors": "身份認證因素", "sms": {"title": "短信驗證碼", "description": "使用短信形式接收驗證碼認證登錄"}, "email": {"title": "電子郵箱驗證", "description": "使用郵件形式接收驗證碼認證登錄"}, "otp": {"title": "OTP 口令", "description": "使用 OTP 一次性口令密碼認證登錄"}, "enableSuccess": "開啟成功", "enableFailed": "開啟失敗，請重試", "disableSuccess": "關閉成功", "disableFailed": "關閉失敗，請重試"}}, "userInfo": {"accountInfo": {"title": "賬號資訊", "createdTime": "創建時間", "lastLoginTime": "最後登錄時間", "lastLoginIp": "最後登錄 IP", "loginCount": "登錄次數", "userSource": "用戶來源", "userType": "用戶類型", "registerAlias": "註冊別名", "registerDevice": "註冊設備", "passwordExpireTime": "密碼過期時間"}, "thirdParty": {"title": "已綁定第三方賬號資訊", "empty": "未綁定任何第三方賬號，您可以綁定第三方賬號進行快捷登錄"}, "loginHistory": {"title": "歷史登錄應用"}, "personalInfo": {"title": "個人資訊", "form": {"name": {"label": "姓名", "placeholder": "請輸入姓名"}, "username": {"label": "用戶名", "placeholder": "請輸入用戶名"}, "email": {"label": "郵箱", "placeholder": "請輸入郵箱", "sendVerify": "發送驗證"}, "gender": {"label": "性別", "placeholder": "請選擇", "options": {"male": "男", "female": "女", "unknown": "未知"}}, "birthdate": {"label": "生日", "placeholder": "請輸入生日"}, "phone": {"label": "手機號", "placeholder": "請輸入手機號"}, "country": {"label": "國家", "placeholder": "請輸入國家代碼"}, "city": {"label": "城市", "placeholder": "請輸入城市"}, "company": {"label": "公司", "placeholder": "請輸入公司名稱"}, "province": {"label": "省份", "placeholder": "請輸入省/區"}, "address": {"label": "街道地址", "placeholder": "請輸入街道地址"}, "externalId": {"label": "原系統ID", "placeholder": "請輸入原系統ID (externalId)"}, "postalCode": {"label": "郵政編碼", "placeholder": "請輸入郵政編碼"}}, "buttons": {"save": "保存", "reset": "重設"}}, "jsonInfo": {"title": "個人資訊", "copy": "複製", "copySuccess": "複製成功", "copyFailed": "複製失敗"}}}, "frontend": {"back": "返回", "tabs": {"application": "應用配置", "protocol": "協議配置", "loginControl": "登錄控制", "accessAuth": "訪問授權"}}, "application": {"basicInfo": {"title": "基本資訊", "appName": "應用名稱", "appDescription": "應用描述", "appLogo": "應用 logo", "visibility": "可見範圍", "web": "Web 端", "mobile": "移動端"}, "endpointInfo": {"title": "端點資訊", "appId": "App ID", "jwksEndpoint": "JWKS 公鑰端點", "appSecret": "App Secret", "authEndpoint": "認證端點", "tokenEndpoint": "Token 端點", "issuer": "Issuer", "userInfoEndpoint": "用戶資訊端點", "discoveryEndpoint": "服務發現地址", "logoutEndpoint": "登出端點"}, "authConfig": {"title": "認證配置", "authAddress": "認證地址", "loginCallbackUrl": "登錄回調 URL", "logoutCallbackUrl": "登出回調 URL", "initiateLoginUrl": "發起登錄 URL", "multipleUrlTip": "多個 URL 用英文逗號「,」隔開"}, "buttons": {"save": "保存", "reset": "重設"}, "validation": {"appNameRequired": "請輸入「應用名稱」", "authAddressRequired": "請輸入「認證地址」", "loginCallbackRequired": "請輸入「登錄回調 URL」"}, "messages": {"copySuccess": "複製成功", "copyFailed": "複製失敗"}}, "loginCtrl": {"title": "登錄註冊控制", "defaultSettings": "預設設定", "registrationSettings": "註冊設定", "loginWay": {"mergedTitle": "登錄註冊合併", "mergedDesc": "單一頁面，無縫的註冊和登錄體驗", "separateTitle": "登錄註冊分頁", "separateDesc": "獨立兩頁面，先註冊再登錄"}, "table": {"sort": "排序", "accountField": "賬號字段", "authMethod": "認證方式", "useForLogin": "用於登錄", "useForRegister": "用於註冊"}, "authMethod": {"password": "密碼", "verifyCode": "驗證碼"}, "placeholder": {"selectLoginField": "請選擇用於登錄的字段", "selectDefaultLoginMethod": "請選擇預設登錄方式", "selectDefaultRegisterMethod": "請選擇預設註冊方式"}, "buttons": {"addAccountField": "新增賬號字段", "save": "保存", "reset": "重設"}, "defaultLoginMethod": {"title": "預設登錄方式", "description": "用戶登錄時預設看到的登錄方式"}, "defaultRegisterMethod": {"title": "預設註冊方式", "description": "用戶註冊時預設看到的註冊方式"}, "socialLogin": {"title": "社交登錄方式", "addIdentitySource": "新增身份源"}, "relatedApps": {"title": "關聯客戶端應用", "noApps": "暫無客戶端應用可關聯，請先創建客戶端應用"}, "defaultLoginType": {"title": "預設登錄方式", "description": "用戶登錄時預設看到的登錄方式", "normal": "常規登錄", "qrCode": "掃碼登錄"}, "onlineUsers": {"title": "在線用戶", "columns": {"userInfo": "用戶資訊", "phone": "手機號", "email": "郵箱", "loginCount": "登錄次數", "lastLoginTime": "最後登錄時間"}}, "autoRegisterLogin": {"title": "自動註冊並登錄", "description": "當用戶使用未註冊的賬號登錄時，系統會自動為其註冊賬號並登錄"}, "mergeLoginRegister": {"title": "登錄註冊合併", "description": "開啟後，登錄界面將包含註冊選項，用戶可以直接在登錄界面完成註冊"}, "loginMethodsSort": {"title": "登錄方式排序", "description": "拖動調整登錄方式的顯示順序", "columns": {"sort": "排序", "loginMethod": "登錄方式", "operations": "操作"}, "buttons": {"moveUp": "上移", "moveDown": "下移"}}, "passwordLoginConfig": {"title": "密碼登錄配置", "description": "配置可用於密碼登錄的方式", "options": {"phonePassword": "手機號密碼", "emailPassword": "郵箱密碼", "usernamePassword": "用戶名密碼"}}, "verifyCodeLoginConfig": {"title": "驗證碼登錄配置", "description": "配置可用於驗證碼登錄的方式", "options": {"phoneCode": "手機號驗證碼", "emailCode": "郵箱驗證碼"}}, "loginOptions": {"phoneCode": "手機號驗證碼", "password": "密碼"}, "registerOptions": {"phone": "手機號", "emailCode": "郵箱驗證碼", "phonePassword": "手機號密碼", "usernamePassword": "用戶名密碼"}, "methodLabels": {"phoneCode": "手機號驗證碼", "emailCode": "郵箱驗證碼", "phonePassword": "手機號密碼", "emailPassword": "郵箱密碼", "usernamePassword": "用戶名密碼"}, "fieldOptions": {"identityNumber": "居民身份證號-identityNumber", "externalId": "原系統ID-externalId"}, "messages": {"saveSuccess": "保存成功", "saveFailed": "保存失敗", "saveError": "保存失敗，請稍後重試"}}, "protocol": {"title": "協議配置", "selectProtocol": "選擇協議", "save": "保存", "reset": "重設", "protocolTypes": {"OIDC": "OpenID Connect", "OAUTH": "OAuth 2.0", "SAML": "SAML 2.0", "CAS": "CAS"}, "enableProtocol": "啟用協議", "tabs": {"basic": "授权配置", "jwt": "JWT 設置", "claim": "Claim 配置", "scope": "Scope 配置", "endpoint": "端點設置", "advanced": "高級設置"}, "oidc": {"title": "OIDC 配置", "tokenExpiry": "令牌過期時間", "authorizationCode": "授權碼", "idToken": "ID 令牌", "accessToken": "訪問令牌", "refreshToken": "刷新令牌", "authorizationModes": "授權模式", "exchangeVerificationMethod": "換取 token 身份驗證方式", "inspectionVerificationMethod": "檢驗 token 身份驗證方式", "withdrawalVerificationMethod": "撤回 token 身份驗證方式", "authorizationCodeTime": "授權碼過期時間", "IdTokenTime": "id_token 過期時間", "AccessTokenTime": "access_token 過期時間", "RefreshTokenTime": "refresh_token 過期時間", "claimMapping": "聲明映射", "addMappingField": "新增映射字段", "selectClaims": "選擇聲明", "createCustomScope": "創建自定義作用域", "configureScope": "配置作用域", "options": {"enableRefreshTokenRotation": "啟用 refresh_token 輪換", "refreshTokenLifetime": "輪換時刷新 refresh_token 的生命週期", "noForceHttpsForImplicit": "不強制隱式模式回調 URL 使用 HTTPS", "enableIdTokenEncryption": "啟用 id_token 加密", "userConsentPage": "用戶知情同意頁面", "idTokenPayloadEncryption": "ID Token Payload 的對稱加密算法", "idTokenKeyEncryption": "ID Token Payload 加密密鑰的非對稱加密算法", "asymmetricPublicKey": "非對稱加密使用的公鑰"}, "units": {"seconds": "秒", "minutes": "分鐘", "hours": "小時", "days": "天"}, "grantTypes": {"title": "授權類型", "authorizationCode": "授權碼", "implicit": "隱式授權", "password": "密碼模式", "clientCredentials": "客戶端憑證", "refreshToken": "刷新令牌"}, "responseTypes": {"title": "響應類型", "code": "授權碼", "token": "令牌", "idToken": "ID 令牌"}, "pkce": {"title": "PKCE 設置", "required": "要求 PKCE", "optional": "可選 PKCE", "disabled": "禁用 PKCE"}, "encryption": {"algorithm": {"a128cbcHs256": "A128CBC-HS256", "a128gcm": "A128GCM", "a256cbcHs512": "A256CBC-HS512", "a256gcm": "A256GCM"}, "method": {"rsaOaep": "RSA-OAEP", "ecdhEs": "ECDH-ES"}}}, "saml": {"title": "SAML 配置", "entityId": "實體 ID", "acsConfig": {"title": "ACS 地址配置", "responseMode": "響應模式", "address": "地址", "addACS": "新增 ACS 地址", "modes": {"httpPost": "HTTP-POST", "httpRedirect": "HTTP-REDIRECT"}}, "customAttributes": {"title": "自定義屬性", "label": "標籤", "way": "方式", "value": "值", "addAttribute": "新增屬性", "ways": {"basic": "基礎", "advanced": "高級"}}, "cert": {"title": "證書配置", "upload": "上傳證書", "generate": "生成證書"}, "signatureAlgorithm": "簽名算法", "algorithms": {"sha1": "SHA-1", "sha256": "SHA-256", "sha512": "SHA-512"}}, "claim": {"title": "聲明配置", "add": "新增聲明", "name": "名稱", "value": "值", "tips": {"duplicateName": "名稱已存在", "required": "必填項"}}, "scope": {"title": "作用域配置", "add": "新增作用域", "name": "名稱", "description": "描述", "claims": "包含的聲明", "tips": {"duplicateName": "名稱已存在", "required": "必填項"}}, "jwt": {"title": "JWT 配置", "algorithm": "簽名算法", "keyPair": "密鑰對", "generate": "生成新密鑰", "algorithms": {"rs256": "RS256", "rs384": "RS384", "rs512": "RS512", "hs256": "HS256", "hs384": "HS384", "hs512": "HS512"}}, "buttons": {"save": "保存", "reset": "重設", "add": "新增", "delete": "刪除"}, "messages": {"saveSuccess": "保存成功", "saveFailed": "保存失敗", "confirmDelete": "確認刪除此項？", "generateSuccess": "生成成功", "generateFailed": "生成失敗"}, "alert": {"oidcInfo": "當你使用 Authing 的身份認證功能時，預設使用 OIDC（OpenID Connect）協議進行認證。", "whichAuthMode": "不清楚應該選用哪種授權模式？", "oauthDeprecated": "【重要提示】不再推薦使用 OAuth2.0，建議切換到 OIDC，具體請查看文檔：", "implementSSO": "實現單點登錄", "samlConfig": "配置你的應用程式為 SAML 身份來源。如有任何疑問，請", "clickForHelp": "點擊這裡查看幫助文檔", "casConfig": "配置你的應用程式為 CAS 身份來源。詳細資訊，請", "viewDocs": "查看文檔"}, "oauth": {"enableOAuth2": "啟用 OAuth 2.0", "selectClaims": "選擇聲明", "createCustomScope": "創建自定義作用域", "scopeExists": "作用域已存在", "configureScope": "配置作用域", "selectUserField": "選擇用戶字段", "grantTypes": "授權類型", "tokenAuthentication": "令牌認證方式", "tokenEndpointAuthMethod": "令牌端點認證方式", "clientAuthentication": "客戶端認證方式", "clientSecret": "客戶端密鑰", "clientId": "客戶端 ID", "withdrawTokenAuthentication": "撤回令牌認證方式", "withdrawTokenEndpointAuthMethod": "撤回令牌端點認證方式", "withdrawClientAuthentication": "撤回客戶端認證方式", "withdrawClientSecret": "撤回客戶端密鑰", "withdrawClientId": "撤回客戶端 ID"}, "cas": {"casVersion": "CAS 版本", "enableCAS": "啟用 CAS", "loginEndpoint": "登錄端點", "logoutEndpoint": "登出端點", "serviceValidateEndpoint": "服務驗證端點", "serviceValidateEndpoint2": "服務驗證端點 2", "serviceTicketExpiry": "服務票證過期時間", "userId": "用戶 ID 字段", "customResponseFields": "自定義響應字段", "addField": "新增字段", "serviceValidateUri": "服務驗證 URI", "logoutUri": "登出 URI", "serverPrefix": "伺服器前綴", "ticketValidateVersion": "票證驗證版本", "ticketValidatePath": "票證驗證路徑", "ticketExpiry": "票證過期時間", "tgtExpiry": "TGT 過期時間", "enterFieldName": "請輸入字段名稱", "enterFieldValue": "請輸入字段值", "userIdPlaceholder": "請輸入用戶 ID"}}, "azureAd": {"basicConfig": "基礎配置", "appLogo": "應用 Logo", "logoTip": "如果設置，Authing 登錄表單將在 使用 {Display Name} 登錄 的按鈕上顯示此圖標，該圖標會展示為 20 * 20。", "uniqueId": "唯一標識", "inputUniqueId": "請輸入唯一標識", "displayName": "顯示名稱", "domainName": "身份源域名", "tenantIdPlaceholder": "請輸入 Azure Active Directory 應用所在的 Tenant ID，不填寫預設選擇認證賬戶類型為 organizations", "clientIdPlaceholder": "請輸入 Azure Active Directory 應用的 Client ID", "clientSecretPlaceholder": "請輸入 Azure Active Directory 應用的 Client Secret", "emailVerificationSync": "郵箱驗證同步策略", "emailVerificationFalse": "始終設置 emailVerified 為 false", "emailVerificationTrue": "始終設置 emailVerified 為 true", "userInteractionType": "用戶交互類型", "interactionNoPrompt": "NoPrompt - 無 prompt 參數選項", "interactionLogin": "login - 強制用戶在該請求上輸入憑據，取消單一登錄", "interactionNone": "none - 與 login 相反，確保不向用戶顯示任何交互式提示。如果請求無法通過單一登錄以無提示方式完成，則 Microsoft 標識平台將返回 interaction_required 錯誤", "interactionConsent": "consent - 用戶登錄之後，要求用戶向應用授予權限", "interactionSelectAccount": "select_account - 顯示在會話中記住的所有帳戶", "interactionTip": "提示身份提供商在用戶進行身份驗證和授權過程時需要採取的操作。", "callbackUrl": "回調地址", "callbackTip": "你需要將此鏈接設置為 Azure AD 應用的回調鏈接，請查看 Azure AD 的文檔說明。", "loginMode": "登錄模式", "loginAndRegister": "可用於登錄註冊", "loginOnly": "僅用於登錄", "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "uploadFailed": "上傳失敗"}, "enabled": "啟用", "disabled": "禁用", "operations": "操作", "cancel": "取消", "confirm": "確認", "create": "創建", "department": {"createDepartment": "新建部門", "editDepartment": "編輯部門", "departmentName": "部門名稱", "inputDepartmentName": "請輸入部門名稱", "departmentCode": "部門Code", "inputDepartmentCode": "請輸入部門Code", "codeTooltip": "部門的唯一標識，同一級別下唯一，可用於數據交換。", "departmentDescription": "部門描述", "inputDepartmentDescription": "請輸入部門描述", "associatedPositions": "關聯崗位", "selectAssociatedPositions": "請選擇關聯崗位", "parentDepartment": "上級部門", "pleaseEnterDepartmentName": "請輸入部門名稱", "pleaseSelectParentDepartment": "請選擇上級部門", "parentDepartmentCannotBeEmpty": "上級部門不能為空", "organizationalStructure": "組織機構", "setDepartmentManager": "設置部門負責人", "searchOrSelectMembers": "搜索或選擇下方成員", "selectedMembers": "已選：{count} 名成員"}, "common": {"cancel": "取消", "confirm": "確定", "selectAll": "全選", "clear": "清空"}, "member": {"memberEntry": "成員入職", "name": "姓名", "inputName": "請輸入姓名", "phoneNumber": "手機號", "inputPhoneNumber": "請輸入手機號", "email": "郵箱", "inputEmail": "請輸入郵箱", "password": "密碼", "inputPassword": "請輸入密碼", "generatePassword": "自動生成密碼", "position": "所屬崗位", "selectPosition": "請選擇關聯崗位", "forceResetPasswordOnFirstLogin": "強制用戶在首次登錄時修改密碼", "nameRequired": "請輸入姓名", "nameLengthLimit": "姓名長度應在 2 到 20 個字符之間", "phoneRequired": "請輸入手機號", "emailRequired": "請輸入郵箱", "passwordRequired": "請輸入密碼", "passwordLengthLimit": "密碼長度不能少於 8 個字符", "positionRequired": "請選擇所屬崗位", "departmentRequired": "請至少選擇一個部門"}, "organization": {"createOrganization": "新建組織", "editOrganization": "編輯組織", "name": "組織名稱", "inputName": "請輸入組織名稱", "code": "組織Code", "inputCode": "請輸入組織Code", "codeTooltip": "組織的唯一標識，同一級別下唯一，可用於數據交換。", "description": "組織描述", "inputDescription": "請輸入組織描述", "relatedPositions": "關聯崗位", "selectRelatedPositions": "請選擇關聯崗位", "nameRequired": "請輸入組織名稱", "codeRequired": "請輸入組織Code"}, "identity": {"oauth2": {"appLogo": "應用 Logo", "logoTip": "如果設置，Authing 登錄表單將在 使用 {Display Name} 登錄 的按鈕上顯示此圖標，該圖標會展示為 20 * 20。", "uniqueId": "唯一標識", "inputUniqueId": "請輸入唯一標識", "displayName": "顯示名稱", "inputDisplayName": "請輸入顯示名稱", "authorizeUrl": "授權 URL", "inputAuthorizeUrl": "請輸入授權 URL", "tokenUrl": "Token URL", "inputTokenUrl": "請輸入 Token URL", "authorizeScope": "授權範圍", "inputAuthorizeScope": "請輸入授權範圍", "scopeTip": "請使用空格分隔不同的授權域", "clientId": "客戶端 ID", "inputClientId": "請輸入客戶端 ID", "clientSecret": "客戶端密鑰", "inputClientSecret": "請輸入客戶端密鑰", "clientSecretTip": "請輸入客戶端密鑰", "codeToTokenScript": "代碼轉換為 Token 腳本", "tokenToUserInfoScript": "Token 轉換為用戶資訊腳本", "loginMode": "登錄模式", "loginAndRegister": "可用於登錄註冊", "loginOnly": "僅用於登錄", "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "uploadFailed": "上傳失敗"}, "facebook": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "appId": "應用編號", "appIdPlaceholder": "請輸入應用編號", "appSecret": "應用密鑰", "appSecretPlaceholder": "請輸入應用密鑰", "callbackUrl": "回調地址", "callbackUrlPlaceholder": "請輸入你的業務回調鏈接", "scopes": "授權範圍", "callbackTip": "你需要將此鏈接配置到對應身份源的回調地址中", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "facebookMobile": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "appId": "應用編號", "appIdPlaceholder": "請輸入應用編號", "appSecret": "應用密鑰", "appSecretPlaceholder": "請輸入應用密鑰", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "github": {"basicConfig": "基礎配置", "uniqueId": "唯一標識", "uniqueIdPlaceholder": "請輸入唯一標識", "displayName": "顯示名稱", "displayNamePlaceholder": "該名稱將會展示在用戶端登錄界面的按鈕上", "clientId": "Client ID", "clientIdPlaceholder": "請輸入 GitHub 提供的 Client ID", "clientSecret": "Client secrets", "clientSecretPlaceholder": "請輸入 GitHub 提供的 Client secrets", "callbackUrl": "回調地址", "callbackUrlPlaceholder": "請輸入你的業務回調鏈接", "scopes": "授權範圍", "callbackTip": "你需要將此鏈接配置到對應身份源的回調地址中", "loginMode": "登錄模式", "loginModeOptions": {"register": "可用於登錄註冊", "login": "僅用於登錄"}, "accountBinding": "賬號綁定", "accountBindingTip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。", "copy": {"success": "複製成功", "failed": "複製失敗"}}, "githubMobile": {"basicConfig": "基礎配置", "uniqueId": {"label": "唯一標識", "placeholder": "請輸入唯一標識"}, "displayName": {"label": "顯示名稱", "placeholder": "該名稱將會展示在用戶端登錄界面的按鈕上"}, "clientId": {"label": "Client ID", "placeholder": "請輸入 GitHub 提供的 Client ID"}, "clientSecret": {"label": "Client secrets", "placeholder": "請輸入 GitHub 提供的 Client secrets"}, "callback": {"label": "回調地址", "tip": "你需要將此鏈接配置到對應身份源的回調地址中"}, "loginMode": {"label": "登錄模式", "options": {"register": "可用於登錄註冊", "login": "僅用於登錄"}}, "accountBinding": {"label": "賬號綁定", "tip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。"}, "copy": {"success": "複製成功", "failed": "複製失敗"}}, "googleMobile": {"basicConfig": "基礎配置", "uniqueId": {"label": "唯一標識", "placeholder": "請輸入唯一標識"}, "displayName": {"label": "顯示名稱", "placeholder": "該名稱將會展示在用戶端登錄界面的按鈕上"}, "clientId": {"label": "Client ID", "placeholder": "請輸入 Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "請輸入 Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "請輸入你的業務回調鏈接"}, "callback": {"label": "回調地址", "tip": "你需要將此鏈接配置到對應身份源的回調地址中"}, "loginMode": {"label": "登錄模式", "options": {"register": "可用於登錄註冊", "login": "僅用於登錄"}}, "accountBinding": {"label": "賬號綁定", "tip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。"}, "copy": {"success": "複製成功", "failed": "複製失敗"}}, "googleWeb": {"basicConfig": "基礎配置", "uniqueId": {"label": "唯一標識", "placeholder": "請輸入唯一標識"}, "displayName": {"label": "顯示名稱", "placeholder": "該名稱將會展示在用戶端登錄界面的按鈕上"}, "clientId": {"label": "Client ID", "placeholder": "請輸入 Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "請輸入 Client Secret"}, "scopes": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "授權 Google 2.0 範圍時需要的 scope， 以英文逗號分隔，預設為 email, profile, openid"}, "domainVerification": {"fileName": {"label": "Domain Verification File Name", "placeholder": "請輸入 Google 域名驗證 HTML 文件名，如 xxxx.html"}, "fileContent": {"label": "Domain Verification File Content", "placeholder": "請輸入 Google 域名驗證 HTML 文件內容"}}, "accessType": {"label": "是否獲取刷新令牌 access_type", "tip": "離線配置對象配置訪問令牌，客戶端對象就會根據需要刷新訪問令牌", "options": {"online": "Online", "offline": "Offline"}}, "prompt": {"label": "登錄授權提示 prompt", "tip": "再次向用戶顯示授權提示", "options": {"firstTime": "僅首次展示", "none": "none", "consent": "consent", "selectAccount": "select_account"}}, "incrementalAuth": {"label": "增量授權", "tip": "增量授權，在您需要時請求資源授權。", "options": {"true": "True", "false": "False"}}, "callbackUrl": {"label": "Callback URL", "placeholder": "請輸入你的業務回調鏈接"}, "callback": {"label": "回調地址", "tip": "你需要將此鏈接配置到對應身份源的回調地址中"}, "loginMode": {"label": "登錄模式", "options": {"register": "可用於登錄註冊", "login": "僅用於登錄"}}, "accountBinding": {"label": "賬號綁定", "tip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。"}, "associationMethod": {"label": "關聯方式", "options": {"fieldMatch": "字段匹配"}}, "fieldType": {"label": "字段類型", "options": {"email": "郵箱"}}, "copy": {"success": "複製成功", "failed": "複製失敗"}}, "linkedin": {"basicConfig": "基礎配置", "uniqueId": {"label": "唯一標識", "placeholder": "請輸入唯一標識"}, "displayName": {"label": "顯示名稱", "placeholder": "該名稱將會展示在用戶端登錄界面的按鈕上"}, "clientId": {"label": "Client ID", "placeholder": "請輸入 Client ID"}, "clientSecret": {"label": "Client Secret", "placeholder": "請輸入 Client Secret"}, "callbackUrl": {"label": "Callback URL", "placeholder": "請輸入你的業務回調鏈接"}, "callback": {"label": "回調地址", "tip": "你需要將此鏈接配置到對應身份源的回調地址中"}, "loginMode": {"label": "登錄模式", "options": {"register": "可用於登錄註冊", "login": "僅用於登錄"}}, "accountBinding": {"label": "賬號綁定", "tip": "對於用戶池中已存在的賬號，用戶可以將這種社交登錄方式綁定至已有賬號。"}, "copy": {"success": "複製成功", "failed": "複製失敗"}}}, "permission": {"role": {"role": {"back": "返回", "edit": "編輯角色", "basicInfo": "基本資訊", "roleName": "角色名稱", "roleNamePlaceholder": "請輸入角色名稱", "roleCode": "角色 Code", "roleCodePlaceholder": "請輸入角色 code，例如：admin", "description": "角色描述", "descriptionPlaceholder": "請輸入角色描述", "expirationDate": "角色自動禁用時間", "selectPermissions": "選擇權限", "cancel": "取消", "permissionGroup": {"selectAll": "全選", "deselectAll": "取消全選"}, "update": "更新", "create": "創建", "validation": {"roleNameRequired": "請輸入角色名稱", "roleCodeRequired": "請輸入角色 Code"}}, "form": {"title": {"create": "創建角色", "edit": "編輯角色"}, "roleName": "角色名稱", "roleNamePlaceholder": "請輸入角色名稱", "roleCode": "角色 Code", "roleCodePlaceholder": "請輸入角色 code，例如：admin", "permissionSpace": "權限空間", "permissionSpacePlaceholder": "請選擇權限空間", "permissionSpace1": "權限空間1", "permissionSpace2": "權限空間2", "description": "角色描述", "descriptionPlaceholder": "請輸入角色描述", "expirationDate": "角色自動禁用時間", "buttons": {"cancel": "取消", "confirm": "確定"}}, "list": {"searchPlaceholder": "搜索角色名稱、Code", "search": "搜索", "columns": {"id": "ID", "roleName": "角色名稱", "roleCode": "角色代碼", "description": "描述", "status": "狀態", "createdAt": "創建時間", "operations": "操作"}, "status": {"enabled": "啟用", "disabled": "禁用"}, "operations": {"edit": "編輯", "delete": "刪除"}, "deleteConfirm": {"title": "警告", "message": "確定要刪除角色 \"{name}\" 嗎？", "confirm": "確定", "cancel": "取消"}, "messages": {"deleteSuccess": "成功刪除角色 {name}", "deleteFailed": "刪除角色失敗", "fetchFailed": "獲取角色列表失敗"}}}}, "userGroup": {"info": "用戶組資訊", "form": {"groupName": {"label": "用戶組名稱", "placeholder": "請輸入用戶組名稱", "required": "請輸入用戶組名稱"}, "groupIdentifier": {"label": "用戶組標識符", "placeholder": "請輸入用戶組標識符", "required": "請輸入用戶組標識符"}, "description": {"label": "描述", "placeholder": "請輸入描述"}, "rules": {"title": "用戶組規則", "category": {"label": "請選擇屬性分類", "userBasic": "用戶基礎字段"}, "attribute": {"label": "請選擇屬性"}, "relation": {"label": "請選擇關係", "equal": "等於", "notEqual": "不等於", "contains": "包含"}, "value": {"label": "請選擇屬性值", "placeholder": "請輸入屬性值"}}, "addRule": "新增規則", "confirm": "確認", "create": "創建", "buttons": {"confirm": "確認", "create": "創建"}}}, "userAffi": {"department": {"title": "所屬部門", "entry": "辦理入職", "empty": "暫無部門資訊", "dialog": {"title": "變更部門", "changeTip": "將\"{name}\"的部門變更為:", "organization": "組織機構", "selected": "已選 · {count}個部門", "clear": "清空", "mainDepartment": "主部門", "setMain": "設為主部門", "buttons": {"cancel": "取消", "confirm": "確定"}}}, "post": {"title": "所屬崗位", "setPost": "設置崗位", "empty": "暫無崗位", "dialog": {"title": "設置崗位", "placeholder": "請選擇關聯崗位", "emptyTip": "暫無相關崗位", "create": "去創建", "buttons": {"cancel": "取消", "confirm": "確定"}}}, "tenant": {"title": "所屬租戶", "columns": {"selection": "選擇", "info": "租戶資訊", "department": "租戶部門", "application": "應用資訊"}, "dialog": {"title": "暫無分組", "tip": "暫時沒有分組資訊，您可以創建分組，新建完成後再給此用戶新增分組", "buttons": {"cancel": "取消", "create": "前往新建"}}}, "userGroup": {"title": "所屬用戶組", "addStatic": "新增靜態用戶組", "columns": {"name": "組名", "code": "唯一標識", "description": "簡介", "type": "用戶組類型", "operations": "操作"}, "type": {"dynamic": "動態用戶組", "static": "靜態用戶組"}, "dialog": {"title": "選擇用戶組", "label": "選擇用戶組:", "placeholder": "選擇用戶組", "buttons": {"cancel": "取消", "confirm": "確定", "create": "前往新建"}}, "delete": {"title": "操作", "confirm": "確定刪除該數據嗎?"}}, "userRole": {"title": "用戶角色", "addRole": "新增角色", "columns": {"roleId": "角色 ID", "roleCode": "角色 Code", "roleName": "角色名稱", "description": "角色描述", "operations": "操作"}, "dialog": {"title": "用戶角色", "label": "選擇角色:", "placeholder": "選擇角色", "rules": {"required": "請選擇角色"}, "buttons": {"cancel": "取消", "confirm": "確定"}}, "delete": {"title": "操作", "confirm": "確定刪除該數據嗎?"}}}}