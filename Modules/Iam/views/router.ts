import { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  {
    path: '/Iam',
    component: () => import('/admin/layout/iamPage.vue'),
    meta: { title: 'Iam.routes.settings' },
    children: [
      {
        path: 'iamcenter',
        name: 'iamcenter',
        meta: { title: 'Iam.routes.frontend', i18n: true },
        component: () => import('./ui/frontend.vue'),
      },
      {
        path: 'admin',
        name: 'admin',
        meta: { title: 'Iam.routes.backend' },
        component: () => import('./ui/frontend.vue'),
      },
      // 身份源
      {
        path: 'identity',
        name: 'identity',
        meta: { title: 'Iam.routes.identity.title' },
        children: [
          {
            path: 'enterprise',
            name: 'enterprise',
            meta: { title: 'Iam.routes.identity.enterprise' },
            component: () => import('./ui/identity/ui/enterprise/index.vue'),
          },
          {
            path: 'enterprise/tmpList',
            name: 'enterpriseTmpList',
            meta: { title: 'Iam.routes.identity.enterpriseTmpList' },
            component: () => import('./ui/identity/ui/enterprise/template.vue'),
          },
          {
            path: 'enterprise/tmpList/detail/:type',
            name: 'enterpriseTmpListDetail',
            meta: { title: 'Iam.routes.identity.enterpriseTmpListDetail' },
            component: () => import('./ui/identity/ui/enterprise/tempDetail.vue'),
          },
          {
            path: 'enterprise/create/:type',
            name: 'EnterpriseCreate',
            meta: { title: 'Iam.routes.identity.enterpriseCreate' },
            component: () => import('./ui/identity/ui/enterprise/form.vue'),
          },
          {
            path: 'enterprise/detail/:id',
            name: 'EnterpriseDetail',
            meta: { title: 'Iam.routes.identity.enterpriseDetail' },
            component: () => import('./ui/identity/ui/enterprise/form.vue'),
          },

          {
            path: 'social',
            name: 'Social',
            meta: { title: 'Iam.routes.identity.social1' },
            component: () => import('./ui/identity/ui/social/index.vue'),
          },
          {
            path: 'social/tmpList',
            name: 'SocialTmpList',
            meta: { title: 'Iam.routes.identity.socialTmpList' },
            component: () => import('./ui/identity/ui/social/template.vue'),
          },
          {
            path: 'social/tmpList/detail/:type',
            name: 'SocialTmpListDetail',
            meta: { title: 'Iam.routes.identity.socialTmpListDetail' },
            component: () => import('./ui/identity/ui/social/tempDetail.vue'),
          },
          {
            path: 'social/create/:type',
            name: 'SocialCreate',
            meta: { title: 'Iam.routes.identity.socialCreate' },
            component: () => import('./ui/identity/ui/social/form.vue'),
          },
        ],
      },
      // 用户管理
      {
        path: 'userManager',
        name: 'iamUserManager',
        meta: { title: 'Iam.routes.userManager.title' },
        children: [
          {
            path: 'list',
            name: 'iamUserManagerList',
            meta: { title: 'Iam.routes.userManager.list.title' },
            children: [
              {
                path: 'index',
                name: 'iamUserManagerListIndex',
                component: () => import('./ui/userManager/list/index.vue'),
              },
              {
                path: 'form/:id',
                name: 'iamUserManagerListForm',
                meta: { title: 'Iam.routes.userManager.list.detail' },
                component: () => import('./ui/userManager/list/form.vue'),
              },
            ],
          },
          {
            path: 'group',
            name: 'iamUserManagerGroup',
            meta: { title: 'Iam.routes.userManager.group.title' },
            children: [
              {
                path: 'index',
                name: 'iamUserManagerGroupIndex',
                meta: { title: 'Iam.routes.userManager.group.list' },
                component: () => import('./ui/userManager/group/index.vue'),
              },
              {
                path: 'form/:code',
                name: 'iiamUserManagerGroupIndexForm',
                meta: { title: 'Iam.routes.userManager.group.detail' },
                component: () => import('./ui/userManager/group/form.vue'),
              },
              {
                path: 'form',
                name: 'iiamUserManagerGroupIndexCreate',
                meta: { title: 'Iam.routes.userManager.group.create' },
                component: () => import('./ui/userManager/group/form.vue'),
              },
            ],
          },
          {
            path: 'organize',
            name: 'iamUserManagerOrganize',
            meta: { title: 'Iam.routes.userManager.organize.title' },
            component: () => import('./ui/userManager/organize/index.vue'),
          },
          {
            path: 'postManagement',
            name: 'iamUserManagerPostManagement',
            meta: { title: 'Iam.routes.userManager.postManagement.title' },
            component: () => import('./ui/userManager/postManagement/index.vue'),
          },
          {
            path: 'postManagement/create',
            name: 'iamUserManagerPostManagementCreate',
            meta: { title: 'Iam.routes.userManager.postManagement.create' },
            component: () => import('./ui/userManager/postManagement/create.vue'),
          },
          {
            path: 'postManagement/edit/:code',
            name: 'iamUserManagerPostManagementUp',
            meta: { title: 'Iam.routes.userManager.postManagement.edit' },
            component: () => import('./ui/userManager/postManagement/create.vue'),
          },
        ],
      },
      // 权限管理
      {
        path: 'permission',
        name: 'iamPermission',
        meta: { title: 'Iam.routes.permission.title' },
        children: [
          {
            path: 'role',
            name: 'iamPermissionRole',
            meta: { title: 'Iam.routes.permission.role.title' },
            component: () => import('./ui/permission/role/index.vue'),
          },
          {
            path: 'role/create',
            name: 'iamPermissionRoleCreate',
            meta: { title: 'Iam.routes.permission.role.create' },
            component: () => import('./ui/permission/role/form.vue'),
          },
          {
            path: 'role/edit/:id',
            name: 'iamPermissionRoleEdit',
            meta: { title: 'Iam.routes.permission.role.edit' },
            component: () => import('./ui/permission/role/form.vue'),
          },
        ],
      },
      // 品牌化
      {
        path: 'branding',
        name: 'branding',
        meta: { title: 'Iam.routes.branding.title' },
        children: [
          {
            path: 'index',
            name: 'index',
            meta: { title: 'Iam.routes.branding.message' },
            component: () => import('./ui/branding/ui/index.vue'),
          },
        ],
      },
      // 安全设置
      {
        path: 'security',
        name: 'security',
        meta: { title: 'Iam.routes.security.title' },
        children: [
          {
            path: 'mfa',
            name: 'mfa',
            meta: { title: 'Iam.routes.security.mfa1' },
            component: () => import('./ui/security/ui/mfa.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/iam/permission/role',
    component: () => import('./ui/permission/role/index.vue'),
  },
  {
    path: '/iam/permission/role/create',
    component: () => import('./ui/permission/role/components/CreateRole.vue'),
  },
]

export default router
