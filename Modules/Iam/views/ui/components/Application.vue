<template>
  <div class="application-container">
    <!-- 基本信息卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.application.basicInfo.title') }}</div>
      <el-form :model="baseInfo" label-position="top" :rules="baseInfoRules" @change="baseInfoChange">
        <div class="form-row">
          <el-form-item :label="$t('Iam.application.basicInfo.appName')" prop="name" class="form-item">
            <el-input v-model="baseInfo.name" size="large" />
          </el-form-item>
          <el-form-item :label="$t('Iam.application.basicInfo.appDescription')" class="form-item">
            <el-input v-model="baseInfo.desc" size="large" />
          </el-form-item>
        </div>
        
        <div class="form-row">
          <el-form-item :label="$t('Iam.application.basicInfo.appLogo')" class="form-item">
            <el-upload class="flex justify-center items-center w-24 h-24 bg-blue-100 rounded-lg" :show-file-list="false">
              <img v-if="baseInfo.logo" :src="baseInfo.logo" class="w-full h-full" />
              <el-icon v-else size="30" color="#007ee5"><Picture /></el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item :label="$t('Iam.application.basicInfo.visibility')" class="form-item">
            <el-checkbox-group v-model="baseInfo.visibility" size="large">
              <el-checkbox value="web" name="web">{{ $t('Iam.application.basicInfo.web') }}</el-checkbox>
              <el-checkbox value="mobile" name="mobile">{{ $t('Iam.application.basicInfo.mobile') }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        
        <div class="form-actions">
          <el-button :disabled="baseInfoChangeTag">{{ $t('Iam.application.buttons.reset') }}</el-button>
          <el-button type="primary" @click="baseInfoSubmit">{{ $t('Iam.application.buttons.save') }}</el-button>
        </div>
      </el-form>
    </div>

    <!-- 端点信息卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.application.endpointInfo.title') }}</div>
      <div class="endpoints-grid">
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.appId') }}</div>
          <div class="endpoint-value">{{ data.certifiedAddress }}</div>
        </div>
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.jwksEndpoint') }}</div>
          <div class="endpoint-value">
            <a :href="jwksUrl" target="_blank" class="endpoint-link">{{ jwksUrl }}</a>
          </div>
        </div>
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.appSecret') }}</div>
          <div class="endpoint-value">{{ data.appSecret }}</div>
        </div>
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.authEndpoint') }}</div>
          <div class="endpoint-value">{{ authEndpoint }}</div>
        </div>
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.tokenEndpoint') }}</div>
          <div class="endpoint-value">{{ tokenEndpoint }}</div>
        </div>
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.issuer') }}</div>
          <div class="endpoint-value">{{ issuer }}</div>
        </div>
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.userInfoEndpoint') }}</div>
          <div class="endpoint-value">{{ userInfoEndpoint }}</div>
        </div>
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.discoveryEndpoint') }}</div>
          <div class="endpoint-value">
            <a :href="discoveryEndpoint" target="_blank" class="endpoint-link">{{ discoveryEndpoint }}</a>
          </div>
        </div>
        <div class="endpoint-item">
          <div class="endpoint-label">{{ $t('Iam.application.endpointInfo.logoutEndpoint') }}</div>
          <div class="endpoint-value">
            <a :href="logoutEndpoint" target="_blank" class="endpoint-link">{{ logoutEndpoint }}</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 认证配置卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.application.authConfig.title') }}</div>
      <el-form :model="attestationInfo" :rules="attestationInfoRules" label-position="top" @change="attestationInfoChange">
        <div class="form-row">
          <el-form-item :label="$t('Iam.application.authConfig.authAddress')" prop="identifier" class="form-item">
            <el-input v-model="shortIdentifier" placeholder="My Site" disabled size="large">
              <template #prepend>https://</template>
              <template #append>.bingo-test.com<el-icon size="14" class="ml-2" @click="copyHandle(attestationInfo.identifier)"><CopyDocument /></el-icon></template>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('Iam.application.authConfig.initiateLoginUrl')" class="form-item">
            <el-input v-model="attestationInfo.initiateLoginUrl" size="large"></el-input>
          </el-form-item>
        </div>
        
        <div class="form-row">
          <el-form-item :label="$t('Iam.application.authConfig.loginCallbackUrl')" prop="loginUrl" class="form-item">
            <el-input v-model="attestationInfo.loginUrl" type="textarea" :rows="2" :placeholder="$t('Iam.application.authConfig.multipleUrlTip')" size="large"></el-input>
          </el-form-item>
          <el-form-item :label="$t('Iam.application.authConfig.logoutCallbackUrl')" class="form-item">
            <el-input v-model="attestationInfo.logoutUrl" type="textarea" :rows="2" :placeholder="$t('Iam.application.authConfig.multipleUrlTip')" size="large"></el-input>
          </el-form-item>
        </div>
        
        <div class="form-actions">
          <el-button :disabled="attestationInfoChangeTag">{{ $t('Iam.application.buttons.reset') }}</el-button>
          <el-button type="primary" @click="attestationInfoSubmit">{{ $t('Iam.application.buttons.save') }}</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'
const { t } = useI18n()

// 接收父组件传递的数据
const props = defineProps<{
  data: any
}>()

// 创建响应式数据
const baseInfo = reactive({
  name: '',
  desc: '',
  logo: '',
  visibility: [],
})

const attestationInfo = reactive({
  certifiedAddress: '',
  loginUrl: '',
  logoutUrl: '',
  initiateLoginUrl: '',
  identifier: '',
})

// 计算属性
const baseUrl = computed(() => `${props.data.identifier}`)
const jwksUrl = computed(() => `${baseUrl.value}/oidc/.well-known/jwks.json`)
const authEndpoint = computed(() => `${baseUrl.value}/oidc/auth`)
const tokenEndpoint = computed(() => `${baseUrl.value}/oidc/token`)
const issuer = computed(() => `${baseUrl.value}/oidc`)
const userInfoEndpoint = computed(() => `${baseUrl.value}/oidc/me`)
const discoveryEndpoint = computed(() => `${baseUrl.value}/oidc/.well-known/openid-configuration`)
const logoutEndpoint = computed(() => `${baseUrl.value}/oidc/session/end`)

// 添加一个新的计算属性
const shortIdentifier = computed(() => {
  const parts = attestationInfo.identifier.split('.')
  return parts.length > 1 ? parts[0] : attestationInfo.identifier
})

// 监听props.data的变化，更新本地数据
watch(
  () => props.data,
  newData => {
    if (newData) {
      baseInfo.name = newData.name
      baseInfo.desc = newData.desc
      baseInfo.logo = newData.logo
      baseInfo.visibility = newData.visibility

      attestationInfo.certifiedAddress = newData.certifiedAddress
      attestationInfo.loginUrl = newData.loginUrl
      attestationInfo.logoutUrl = newData.logoutUrl
      attestationInfo.initiateLoginUrl = newData.initiateLoginUrl
      attestationInfo.identifier = newData.identifier ? newData.identifier.replace(/^https?:\/\//, '').replace(/\.bingo-test\.com$/, '') : ''
    }
  },
  { immediate: true, deep: true },
)
const appCode = ref(localStorage.getItem('app_code') || 'admin')
// 应用配置 --> 基本信息
const baseInfoChangeTag = ref(true)
const baseInfoSubmit = async () => {
  try {
    const response = await http.put(`/iam/applications/${appCode.value}`, {
      name: baseInfo.name,
      description: baseInfo.desc,
      logo: baseInfo.logo,
      applicationInvisibilityType: baseInfo.visibility,
    })
  } catch (error) {}
}
const baseInfoChange = () => {
  baseInfoChangeTag.value = false
}
const baseInfoRules = {
  name: [{ required: true, message: t('Iam.application.validation.appNameRequired'), trigger: 'blur' }],
}

// 应用配置 --> 认证配置
const attestationInfoChangeTag = ref(true)
const attestationInfoSubmit = async () => {
  try {
    const response = await http.put(`/iam/applications/${appCode.value}`, {
      initLoginUrl: attestationInfo.loginUrl,
      logoutRedirectUris: attestationInfo.logoutUrl.split(',').map(url => url.trim()),
      protocol: 'saml',
      redirectUris: attestationInfo.initiateLoginUrl.split(',').map(url => url.trim()),
    })
  } catch (error) {}
}
const attestationInfoChange = () => {
  attestationInfoChangeTag.value = false
}
const attestationInfoRules = {
  certifiedAddress: [{ required: true, message: t('Iam.application.validation.authAddressRequired'), trigger: 'blur' }],
  loginUrl: [{ required: true, message: t('Iam.application.validation.loginCallbackRequired'), trigger: 'blur' }],
}

const copyHandle = (url: string) => {
  navigator.clipboard.writeText(`https://${url}.bingo-test.com`).then(
    () => ElMessage.success(t('Iam.application.messages.copySuccess')),
    () => ElMessage.error(t('Iam.application.messages.copyFailed')),
  )
}
</script>

<style lang="scss" scoped>
.section-card {
  margin-bottom: 26px;
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 26px;
}

.form-item {
  font-size: 16px;
  color: #000;
  
}

.form-actions {
  display: flex;
  justify-content: center;
}

.endpoints-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 26px;
}

.endpoint-label {
  font-size: 16px;
  color: #000;
  margin-bottom: 8px;
}

.endpoint-value {
  font-size: 16px;
  color: #000;
  word-break: break-all;
}

.endpoint-link {
  color: #007ee5;
  text-decoration: none;
  transition: color 0.3s;
  font-size: 16px;
  
  &:hover {
    color: #0056b3;
    text-decoration: underline;
  }
}
</style>
