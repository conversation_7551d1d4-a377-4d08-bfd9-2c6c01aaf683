<template>
  <div class="login-ctrl-container">
    <!-- 登录方式选择卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.title') }}</div>
      <div class="login-way-grid">
        <div class="login-way-item" v-for="(item, index) in loginWay" :key="index" 
             :class="{ active: checkedWay === item.val }" 
             @click="checkedWayHandle(item.val)">
          <div class="login-way-title">{{ item.title }}</div>
          <div class="login-way-desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>

    <!-- 登录字段配置卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.table.accountField') }}</div>
      <el-table :data="loginField" style="width: 100%" class="custom-table">
        <template #empty>
          <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
        </template>
        <el-table-column :label="$t('Iam.loginCtrl.table.sort')" width="80">
          <template #default="scope">
            <el-input type="number" v-model="scope.row.sort" @input="sortHandle"></el-input>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Iam.loginCtrl.table.accountField')">
          <template #default="scope">
            <div class="field-selector">
              <el-select v-model="scope.row.field" :disabled="scope.row.disabled" :placeholder="$t('Iam.loginCtrl.placeholder.selectLoginField')" size="large" @change="changeHandle">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-icon color="#333" size="16" class="delete-icon" :class="{ hide: scope.row.disabled }" @click="delHandle(scope.$index)"><Remove /></el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('Iam.loginCtrl.table.authMethod')" prop="authenticateWay" width="100"></el-table-column>
        <el-table-column :label="$t('Iam.loginCtrl.table.useForLogin')" width="100">
          <template #default="scope">
            <el-checkbox v-model="scope.row.isLogin" @change="checkLogin(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('Iam.loginCtrl.table.useForRegister')" width="100">
          <template #default="scope">
            <el-checkbox v-model="scope.row.isRegister" :disabled="!scope.row.isLogin" @change="changeHandle" />
          </template>
        </el-table-column>
      </el-table>
      <div class="add-button-container">
        <el-button link type="primary" @click="addHandle">
          <el-icon class="shrink-0" size="16" color="#007ee5"><Plus /></el-icon>
          <span>{{ $t('Iam.loginCtrl.buttons.addAccountField') }}</span>
        </el-button>
      </div>
      <div class="form-actions">
        <el-button :disabled="changeTag">{{ $t('Iam.loginCtrl.buttons.reset') }}</el-button>
        <el-button type="primary" @click="baseInfoSubmit">{{ $t('Iam.loginCtrl.buttons.save') }}</el-button>
      </div>
    </div>

    <!-- 默认登录/注册方式卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.defaultSettings') }}</div>
      
      <div class="setting-group">
        <div class="setting-label">{{ $t('Iam.loginCtrl.defaultLoginMethod.title') }}</div>
        <div class="setting-desc">{{ $t('Iam.loginCtrl.defaultLoginMethod.description') }}</div>
        <el-select v-model="defaultLoginTab" :placeholder="$t('Iam.loginCtrl.placeholder.selectDefaultLoginMethod')" class="setting-control">
          <el-option v-for="item in loginTabOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      
      <div class="setting-group">
        <div class="setting-label">{{ $t('Iam.loginCtrl.defaultRegisterMethod.title') }}</div>
        <div class="setting-desc">{{ $t('Iam.loginCtrl.defaultRegisterMethod.description') }}</div>
        <el-select v-model="defaultRegisterTab" :placeholder="$t('Iam.loginCtrl.placeholder.selectDefaultRegisterMethod')" class="setting-control">
          <el-option v-for="item in registerTabOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      
      <div class="setting-group">
        <div class="setting-label">{{ $t('Iam.loginCtrl.defaultLoginType.title') }}</div>
        <div class="setting-desc">{{ $t('Iam.loginCtrl.defaultLoginType.description') }}</div>
        <el-radio-group v-model="normalLogin" class="setting-control">
          <el-radio value="1">{{ $t('Iam.loginCtrl.defaultLoginType.normal') }}</el-radio>
          <el-radio value="2">{{ $t('Iam.loginCtrl.defaultLoginType.qrCode') }}</el-radio>
        </el-radio-group>
      </div>
    </div>
    
    <!-- 社交登录配置卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.socialLogin.title') }}</div>
      <div class="social-login-container">
        <el-card shadow="hover" class="social-login-add-card">
          <div class="flex justify-center items-center">
            <el-icon class="shrink-0" size="30" color="#007ee5"><Plus /></el-icon>
            <p class="pl-4">{{ $t('Iam.loginCtrl.socialLogin.addIdentitySource') }}</p>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 相关应用卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.relatedApps.title') }}</div>
      <el-empty class="empty-container" image-size="100px" :description="$t('Iam.loginCtrl.relatedApps.noApps')"></el-empty>
    </div>

    <!-- 在线用户管理卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.onlineUsers.title') }}</div>
      <el-table :data="userLoginList" style="width: 100%" height="400" class="custom-table">
        <template #empty>
          <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
        </template>
        <el-table-column type="selection" width="55" />
        <el-table-column property="userInfo" :label="$t('Iam.loginCtrl.onlineUsers.columns.userInfo')" />
        <el-table-column property="phone" :label="$t('Iam.loginCtrl.onlineUsers.columns.phone')" />
        <el-table-column property="mail" :label="$t('Iam.loginCtrl.onlineUsers.columns.email')" />
        <el-table-column property="loginCount" :label="$t('Iam.loginCtrl.onlineUsers.columns.loginCount')" width="100" />
        <el-table-column property="loginAt" :label="$t('Iam.loginCtrl.onlineUsers.columns.lastLoginTime')" width="220" />
      </el-table>
    </div>

    <!-- 登录注册相关设置卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.registrationSettings') }}</div>
      
      <div class="setting-group">
        <div class="setting-label">{{ $t('Iam.loginCtrl.autoRegisterLogin.title') }}</div>
        <div class="setting-desc">{{ $t('Iam.loginCtrl.autoRegisterLogin.description') }}</div>
        <el-switch v-model="autoRegisterThenLogin" @change="changeHandle" class="setting-control" />
      </div>
      
      <div class="setting-group">
        <div class="setting-label">{{ $t('Iam.loginCtrl.mergeLoginRegister.title') }}</div>
        <div class="setting-desc">{{ $t('Iam.loginCtrl.mergeLoginRegister.description') }}</div>
        <el-switch v-model="loginRegisterMerged" @change="changeHandle" class="setting-control" />
      </div>
    </div>

    <!-- 登录方式排序卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.loginMethodsSort.title') }}</div>
      <div class="setting-desc mb-4">{{ $t('Iam.loginCtrl.loginMethodsSort.description') }}</div>
      <el-table :data="loginMethodsSort" row-key="method" :max-height="400" class="custom-table">
        <template #empty>
          <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
        </template>
        <el-table-column :label="$t('Iam.loginCtrl.loginMethodsSort.columns.sort')" width="70">
          <template #default="scope">
            <el-input-number v-model="scope.row.sort" :min="1" :max="loginMethodsSort.length" @change="updateLoginMethodsSort" />
          </template>
        </el-table-column>
        <el-table-column prop="method" :label="$t('Iam.loginCtrl.loginMethodsSort.columns.loginMethod')">
          <template #default="scope">
            {{ getLoginMethodLabel(scope.row.method) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Iam.loginCtrl.loginMethodsSort.columns.operations')" width="180">
          <template #default="scope">
            <div class="operations-container">
              <el-button size="small" @click="moveLoginMethod(scope.$index, -1)" :disabled="scope.$index === 0">
                {{ $t('Iam.loginCtrl.loginMethodsSort.buttons.moveUp') }}
              </el-button>
              <el-button size="small" @click="moveLoginMethod(scope.$index, 1)" :disabled="scope.$index === loginMethodsSort.length - 1">
                {{ $t('Iam.loginCtrl.loginMethodsSort.buttons.moveDown') }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 密码登录配置卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.passwordLoginConfig.title') }}</div>
      <div class="setting-desc mb-4">{{ $t('Iam.loginCtrl.passwordLoginConfig.description') }}</div>
      <el-checkbox-group v-model="passwordLoginMethods" @change="changeHandle" class="checkbox-group">
        <el-checkbox label="phone-password">{{ $t('Iam.loginCtrl.passwordLoginConfig.options.phonePassword') }}</el-checkbox>
        <el-checkbox label="email-password">{{ $t('Iam.loginCtrl.passwordLoginConfig.options.emailPassword') }}</el-checkbox>
        <el-checkbox label="username-password">{{ $t('Iam.loginCtrl.passwordLoginConfig.options.usernamePassword') }}</el-checkbox>
      </el-checkbox-group>
    </div>

    <!-- 验证码登录配置卡片 -->
    <div class="section-card">
      <div class="section-title">{{ $t('Iam.loginCtrl.verifyCodeLoginConfig.title') }}</div>
      <div class="setting-desc mb-4">{{ $t('Iam.loginCtrl.verifyCodeLoginConfig.description') }}</div>
      <el-checkbox-group v-model="verifyCodeLoginMethods" @change="changeHandle" class="checkbox-group">
        <el-checkbox label="phone-code">{{ $t('Iam.loginCtrl.verifyCodeLoginConfig.options.phoneCode') }}</el-checkbox>
        <el-checkbox label="email-code">{{ $t('Iam.loginCtrl.verifyCodeLoginConfig.options.emailCode') }}</el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import http from '/admin/support/http'

const { t } = useI18n()

// 接收父组件传递的数据
const props = defineProps<{
  data: any
}>()
const appCode = ref(localStorage.getItem('app_code') || 'admin')
const loginWay = ref([
  {
    val: '登录注册合并',
    title: t('Iam.loginCtrl.loginWay.mergedTitle'),
    desc: t('Iam.loginCtrl.loginWay.mergedDesc'),
  },
  {
    val: '登录注册分页',
    title: t('Iam.loginCtrl.loginWay.separateTitle'),
    desc: t('Iam.loginCtrl.loginWay.separateDesc'),
  },
])
const checkedWay = ref('登录注册合并')
const loginField = ref([
  {
    sort: 0,
    field: '手机号-phone',
    authenticateWay: t('Iam.loginCtrl.authMethod.verifyCode'),
    isLogin: true,
    isRegister: true,
    disabled: true,
  },
  {
    sort: 1,
    field: '邮箱-email',
    authenticateWay: t('Iam.loginCtrl.authMethod.verifyCode'),
    isLogin: true,
    isRegister: true,
    disabled: true,
  },
  {
    sort: 2,
    field: '手机号-phone',
    authenticateWay: t('Iam.loginCtrl.authMethod.password'),
    isLogin: false,
    isRegister: false,
    disabled: true,
  },
  {
    sort: 3,
    field: '邮箱-email',
    authenticateWay: t('Iam.loginCtrl.authMethod.password'),
    isLogin: false,
    isRegister: false,
    disabled: true,
  },
  {
    sort: 4,
    field: '用户名-username',
    authenticateWay: t('Iam.loginCtrl.authMethod.password'),
    isLogin: false,
    isRegister: false,
    disabled: true,
  },
])
const options = ref([
  {
    label: t('Iam.loginCtrl.fieldOptions.identityNumber'),
    value: '1',
  },
  {
    label: t('Iam.loginCtrl.fieldOptions.externalId'),
    value: '2',
  },
])
const changeTag = ref(true)
const sortHandle = () => {
  loginField.value = loginField.value.sort((a, b) => a.sort - b.sort)
  changeHandle()
}
const addHandle = () => {
  loginField.value.push({
    sort: loginField.value[loginField.value.length - 1].sort + 1,
    field: '',
    authenticateWay: t('Iam.loginCtrl.authMethod.password'),
    isLogin: false,
    isRegister: false,
    disabled: false,
  })
  changeHandle()
}
const checkedWayHandle = (val: string) => {
  checkedWay.value = val
  changeHandle()
}
const baseInfoSubmit = async () => {
  // 构建提交的数据
  const submitData = {
    autoRegisterThenLogin: autoRegisterThenLogin.value,
    tabMethodsSortConfig: {
      loginMethodsSort: loginMethodsSort.value.map((item: {method: string}) => item.method),
    },
    enabledPPRegisterValid: enabledPPRegisterValid.value,
    defaultLoginTab: defaultLoginTab.value,
    loginTabs: ['phone-code', 'password'], // 固定为这两个选项
    registerTabs: ['phone', 'emailCode', 'phone-password', 'username-password'], // 固定为这四个选项
    passwordTabConfig: {
      enabledLoginMethods: passwordLoginMethods.value,
      validLoginMethods: passwordLoginMethods.value,
      validRegisterMethods: passwordLoginMethods.value,
    },
    verifyCodeTabConfig: {
      enabledLoginMethods: verifyCodeLoginMethods.value,
      validLoginMethods: verifyCodeLoginMethods.value,
      validRegisterMethods: verifyCodeLoginMethods.value,
    },
    defaultRegisterTab: defaultRegisterTab.value,
  }

  try {
    // 调用设置接口
    const response = await http.put(`/iam/applications/${appCode.value}`, submitData)

    if (response.status === 200) {
      ElMessage.success(t('Iam.loginCtrl.messages.saveSuccess'))
      changeTag.value = true // 重置更改标志
    } else {
      ElMessage.error(t('Iam.loginCtrl.messages.saveFailed'))
    }
  } catch (error) {
    console.error('Error submitting data:', error)
    ElMessage.error(t('Iam.loginCtrl.messages.saveError'))
  }
}
const changeHandle = () => {
  changeTag.value = false
}
const checkLogin = (row: any) => {
  if (!row.isLogin) row.isRegister = false
  changeHandle()
}
const delHandle = (index: number) => {
  loginField.value.splice(index, 1)
}

const passkeyWay = ref(false)
const pushWay = ref(false)
const normalLogin = ref('1')
const userLoginList = ref([
  {
    id: 1,
    userInfo: 'Tyler Rodriguez',
    phone: '******-576-9062x9562',
    mail: '<EMAIL>',
    loginCount: 3,
    loginAt: '2024-07-13 00:27:57',
  },
  {
    id: 2,
    userInfo: 'Rhonda Harris',
    phone: '001-848-060-8539x6760',
    mail: '<EMAIL>',
    loginCount: 98,
    loginAt: '2024-06-05 00:59:07',
  },
  {
    id: 3,
    userInfo: 'Brian Anderson',
    phone: '001-715-393-8560x1929',
    mail: '<EMAIL>',
    loginCount: 69,
    loginAt: '2024-01-04 19:39:50',
  },
  {
    id: 4,
    userInfo: 'Melissa Mendez',
    phone: '******-468-0960x185',
    mail: '<EMAIL>',
    loginCount: 49,
    loginAt: '2024-08-10 13:22:18',
  },
  {
    id: 5,
    userInfo: 'Evan Castro',
    phone: '(953)575-8972',
    mail: '<EMAIL>',
    loginCount: 11,
    loginAt: '2024-01-26 02:43:03',
  },
  {
    id: 6,
    userInfo: 'Dawn Estrada',
    phone: '************',
    mail: '<EMAIL>',
    loginCount: 50,
    loginAt: '2024-05-18 04:10:31',
  },
  {
    id: 7,
    userInfo: 'Joseph Smith',
    phone: '******-781-1442',
    mail: '<EMAIL>',
    loginCount: 46,
    loginAt: '2024-01-14 19:30:19',
  },
  {
    id: 8,
    userInfo: 'Andrew Martin',
    phone: '(252)054-0195',
    mail: '<EMAIL>',
    loginCount: 16,
    loginAt: '2024-08-03 10:38:38',
  },
  {
    id: 9,
    userInfo: 'Christine Johnson',
    phone: '9213255103',
    mail: '<EMAIL>',
    loginCount: 39,
    loginAt: '2024-08-27 12:21:56',
  },
  {
    id: 10,
    userInfo: 'Erica Harris',
    phone: '681.584.1340x9367',
    mail: '<EMAIL>',
    loginCount: 86,
    loginAt: '2024-01-07 17:47:59',
  },
  {
    id: 11,
    userInfo: 'Denise Lopez',
    phone: '(518)188-0616',
    mail: '<EMAIL>',
    loginCount: 8,
    loginAt: '2024-04-13 01:36:18',
  },
  {
    id: 12,
    userInfo: 'Kevin Reed',
    phone: '************',
    mail: '<EMAIL>',
    loginCount: 71,
    loginAt: '2024-08-23 18:32:58',
  },
  {
    id: 13,
    userInfo: 'James Underwood',
    phone: '************',
    mail: '<EMAIL>',
    loginCount: 72,
    loginAt: '2024-07-09 21:43:35',
  },
  {
    id: 14,
    userInfo: 'Jeffrey WillIams',
    phone: '************',
    mail: '<EMAIL>',
    loginCount: 67,
    loginAt: '2024-01-19 21:31:47',
  },
  {
    id: 15,
    userInfo: 'Scott Bell',
    phone: '******-355-7714',
    mail: '<EMAIL>',
    loginCount: 90,
    loginAt: '2024-01-28 09:43:33',
  },
  {
    id: 16,
    userInfo: 'Derrick Anderson',
    phone: '************',
    mail: '<EMAIL>',
    loginCount: 2,
    loginAt: '2024-02-15 16:12:06',
  },
  {
    id: 17,
    userInfo: 'John Simpson',
    phone: '******-655-4272',
    mail: '<EMAIL>',
    loginCount: 77,
    loginAt: '2024-04-29 04:57:55',
  },
  {
    id: 18,
    userInfo: 'Crystal Hill',
    phone: '(122)874-6144x086',
    mail: '<EMAIL>',
    loginCount: 12,
    loginAt: '2024-09-08 08:50:02',
  },
  {
    id: 19,
    userInfo: 'Jonathan Moore',
    phone: '************',
    mail: '<EMAIL>',
    loginCount: 57,
    loginAt: '2024-04-15 09:09:18',
  },
  {
    id: 20,
    userInfo: 'Lauren King',
    phone: '************',
    mail: '<EMAIL>',
    loginCount: 59,
    loginAt: '2024-06-05 10:00:10',
  },
  {
    id: 21,
    userInfo: 'Karen Anderson',
    phone: '555.760.1173x404',
    mail: '<EMAIL>',
    loginCount: 22,
    loginAt: '2024-06-02 10:20:16',
  },
  {
    id: 22,
    userInfo: 'Daniel Wolfe',
    phone: '001-652-453-8457x4268',
    mail: '<EMAIL>',
    loginCount: 68,
    loginAt: '2024-01-30 00:47:14',
  },
  {
    id: 23,
    userInfo: 'Emily White',
    phone: '654.341.5122x339',
    mail: '<EMAIL>',
    loginCount: 65,
    loginAt: '2024-04-18 19:47:38',
  },
  {
    id: 24,
    userInfo: 'Jessica Lamb',
    phone: '************',
    mail: '<EMAIL>',
    loginCount: 94,
    loginAt: '2024-01-24 12:26:22',
  },
  {
    id: 25,
    userInfo: 'Kristi Rice',
    phone: '001-************',
    mail: '<EMAIL>',
    loginCount: 19,
    loginAt: '2024-08-24 00:30:04',
  },
  {
    id: 26,
    userInfo: 'Maureen Castaneda',
    phone: '(039)734-9973x35608',
    mail: '<EMAIL>',
    loginCount: 51,
    loginAt: '2024-05-30 19:02:13',
  },
  {
    id: 27,
    userInfo: 'Brandi Miller',
    phone: '******-186-0573',
    mail: '<EMAIL>',
    loginCount: 3,
    loginAt: '2024-08-27 07:01:57',
  },
  {
    id: 28,
    userInfo: 'Jason Silva',
    phone: '(019)412-8486x25756',
    mail: '<EMAIL>',
    loginCount: 34,
    loginAt: '2024-07-19 00:42:28',
  },
  {
    id: 29,
    userInfo: 'Aaron Evans',
    phone: '289-970-5344x98389',
    mail: '<EMAIL>',
    loginCount: 32,
    loginAt: '2024-07-23 05:23:25',
  },
  {
    id: 30,
    userInfo: 'Kevin Houston',
    phone: '001-071-670-3631x95132',
    mail: '<EMAIL>',
    loginCount: 1,
    loginAt: '2024-07-28 00:58:28',
  },
])

// 添加新的 ref 变量
const loginRequireEmailVerified = ref(false)
const agreementEnabled = ref(false)
const ssoEnabled = ref(false)
const mfaType = ref('')
const loginFailStrategy = ref({})
const loginSmsConfig = ref({})
const registerSmsConfig = ref({})
const accountLock = ref({})
const emailVerifiedDefault = ref(false)
const sendWelcomeEmail = ref(false)
const autoRegisterThenLogin = ref(false)
const enableCompletePassword = ref(false)
const passwordStrength = ref('')
const verifyCodeLength = ref(0)
const verifyCodeMaxAttempts = ref(0)

// 新增的响应式变量
const defaultLoginTab = ref('phone-code')
const defaultRegisterTab = ref('phone')
const enabledPPRegisterValid = ref(true)
const loginRegisterMerged = ref(false)
const loginMethodsSort = ref<Array<{method: string, sort: number}>>([])
const passwordLoginMethods = ref<string[]>([])
const verifyCodeLoginMethods = ref<string[]>([])

// 登录方式选项
const loginTabOptions = [
  { value: 'phone-code', label: t('Iam.loginCtrl.loginOptions.phoneCode') },
  { value: 'password', label: t('Iam.loginCtrl.loginOptions.password') },
]

// 注册方式选项
const registerTabOptions = [
  { value: 'phone', label: t('Iam.loginCtrl.registerOptions.phone') },
  { value: 'emailCode', label: t('Iam.loginCtrl.registerOptions.emailCode') },
  { value: 'phone-password', label: t('Iam.loginCtrl.registerOptions.phonePassword') },
  { value: 'username-password', label: t('Iam.loginCtrl.registerOptions.usernamePassword') },
]

// 获取登录方式标签
const getLoginMethodLabel = (method: string): string => {
  const methodLabels: Record<string, string> = {
    'phone-code': t('Iam.loginCtrl.methodLabels.phoneCode'),
    'email-code': t('Iam.loginCtrl.methodLabels.emailCode'),
    'phone-password': t('Iam.loginCtrl.methodLabels.phonePassword'),
    'email-password': t('Iam.loginCtrl.methodLabels.emailPassword'),
    'username-password': t('Iam.loginCtrl.methodLabels.usernamePassword'),
  }
  return methodLabels[method] || method
}

// 更新登录方式排序
const updateLoginMethodsSort = () => {
  loginMethodsSort.value.sort((a, b) => a.sort - b.sort)
  changeHandle()
}

// 移动登录方式
const moveLoginMethod = (index: number, direction: number) => {
  const newIndex = index + direction
  if (newIndex >= 0 && newIndex < loginMethodsSort.value.length) {
    const temp = loginMethodsSort.value[index]
    loginMethodsSort.value[index] = loginMethodsSort.value[newIndex]
    loginMethodsSort.value[newIndex] = temp
    updateLoginMethodsSort()
  }
}

// 监听props.data的变化，更新本地数据
watch(
  () => props.data,
  newData => {
    if (newData) {
      checkedWay.value = newData.defaultLoginTab || '登录注册合并'

      // 更新loginField
      if (newData.loginTabs) {
        loginField.value = newData.loginTabs.map((tab: string) => ({
          sort: 0, // 可能需要根据实际情况设置排序
          field: tab,
          authenticateWay: tab.includes('password') ? t('Iam.loginCtrl.authMethod.password') : t('Iam.loginCtrl.authMethod.verifyCode'),
          isLogin: true,
          isRegister: newData.registerTabs?.includes(tab) || false,
          disabled: true,
        }))
      }

      // 更新其他字段
      if (newData.loginRequireEmailVerified !== undefined) {
        loginRequireEmailVerified.value = newData.loginRequireEmailVerified
      }

      if (newData.agreementEnabled !== undefined) {
        agreementEnabled.value = newData.agreementEnabled
      }

      if (newData.ssoEnabled !== undefined) {
        ssoEnabled.value = newData.ssoEnabled
      }

      if (newData.mfaType) {
        mfaType.value = newData.mfaType
      }

      if (newData.loginFailStrategy) {
        loginFailStrategy.value = newData.loginFailStrategy
      }

      if (newData.loginSmsConfig) {
        loginSmsConfig.value = newData.loginSmsConfig
      }

      if (newData.registerSmsConfig) {
        registerSmsConfig.value = newData.registerSmsConfig
      }

      if (newData.accountLock) {
        accountLock.value = newData.accountLock
      }

      if (newData.emailVerifiedDefault !== undefined) {
        emailVerifiedDefault.value = newData.emailVerifiedDefault
      }

      if (newData.sendWelcomeEmail !== undefined) {
        sendWelcomeEmail.value = newData.sendWelcomeEmail
      }

      if (newData.autoRegisterThenLogin !== undefined) {
        autoRegisterThenLogin.value = newData.autoRegisterThenLogin
      }

      if (newData.enableCompletePassword !== undefined) {
        enableCompletePassword.value = newData.enableCompletePassword
      }

      if (newData.passwordStrength) {
        passwordStrength.value = newData.passwordStrength
      }

      if (newData.verifyCodeLength) {
        verifyCodeLength.value = newData.verifyCodeLength
      }

      if (newData.verifyCodeMaxAttempts) {
        verifyCodeMaxAttempts.value = newData.verifyCodeMaxAttempts
      }

      // 更新新增的字段
      if (newData.defaultLoginTab) {
        defaultLoginTab.value = newData.defaultLoginTab
      }
      if (newData.defaultRegisterTab) {
        defaultRegisterTab.value = newData.defaultRegisterTab
      }
      if (newData.autoRegisterThenLogin !== undefined) {
        autoRegisterThenLogin.value = newData.autoRegisterThenLogin
      }
      if (newData.enabledPPRegisterValid !== undefined) {
        enabledPPRegisterValid.value = newData.enabledPPRegisterValid
      }
      if (newData.autoRegisterThenLogin !== undefined) {
        autoRegisterThenLogin.value = newData.autoRegisterThenLogin
      }
      if (newData.tabMethodsSortConfig && newData.tabMethodsSortConfig.loginMethodsSort) {
        loginMethodsSort.value = newData.tabMethodsSortConfig.loginMethodsSort.map((method: string, index: number) => ({
          method,
          sort: index + 1,
        }))
      }
      if (newData.passwordTabConfig && newData.passwordTabConfig.enabledLoginMethods) {
        passwordLoginMethods.value = newData.passwordTabConfig.enabledLoginMethods
      }
      if (newData.verifyCodeTabConfig && newData.verifyCodeTabConfig.enabledLoginMethods) {
        verifyCodeLoginMethods.value = newData.verifyCodeTabConfig.enabledLoginMethods
      }
    }
  },
  { immediate: true, deep: true },
)

// 添加组件默认导出以解决导入问题
defineExpose({})
</script>

<style lang="scss" scoped>
.section-card {
  margin-bottom: 26px;
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin-bottom: 20px;
}

.setting-group {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.setting-label {
  font-size: 16px;
  font-weight: 500;
  color: #000;
  margin-bottom: 8px;
}

.setting-desc {
  font-size: 14px;
  color: #606266;
  margin-bottom: 12px;
}

.setting-control {
  width: 100%;
  max-width: 500px;
}

.login-way-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.login-way-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #a0cfff;
    background-color: #ecf5ff;
  }
  
  &.active {
    border-color: #409eff;
    background-color: #ecf5ff;
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
  }
}

.login-way-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.login-way-desc {
  font-size: 14px;
  color: #606266;
}

.custom-table {
  margin-bottom: 16px;
  
  :deep(.el-table__header-wrapper) {
    th {
      font-weight: 500;
      background-color: #f5f7fa;
    }
  }
}

.add-button-container {
  margin-bottom: 20px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.social-login-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.social-login-add-card {
  width: 240px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background-color: #f0f9ff;
  }
}

.field-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .el-select {
    flex: 1;
  }
}

.delete-icon {
  cursor: pointer;
  
  &.hide {
    opacity: 0;
    pointer-events: none;
  }
  
  &:hover {
    color: #f56c6c;
  }
}

.empty-container {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 30px;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  
  .el-checkbox {
    margin-right: 0;
  }
}

.operations-container {
  display: flex;
  gap: 8px;
}


</style>