<template>
  <div class="protocol-container">
  <div class="section-card">
  <el-form :model="form" label-position="top" :rules="rules" @change="formChange">
    <el-form-item :label="$t('Iam.protocol.selectProtocol')">
      <el-radio-group v-model="form.protocolType" size="large" class="w-full" @change="changeProtocolType">
        <el-radio class="w-1/4" value="oidc" :disabled="!form.oidcProviderEnabled">{{ $t('Iam.protocol.protocolTypes.OIDC') }}</el-radio>
        <el-radio class="w-1/4" value="oauth" :disabled="!form.oauthProviderEnabled">{{ $t('Iam.protocol.protocolTypes.OAUTH') }}</el-radio>
        <el-radio class="w-1/4" value="saml" :disabled="!form.samlProviderEnabled">{{ $t('Iam.protocol.protocolTypes.SAML') }}</el-radio>
        <el-radio class="w-1/4" value="cas" :disabled="!form.casProviderEnabled">{{ $t('Iam.protocol.protocolTypes.CAS') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="$t('Iam.protocol.tabs.basic')">
      <el-radio-group v-model="form.protocolConfig" class="mb-2">
        <el-radio-button value="OIDC">{{ $t('Iam.protocol.protocolTypes.OIDC') }}</el-radio-button>
        <el-radio-button value="OAuth 2.0">{{ $t('Iam.protocol.protocolTypes.OAUTH') }}</el-radio-button>
        <el-radio-button value="SAML2">{{ $t('Iam.protocol.protocolTypes.SAML') }}</el-radio-button>
        <el-radio-button value="CAS">{{ $t('Iam.protocol.protocolTypes.CAS') }}</el-radio-button>
      </el-radio-group>
      <el-alert type="info" show-icon :closable="false" v-if="form.protocolConfig === 'OIDC'">
        <p>{{ $t('Iam.protocol.alert.oidcInfo') }} <a href="">{{ $t('Iam.protocol.alert.whichAuthMode') }}</a></p>
      </el-alert>
      <el-alert type="info" show-icon :closable="false" v-if="form.protocolConfig === 'OAuth 2.0'">
        <p>{{ $t('Iam.protocol.alert.oauthDeprecated') }}<a href="">{{ $t('Iam.protocol.alert.implementSSO') }}</a></p>
      </el-alert>
      <el-alert type="info" show-icon :closable="false" v-if="form.protocolConfig === 'SAML2'">
        <p>{{ $t('Iam.protocol.alert.samlConfig') }}<a href="">{{ $t('Iam.protocol.alert.clickForHelp') }}</a></p>
      </el-alert>
      <el-alert type="info" show-icon :closable="false" v-if="form.protocolConfig === 'CAS'">
        <p>{{ $t('Iam.protocol.alert.casConfig') }}<a href="">{{ $t('Iam.protocol.alert.viewDocs') }}</a>。</p>
      </el-alert>
    </el-form-item>

    <!-- S OIDC -->
    <el-form-item :label="$t('Iam.protocol.oidc.grantTypes.title')" v-if="form.protocolConfig === 'OIDC'">
      <el-checkbox-group v-model="form.licensingMode" size="large" class="w-full">
        <el-checkbox class="w-1/4" value="authorization_code" name="authorization_code">{{ $t('Iam.protocol.oidc.grantTypes.authorizationCode') }}</el-checkbox>
        <el-checkbox class="w-1/4" value="implicit" name="implicit">{{ $t('Iam.protocol.oidc.grantTypes.implicit') }}</el-checkbox>
        <el-checkbox class="w-1/4" value="refresh_token" name="refresh_token">{{ $t('Iam.protocol.oidc.grantTypes.refreshToken') }}</el-checkbox>
        <el-checkbox class="w-1/4" value="password" name="password">{{ $t('Iam.protocol.oidc.grantTypes.password') }}</el-checkbox>
        <el-checkbox class="w-1/4" value="client_credentials" name="client_credentials">{{ $t('Iam.protocol.oidc.grantTypes.clientCredentials') }}</el-checkbox>
        <el-checkbox class="w-1/4" value="authing_token" name="authing_token">authing_token</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item :label="$t('Iam.protocol.oidc.responseTypes.title')" v-if="form.protocolConfig === 'OIDC'">
      <el-checkbox-group v-model="form.backType" size="large" class="w-full">
        <el-checkbox class="w-1/4" value="code id_token token" name="code id_token token">code id_token token</el-checkbox>
        <el-checkbox class="w-1/4" value="code id_token" name="code id_token">code id_token</el-checkbox>
        <el-checkbox class="w-1/4" value="code token" name="code token">code token</el-checkbox>
        <el-checkbox class="w-1/4" value="code" name="code">{{ $t('Iam.protocol.oidc.responseTypes.code') }}</el-checkbox>
        <el-checkbox class="w-1/4" value="id_token token" name="id_token token">id_token token</el-checkbox>
        <el-checkbox class="w-1/4" value="id_token" name="id_token">{{ $t('Iam.protocol.oidc.responseTypes.idToken') }}</el-checkbox>
        <el-checkbox class="w-1/4" value="none" name="none">none</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item :label="$t('Iam.protocol.jwt.algorithm')" v-if="form.protocolConfig === 'OIDC'">
      <el-radio-group v-model="form.signature" size="large" class="w-full">
        <el-radio class="w-1/4" value="HS256">{{ $t('Iam.protocol.jwt.algorithms.hs256') }}</el-radio>
        <el-radio class="w-1/4" value="RS256">{{ $t('Iam.protocol.jwt.algorithms.rs256') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="form.protocolConfig === 'OIDC'">
      <el-checkbox-group v-model="form.unknown" size="large" class="w-full">
        <el-checkbox class="w-full" value="启用 refresh_token 轮换">{{ $t('Iam.protocol.oidc.options.enableRefreshTokenRotation') }}</el-checkbox>
        <el-checkbox class="w-full" value="轮换 refresh_token 时，同时刷新 refresh_token 有效时间" v-if="form.unknown.indexOf('启用 refresh_token 轮换') !== -1">
          {{ $t('Iam.protocol.oidc.options.refreshTokenLifetime') }}
        </el-checkbox>
        <el-checkbox class="w-full" value="不强制 implicit 模式回调链接为 https">{{ $t('Iam.protocol.oidc.options.noForceHttpsForImplicit') }}</el-checkbox>
        <el-checkbox class="w-full" value="启用 id_token 加密">{{ $t('Iam.protocol.oidc.options.enableIdTokenEncryption') }}</el-checkbox>

        <el-form-item :label="$t('Iam.protocol.oidc.options.idTokenPayloadEncryption')" v-if="form.unknown.indexOf('启用 id_token 加密') !== -1" class="pr-6 w-6/12">
          <el-select v-model="form.idTokenEncryptionAlgorithm" size="large">
            <el-option :label="item.label" :value="item.val" v-for="item in encryptionAlgorithm" :key="item.val" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.oidc.options.idTokenKeyEncryption')" v-if="form.unknown.indexOf('启用 id_token 加密') !== -1" class="pr-6 w-6/12">
          <el-select v-model="form.idTokenSecret" size="large">
            <el-option :label="item.label" :value="item.val" v-for="item in secretList" :key="item.val" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.oidc.options.asymmetricPublicKey')" v-if="form.unknown.indexOf('启用 id_token 加密') !== -1" class="pr-6 w-6/12">
          <el-input v-model="form.idTokenPublic" :rows="5" type="textarea" />
        </el-form-item>

        <el-checkbox class="w-full" value="用户知情同意页面">{{ $t('Iam.protocol.oidc.options.userConsentPage') }}</el-checkbox>
      </el-checkbox-group>
    </el-form-item>

    <el-form-item :label="$t('Iam.protocol.oidc.exchangeVerificationMethod')" v-if="form.protocolConfig === 'OIDC'">
      <el-radio-group v-model="form.exchangeVerificationMethod" size="large" class="w-full">
        <el-radio class="w-1/4" value="client_secret_post">client_secret_post</el-radio>
        <el-radio class="w-1/4" value="client_secret_basic">client_secret_basic</el-radio>
        <el-radio class="w-1/4" value="none">none</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="$t('Iam.protocol.oidc.inspectionVerificationMethod')" v-if="form.protocolConfig === 'OIDC'">
      <el-radio-group v-model="form.inspectionVerificationMethod" size="large" class="w-full">
        <el-radio class="w-1/4" value="client_secret_post">client_secret_post</el-radio>
        <el-radio class="w-1/4" value="client_secret_basic">client_secret_basic</el-radio>
        <el-radio class="w-1/4" value="none">none</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="$t('Iam.protocol.oidc.withdrawalVerificationMethod')" v-if="form.protocolConfig === 'OIDC'">
      <el-radio-group v-model="form.withdrawalVerificationMethod" size="large" class="w-full">
        <el-radio class="w-1/4" value="client_secret_post">client_secret_post</el-radio>
        <el-radio class="w-1/4" value="client_secret_basic">client_secret_basic</el-radio>
        <el-radio class="w-1/4" value="none">none</el-radio>
      </el-radio-group>
    </el-form-item>
    <div class="flex" v-if="form.protocolConfig === 'OIDC'">
      <el-form-item :label="$t('Iam.protocol.oidc.authorizationCodeTime')" class="pr-6 w-6/12">
        <el-input type="number" v-model="form.authorizationCode" size="large">
          <template #append>
            <el-select v-model="form.authorizationCodeUnit" style="width: 80px" size="large">
              <el-option :label="item.label" :value="item.val" v-for="item in timeUnit" :key="item.val" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.oidc.IdTokenTime')" class="pl-6 w-6/12">
        <el-input type="number" v-model="form.idToken" size="large">
          <template #append>
            <el-select v-model="form.idTokenUnit" style="width: 80px" size="large">
              <el-option :label="item.label" :value="item.val" v-for="item in timeUnit" :key="item.val" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
    </div>
    <div class="flex" v-if="form.protocolConfig === 'OIDC'">
      <el-form-item :label="$t('Iam.protocol.oidc.AccessTokenTime')" class="pr-6 w-6/12">
        <el-input type="number" v-model="form.accessToken" size="large">
          <template #append>
            <el-select v-model="form.accessTokenUnit" style="width: 80px" size="large">
              <el-option :label="item.label" :value="item.val" v-for="item in timeUnit" :key="item.val" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.oidc.RefreshTokenTime')" class="pl-6 w-6/12">
        <el-input type="number" v-model="form.refreshToken" size="large">
          <template #append>
            <el-select v-model="form.refreshTokenUnit" style="width: 80px" size="large">
              <el-option :label="item.label" :value="item.val" v-for="item in timeUnit" :key="item.val" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
    </div>
    <!-- E OIDC -->

    <!-- S OAuth 2.0 -->
    <el-form-item :label="$t('Iam.protocol.oauth.enableOAuth2')" v-if="form.protocolConfig === 'OAuth 2.0'">
      <el-switch v-model="form.openOAuth2" style="--el-switch-on-color: #007ee5" />
    </el-form-item>
    <div v-if="form.protocolConfig === 'OAuth 2.0' && form.openOAuth2">
      <el-form-item :label="$t('Iam.protocol.oauth.grantTypes')">
        <el-checkbox-group v-model="form.licensingMmode" size="large" class="w-full">
          <el-checkbox class="w-1/4" value="authorization_code" name="authorization_code">authorization_code</el-checkbox>
          <el-checkbox class="w-1/4" value="implicit" name="implicit">implicit</el-checkbox>
          <el-checkbox class="w-1/4" value="refresh_token" name="refresh_token">refresh_token</el-checkbox>
          <el-checkbox class="w-1/4" value="password" name="password">password</el-checkbox>
          <el-checkbox class="w-1/4" value="client_credentials" name="client_credentials">client_credentials</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.oauth.tokenAuthentication')">
        <el-radio-group v-model="form.tokenAuthentication" size="large" class="w-full">
          <el-radio class="w-1/4" value="client_secret_basic">client_secret_basic</el-radio>
          <el-radio class="w-1/4" value="client_secret_post">client_secret_post</el-radio>
          <el-radio class="w-1/4" value="none">none</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.oauth.withdrawTokenAuthentication')">
        <el-radio-group v-model="form.withdrawTokenAuthentication" size="large" class="w-full">
          <el-radio class="w-1/4" value="client_secret_post">client_secret_post</el-radio>
          <el-radio class="w-1/4" value="none">none</el-radio>
        </el-radio-group>
      </el-form-item>
    </div>
    <!-- E OAuth 2.0 -->

    <!-- S SAML -->
    <el-form-item class="w-6/12" :label="$t('Iam.protocol.saml.entityId')" v-if="form.protocolConfig === 'SAML2'">
      <el-input  v-model="form.samlEntityId" size="large" />
    </el-form-item>
    <div v-if="form.protocolConfig === 'SAML2' && form.openSAML2">
      <div class="flex">
        <el-form-item :label="$t('Iam.protocol.saml.metadata')" class="pr-6 w-6/12">
          <el-alert :closable="false">
            <a href="https://kjls2lpa3p3i-demo.authing.cn/api/v2/saml-idp/66def3772fddad027971ea36/metadata">{{ $t('Iam.protocol.saml.metadataUrl') }}</a>
          </el-alert>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.saml.certificate')" class="pl-6 w-6/12">
          <el-alert :closable="false">
            <a href="https://kjls2lpa3p3i-demo.authing.cn/api/v2/saml-idp/66def3772fddad027971ea36/cert">{{ $t('Iam.protocol.saml.certUrl') }}</a>
          </el-alert>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Iam.protocol.saml.nameId')" class="pr-6 w-6/12">
          <el-input size="large" v-model="form.nameId" :placeholder="$t('Iam.protocol.saml.nameIdPlaceholder')"></el-input>
          <p class="text-xs text-slate-400">{{ $t('Iam.protocol.saml.nameIdDescription') }}</p>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.saml.nameIdFormat')" class="pl-6 w-6/12">
          <el-select v-model="form.nameIdFormat" size="large">
            <el-option :label="item.label" :value="item.val" v-for="item in nameIdFormatList" :key="item.val" />
          </el-select>
          <p class="text-xs text-slate-400">{{ $t('Iam.protocol.saml.nameIdFormatDescription') }}</p>
        </el-form-item>
      </div>
      <el-form-item :label="$t('Iam.protocol.saml.acs')">
        <div class="flex items-center mt-4 mb-2 w-full item">
          <div class="w-48 text-sm shrink-0">{{ $t('Iam.protocol.saml.responseMode') }}</div>
          <div class="flex-1 ml-2 text-sm">{{ $t('Iam.protocol.saml.acsAddress') }}</div>
          <div class="ml-6 w-4 text-sm"></div>
        </div>
        <div class="flex items-center mb-6 w-full item" v-for="(acsItem, index) in acsAddressList" :key="index">
          <el-select size="large" v-model="acsItem.responseMode" :placeholder="$t('Iam.protocol.saml.selectResponseMode')" class="shrink-0" style="--el-select-width: 192px">
            <el-option v-for="item in responseModeList" :key="item.val" :label="item.label" :value="item.val" />
          </el-select>
          <el-input size="large" class="flex-1 ml-2" v-model="acsItem.address" :placeholder="$t('Iam.protocol.saml.enterAcsAddress')"></el-input>
          <el-icon color="#333" size="16" class="ml-6 cursor-pointer delete-btn" @click="delACSItem(index)"><Remove /></el-icon>
        </div>
        <el-button class="w-full add-btn" color="#f7f8fa" @click="addACSItem">{{ $t('Iam.protocol.saml.addAcs') }}</el-button>
        <p class="text-xs text-slate-400">{{ $t('Iam.protocol.saml.acsDescription') }}</p>
      </el-form-item>
      <div class="flex">
        <el-form-item :label="$t('Iam.protocol.saml.audience')" class="pr-6 w-6/12">
          <el-input size="large" v-model="form.audience" :placeholder="$t('Iam.protocol.saml.audiencePlaceholder')"></el-input>
          <p class="text-xs text-slate-400">{{ $t('Iam.protocol.saml.audienceDescription') }}</p>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.saml.recipient')" class="pl-6 w-6/12">
          <el-input size="large" v-model="form.recipient" :placeholder="$t('Iam.protocol.saml.recipientPlaceholder')"></el-input>
          <p class="text-xs text-slate-400">{{ $t('Iam.protocol.saml.recipientDescription') }}</p>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Iam.protocol.saml.destination')" class="pr-6 w-6/12">
          <el-input size="large" v-model="form.destination" :placeholder="$t('Iam.protocol.saml.destinationPlaceholder')"></el-input>
          <p class="text-xs text-slate-400">{{ $t('Iam.protocol.saml.destinationDescription') }}</p>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.saml.responseDigest')" class="pl-6 w-6/12">
          <el-select size="large" v-model="form.SAMLResponse" style="--el-select-width: 100%">
            <el-option v-for="item in SAMLResponse" :key="item.val" :label="item.label" :value="item.val" />
          </el-select>
          <p class="text-xs text-slate-400">{{ $t('Iam.protocol.saml.responseDigestDescription') }}</p>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Iam.protocol.saml.responseSignature')" class="pr-6 w-6/12">
          <el-select size="large" v-model="form.SAMLResponseSign" style="--el-select-width: 100%">
            <el-option v-for="item in SAMLResponseSign" :key="item.val" :label="item.label" :value="item.val" />
          </el-select>
          <p class="text-xs text-slate-400">{{ $t('Iam.protocol.saml.responseSignatureDescription') }}</p>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.saml.assertionExpiration')" class="pl-6 w-6/12">
          <el-input type="number" v-model="form.SAMLExpiration" size="large"></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item :label="$t('Iam.protocol.saml.signResponse')" class="pr-6 w-6/12">
          <el-switch v-model="form.isSAMLResponseSign" style="--el-switch-on-color: #007ee5" />
          <p class="w-full text-xs text-slate-400">{{ $t('Iam.protocol.saml.signResponseDescription') }}</p>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.saml.verifyRequestSignature')" class="pl-6 w-6/12">
          <el-switch v-model="form.isSAMLRequestSign" style="--el-switch-on-color: #007ee5" />
          <p class="w-full text-xs text-slate-400">{{ $t('Iam.protocol.saml.verifyRequestSignatureDescription') }}</p>
        </el-form-item>
      </div>
      <el-form-item :label="$t('Iam.protocol.saml.requestSignatureCert')" v-if="form.isSAMLRequestSign">
        <el-input
          size="large"
          type="textarea"
          v-model="form.SAMLRequestSignCertificate"
          :rows="6"
          placeholder="-----BEGIN CERTIFICATE-----
xxxxxxxxxxxxxxxxxxxxxxx
...
xxxxxxxxxxxxxxxxxxxxxxx
-----END CERTIFICATE-----"
        ></el-input>
        <p class="w-full text-xs text-slate-400">{{ $t('Iam.protocol.saml.requestSignatureCertDescription') }}</p>
      </el-form-item>
      <div class="flex">
        <el-form-item :label="$t('Iam.protocol.saml.logoutConfig')" class="pr-6 w-6/12">
          <el-input v-model="form.SAMLLogout" size="large">
            <template #prepend>
              <el-select v-model="form.SAMLLogoutWay" style="width: 164px" size="large">
                <el-option :label="item.label" :value="item.val" v-for="item in SAMLLogoutList" :key="item.val" />
              </el-select>
            </template>
          </el-input>
          <p class="w-full text-xs text-slate-400">{{ $t('Iam.protocol.saml.logoutConfigDescription') }}</p>
        </el-form-item>
        <el-form-item :label="$t('Iam.protocol.saml.idpEntityId')" class="pl-6 w-6/12">
          <el-input v-model="form.idPEntityID" size="large" :placeholder="$t('Iam.protocol.saml.idpEntityIdPlaceholder')"></el-input>
          <p class="w-full text-xs text-slate-400">{{ $t('Iam.protocol.saml.idpEntityIdDescription') }}</p>
        </el-form-item>
      </div>
      <el-form-item :label="$t('Iam.protocol.saml.customResponseAttributes')">
        <div class="flex items-center mb-6 w-full item" v-for="(SAMLItem, index) in customSAMLResponse" :key="index">
          <el-input size="large" class="flex-1 ml-2" v-model="SAMLItem.label" :placeholder="$t('Iam.protocol.saml.enterNameAttribute')"></el-input>
          <el-select size="large" v-model="SAMLItem.way" class="flex-1 mx-2">
            <el-option v-for="item in customSAMLResponseList" :key="item.val" :label="item.label" :value="item.val" />
          </el-select>
          <el-input size="large" class="flex-1 ml-2" v-model="SAMLItem.val" :placeholder="$t('Iam.protocol.saml.enterValue')"></el-input>
          <el-icon color="#333" size="16" class="ml-6 cursor-pointer delete-btn" @click="delSAMLItem(index)"><Remove /></el-icon>
        </div>
        <el-button class="w-full add-btn" color="#f7f8fa" @click="addSAMLItem">{{ $t('Iam.protocol.saml.addAttribute') }}</el-button>
      </el-form-item>
    </div>
    <!-- E SAML -->

    <!-- S CAS -->
    <el-form-item :label="$t('Iam.protocol.cas.enableCAS')" v-if="form.protocolConfig === 'CAS'">
      <el-switch v-model="form.openCAS" style="--el-switch-on-color: #007ee5" />
    </el-form-item>
    <div v-if="form.protocolConfig === 'CAS' && form.openCAS">
      <el-form-item :label="$t('Iam.protocol.cas.loginEndpoint')">
        <el-alert :closable="false">
          <a href="https://kjls2lpa3p3i-demo.authing.cn/cas-idp/66def3772fddad027971ea36/login">https://kjls2lpa3p3i-demo.authing.cn/cas-idp/66def3772fddad027971ea36/login</a>
        </el-alert>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.cas.logoutEndpoint')">
        <el-alert :closable="false">
          <a href="https://kjls2lpa3p3i-demo.authing.cn/cas-idp/66def3772fddad027971ea36/logout">https://kjls2lpa3p3i-demo.authing.cn/cas-idp/66def3772fddad027971ea36/logout</a>
        </el-alert>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.cas.serviceValidateEndpoint')">
        <el-alert :closable="false">
          <a href="https://kjls2lpa3p3i-demo.authing.cn/cas-idp/66def3772fddad027971ea36/validate">https://kjls2lpa3p3i-demo.authing.cn/cas-idp/66def3772fddad027971ea36/validate</a>
        </el-alert>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.cas.serviceValidateEndpoint2')">
        <el-alert :closable="false">
          <a href="https://kjls2lpa3p3i-demo.authing.cn/cas-idp/66def3772fddad027971ea36/serviceValidate">https://kjls2lpa3p3i-demo.authing.cn/cas-idp/66def3772fddad027971ea36/serviceValidate</a>
        </el-alert>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.cas.serviceTicketExpiry')" class="pr-6 w-6/12">
        <el-input type="number" v-model="form.serviceTicketAt" size="large">
          <template #append>
            <el-select v-model="form.authorizationCodeUnit" style="width: 80px" size="large">
              <el-option :label="item.label" :value="item.val" v-for="item in timeUnit" :key="item.val" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.cas.userId')">
        <el-input v-model="form.CASUserId" size="large" :placeholder="$t('Iam.protocol.cas.userIdPlaceholder')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('Iam.protocol.cas.customResponseFields')">
        <div class="flex items-center mb-6 w-full item" v-for="(casItem, index) in customCASList" :key="index">
          <el-input size="large" class="flex-1 mr-2" v-model="casItem.label" :placeholder="$t('Iam.protocol.cas.enterFieldName')"></el-input>
          <el-input size="large" class="flex-1 ml-2" v-model="casItem.value" :placeholder="$t('Iam.protocol.cas.enterFieldValue')"></el-input>
          <el-icon color="#333" size="16" class="ml-6 cursor-pointer delete-btn" @click="delCASItem(index)"><Remove /></el-icon>
        </div>
        <el-button class="w-full add-btn" color="#f7f8fa" @click="addCASItem">{{ $t('Iam.protocol.cas.addField') }}</el-button>
      </el-form-item>
    </div>
    <!-- E CAS -->

    <div class="flex justify-center">
      <el-button :disabled="changeTag">{{ $t('Iam.protocol.reset') }}</el-button>
      <el-button type="primary" @click="onSubmit">{{ $t('Iam.protocol.save') }}</el-button>
    </div>
  </el-form>
  </div>
  <!-- OIDC Scope 配置卡片 -->
  <div class="section-card mt-6" style="margin-bottom: 0;" v-if="form.protocolConfig === 'OIDC'">
    <div class="section-title">OIDC Scope 配置</div>
    <p class="section-desc">使用 Authing 作为 IdP 时，定制你的 OIDC Scope、claim 以及对应的 Authing 用户字段。<a href="" class="link-text">功能指路</a></p>
    
    <el-collapse class="scope-collapse">
      <el-collapse-item :title="$t('Iam.protocol.oidc.claimMapping')" name="1">
        <div class="claim-item" v-for="(claimItem, index) in claimList" :key="index">
          <div class="flex-1">
            <el-input :disabled="claimItem.disabled" size="large" v-model="claimItem.name" :placeholder="$t('Iam.protocol.oidc.enterClaimName')" @input="claimNameInput(claimItem)"></el-input>
            <p class="error-text" v-if="claimItem.tips">{{ $t('Iam.protocol.oidc.claimExists') }}</p>
          </div>
          <el-icon color="#007ee5" size="18" class="mr-2 ml-2"><Connection /></el-icon>
          <el-select :disabled="claimItem.disabled" size="large" v-model="claimItem.value" :placeholder="$t('Iam.protocol.oidc.selectUserField')" class="flex-1">
            <el-option-group v-for="group in options" :key="group.label" :label="`${group.label} · ${group.options.length}`">
              <el-option v-for="item in group.options" :key="item.value" :label="`${item.label} - ${item.value}`" :value="item.value" />
            </el-option-group>
          </el-select>
          <el-icon color="#333" size="16" class="ml-6 cursor-pointer delete-btn" :class="{ hide: claimItem.disabled }" @click="delClaimItem(index)"><Remove /></el-icon>
        </div>
        <el-button class="w-full add-btn" color="#f7f8fa" @click="addClaimItem">{{ $t('Iam.protocol.oidc.addMappingField') }}</el-button>
      </el-collapse-item>
      
      <el-collapse-item :title="$t('Iam.protocol.oidc.configureScope')" name="2">
        <div class="scope-container">
          <div class="scope-item" v-for="(scopeItem, index) in scopeList" :key="index">
            <div class="line"></div>
            <div class="scope-content">
              <el-input v-model="scopeItem.label" size="large" :disabled="scopeItem.disabled" @input="scopeNameInput(scopeItem)"></el-input>
              <div class="input-box"></div>
              <div class="label">claims:</div>
              <el-select size="large" :placeholder="$t('Iam.protocol.oidc.selectClaims')" :disabled="scopeItem.disabled" v-model="scopeItem.value" multiple class="flex-1" suffix-icon>
                <el-option v-for="item in scopeOpts" :key="item.label" :label="item.value" :value="item.value" />
              </el-select>
              <el-icon color="#333" size="16" class="ml-6 cursor-pointer delete-btn" :class="{ hide: scopeItem.disabled }" @click="delScopeItem(index)"><Remove /></el-icon>
            </div>
            <p class="error-text" v-if="scopeItem.tips">{{ $t('Iam.protocol.oidc.scopeExists') }}</p>
          </div>
          <el-button class="w-full add-btn" color="#fff" @click="addScopeItem">{{ $t('Iam.protocol.oidc.createCustomScope') }}</el-button>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, onMounted } from 'vue'
import http from '/admin/support/http'
import { ElMessage } from 'element-plus'
// 接收父组件传递的数据
const props = defineProps<{
  data: any
}>()
const appCode = ref(localStorage.getItem('app_code') || 'admin')
const form = reactive({
  protocolType: 'OIDC',
  protocolConfig: 'OIDC',
  licensingMode: ['authorization_code', 'refresh_token', 'password'],
  backType: ['code'],
  signature: 'HS256',
  unknown: ['用户知情同意页面'],
  idTokenEncryptionAlgorithm: 'A128CBC-HS256',
  idTokenSecret: 'RSA-OAEP',
  idTokenPublic: '',
  exchangeVerificationMethod: 'client_secret_post',
  inspectionVerificationMethod: 'client_secret_post',
  withdrawalVerificationMethod: 'client_secret_post',
  authorizationCode: 600,
  authorizationCodeUnit: '1',
  idToken: 1209600,
  idTokenUnit: '1',
  accessToken: 1209600,
  accessTokenUnit: '1',
  refreshToken: 2592000,
  refreshTokenUnit: '1',

  openOAuth2: false,
  licensingMmode: ['authorization_code'],
  tokenAuthentication: 'client_secret_basic',
  withdrawTokenAuthentication: 'client_secret_post',

  openSAML2: false,
  nameId: '',
  nameIdFormat: 'unspecified',
  audience: '',
  recipient: '',
  destination: '',
  SAMLResponse: 'SHA1',
  SAMLResponseSign: 'RSA SHA1',
  SAMLExpiration: 3600,
  isSAMLResponseSign: false,
  isSAMLRequestSign: false,
  SAMLLogout: '',
  SAMLRequestSignCertificate: '',
  SAMLLogoutWay: 'HTTP-REDIRECT',
  idPEntityID: '',
  oidcProviderEnabled: false,
  oauthProviderEnabled: false,
  samlProviderEnabled: false,
  casProviderEnabled: false,

  openCAS: false,
  serviceTicketAt: 300,
  CASUserId: '',
})

// 监听props.data的变化，更新本地数据
watch(
  () => props.data,
  newData => {
    if (newData) {
      console.log(newData, 'newData')
      form.protocolType = newData.protocolType || 'OIDC'
      form.protocolConfig = newData.protocolType?.toUpperCase() || 'OIDC'
      form.oidcProviderEnabled = newData.oidcProviderEnabled
      form.oauthProviderEnabled = newData.oauthProviderEnabled
      form.samlProviderEnabled = newData.samlProviderEnabled
      form.casProviderEnabled = newData.casProviderEnabled

      // OIDC 配置回填
      if (newData.oidcConfig) {
        form.licensingMode = newData.oidcConfig.grant_types || ['authorization_code', 'refresh_token', 'password']
        form.backType = newData.oidcConfig.response_types || ['code']
        form.signature = newData.oidcConfig.id_token_signed_response_alg || 'HS256'
        form.exchangeVerificationMethod = newData.oidcConfig.token_endpoint_auth_method || 'client_secret_post'
        form.inspectionVerificationMethod = newData.oidcConfig.introspection_endpoint_auth_method || 'client_secret_post'
        form.withdrawalVerificationMethod = newData.oidcConfig.revocation_endpoint_auth_method || 'client_secret_post'

        // 设置过期时间
        form.authorizationCode = convertFromSeconds(Number(newData.oidcConfig.authorization_code_expire))
        form.idToken = convertFromSeconds(Number(newData.oidcConfig.id_token_expire))
        form.accessToken = convertFromSeconds(Number(newData.oidcConfig.access_token_expire))
        form.refreshToken = convertFromSeconds(Number(newData.oidcConfig.refresh_token_expire))

        // 设置 unknown 数组
        form.unknown = []
        if (newData.oidcConfig.rotate_refresh_token) form.unknown.push('启用 refresh_token 轮换')
        if (newData.oidcConfig.refresh_duration_when_rotate_refresh_token) form.unknown.push('轮换 refresh_token 时，同时刷新 refresh_token 有效时间')
        if (newData.oidcConfig.skip_implicit_flow_rules) form.unknown.push('不强制 implicit 模式回调链接为 https')
        if (newData.oidcConfig.display_prompt_page) form.unknown.push('用户知情同意页面')
      }

      // OAuth 2.0 配置回填
      if (newData.oauthConfig) {
        form.openOAuth2 = true
        form.licensingMmode = newData.oauthConfig.grants || ['authorization_code']
        form.tokenAuthentication = newData.oauthConfig.introspection_endpoint_auth_method || 'client_secret_basic'
        form.withdrawTokenAuthentication = newData.oauthConfig.revocation_endpoint_auth_method || 'client_secret_post'
      }

      // SAML2 配置回填
      if (newData.samlConfig) {
        form.openSAML2 = true
        form.nameIdFormat = newData.samlConfig.nameIdentifierFormat || 'unspecified'
        form.SAMLResponse = getSAMLResponseFromDigestAlgorithm(newData.samlConfig.digestAlgorithm)
        form.SAMLResponseSign = getSAMLResponseSignFromSignatureAlgorithm(newData.samlConfig.signatureAlgorithm)
        form.SAMLExpiration = newData.samlConfig.lifetimeInSeconds || 3600
        form.isSAMLResponseSign = newData.samlConfig.signResponse || false
        form.isSAMLRequestSign = newData.samlConfig.verifyRequest || false

        // 设置 ACS 地址
        acsAddressList.value =
          newData.samlConfig._acs?.map(acs => ({
            responseMode: acs.binding === 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST' ? 'HTTP-POST' : 'HTTP-REDIRECT',
            address: acs.url,
          })) || []

        // 设置 SLO 配置
        if (newData.samlConfig.sloConfig) {
          form.SAMLLogoutWay = newData.samlConfig.sloConfig.binding === 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect' ? 'HTTP-REDIRECT' : 'HTTP-POST'
          form.SAMLLogout = newData.samlConfig.sloConfig.url || ''
        }

        // 设置自定义属性
        customSAMLResponse.value =
          newData.samlConfig.customAttributes?.map(attr => ({
            label: attr.name,
            way: 'Basic',
            val: attr.value,
          })) || []
      }

      // CAS 配置回填
      if (newData.casConfig) {
        form.openCAS = true
        form.serviceTicketAt = newData.casConfig.stLifetime || 300
        form.CASUserId = newData.casConfig.casUserIdentifier || ''

        // 设置自定义属性
        customCASList.value = Object.entries(newData.casConfig.customAttributes || {}).map(([key, value]) => ({
          label: key,
          value: value as string,
        }))
      }
    }
  },
  { immediate: true, deep: true },
)
// 添加辅助函数
const convertFromSeconds = (seconds: number): { value: number; unit: string } => {
  if (seconds % 86400 === 0) return { value: seconds / 86400, unit: '4' }
  if (seconds % 3600 === 0) return { value: seconds / 3600, unit: '3' }
  if (seconds % 60 === 0) return { value: seconds / 60, unit: '2' }
  return { value: seconds, unit: '1' }
}
const getSAMLResponseFromDigestAlgorithm = (algorithm: string): string => {
  switch (algorithm) {
    case 'http://www.w3.org/2000/09/xmldsig#sha1':
      return 'SHA1'
    case 'http://www.w3.org/2001/04/xmlenc#sha256':
      return 'SHA256'
    case 'http://www.w3.org/2001/04/xmlenc#sha512':
      return 'SHA512'
    default:
      return 'SHA1'
  }
}

const getSAMLResponseSignFromSignatureAlgorithm = (algorithm: string): string => {
  switch (algorithm) {
    case 'http://www.w3.org/2000/09/xmldsig#rsa-sha1':
      return 'RSA SHA1'
    case 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256':
      return 'RSA SHA256'
    case 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha512':
      return 'RSA SHA512'
    default:
      return 'RSA SHA1'
  }
}
const timeUnit = ref([
  {
    label: '秒',
    val: '1',
  },
  {
    label: '分钟',
    val: '2',
  },
  {
    label: '小时',
    val: '3',
  },
  {
    label: '天',
    val: '4',
  },
])
const encryptionAlgorithm = ref([
  {
    label: 'A128CBC-HS256',
    val: 'A128CBC-HS256',
  },
  {
    label: 'A128GCM',
    val: 'A128GCM',
  },
  {
    label: 'A256CBC-HS512',
    val: 'A256CBC-HS512',
  },
  {
    label: 'A256GCM',
    val: 'A256GCM',
  },
])
const secretList = ref([
  {
    label: 'RSA-OAEP',
    val: 'RSA-OAEP',
  },
  {
    label: 'ECDH-ES',
    val: 'ECDH-ES',
  },
])
const rules = {}
const changeTag = ref(true)
const formChange = () => {
  changeTag.value = false
}
const nameIdFormatList = ref([
  {
    label: 'unspecified',
    val: 'unspecified',
  },
  {
    label: 'transient',
    val: 'transient',
  },
  {
    label: 'persistent',
    val: 'persistent',
  },
  {
    label: 'emailAddress',
    val: 'emailAddress',
  },
  {
    label: 'X509SubjectName',
    val: 'X509SubjectName',
  },
  {
    label: 'WindowsDomainQualifiedName',
    val: 'WindowsDomainQualifiedName',
  },
  {
    label: 'kerberos',
    val: 'kerberos',
  },
  {
    label: 'entity',
    val: 'entity',
  },
])
const acsAddressList = ref([
  {
    responseMode: 'HTTP-POST',
    address: '',
  },
])
const responseModeList = ref([
  {
    label: 'HTTP-POST',
    val: 'HTTP-POST',
  },
])
const delACSItem = index => {
  acsAddressList.value.splice(index, 1)
}
const addACSItem = () => {
  acsAddressList.value.push({
    responseMode: 'HTTP-POST',
    address: '',
  })
}
const SAMLResponse = ref([
  {
    label: 'SHA1',
    val: 'SHA1',
  },
  {
    label: 'SHA256',
    val: 'SHA256',
  },
  {
    label: 'SHA512',
    val: 'SHA512',
  },
])
const SAMLResponseSign = ref([
  {
    label: 'RSA SHA1',
    val: 'RSA SHA1',
  },
  {
    label: 'RSA SHA256',
    val: 'RSA SHA256',
  },
  {
    label: 'RSA SHA512',
    val: 'RSA SHA512',
  },
])
const SAMLLogoutList = ref([
  {
    label: 'HTTP-REDIRECT',
    val: 'HTTP-REDIRECT',
  },
  {
    label: 'HTTP-POST',
    val: 'HTTP-POST',
  },
])
const customSAMLResponse = ref<any[]>([])
const customSAMLResponseList = ref([
  {
    label: 'Basic',
    val: 'Basic',
  },
  {
    label: 'Uri',
    val: 'Uri',
  },
])
const delSAMLItem = index => {
  customSAMLResponse.value.splice(index, 1)
}
const addSAMLItem = () => {
  customSAMLResponse.value.push({
    label: '',
    way: 'Basic',
    val: '',
  })
}

const customCASList = ref<{ label: string; value: string }[]>([])
const addCASItem = () => {
  customCASList.value.push({
    label: '',
    value: '',
  })
}
const delCASItem = (index: number) => {
  customCASList.value.splice(index, 1)
}

const options = ref([
  {
    label: '用户基础字段',
    options: [
      {
        label: '用户 ID',
        value: 'id',
      },
      {
        label: '姓名',
        value: 'name',
      },
      {
        label: '用户名',
        value: 'username',
      },
      {
        label: '账号状态',
        value: 'status',
      },
      {
        label: '工作状态',
        value: 'workStatus',
      },
      {
        label: '昵称',
        value: 'nickname',
      },
      {
        label: '性别',
        value: 'gender',
      },
      {
        label: '生日',
        value: 'birthdate',
      },
      {
        label: '手机号',
        value: 'phone',
      },
      {
        label: '邮箱',
        value: 'email',
      },
      {
        label: '居民身份证号',
        value: 'identityNumber',
      },
      {
        label: '名',
        value: 'givenName',
      },
      {
        label: '姓',
        value: 'familyName',
      },
      {
        label: '中间名',
        value: 'middleName',
      },
      {
        label: '曾用名',
        value: 'preferredUsername',
      },
      {
        label: '个人主页',
        value: 'profile',
      },
      {
        label: '国家',
        value: 'country',
      },
      {
        label: '时区',
        value: 'zoneinfo',
      },
      {
        label: '个人网站',
        value: 'website',
      },
      {
        label: '住址',
        value: 'address',
      },
      {
        label: '公司',
        value: 'company',
      },
      {
        label: '城市',
        value: 'city',
      },
      {
        label: '省份',
        value: 'province',
      },
      {
        label: '街道地址',
        value: 'streetAddress',
      },
      {
        label: '原系统ID',
        value: 'externalId',
      },
      {
        label: '邮政编码',
        value: 'postalCode',
      },
      {
        label: '完整的邮寄地址',
        value: 'formatted',
      },
      {
        label: '注册时间',
        value: 'signedUp',
      },
      {
        label: '地区',
        value: 'locale',
      },
      {
        label: '最后登录时间',
        value: 'lastLogin',
      },
      {
        label: '登录次数',
        value: 'loginsCount',
      },
      {
        label: '最后登录应用',
        value: 'lastLoginApp',
      },
      {
        label: '用户来源',
        value: 'userSource',
      },
      {
        label: '所属部门',
        value: 'departmentName',
      },
      {
        label: '岗位',
        value: 'post',
      },
      {
        label: '用户类型',
        value: 'usertype',
      },
      {
        label: '手机号是否验证',
        value: 'phoneVerified',
      },
      {
        label: '邮箱是否验证',
        value: 'emailVerified',
      },
      {
        label: '手机号是否验证',
        value: 'phoneVerified',
      },
      {
        label: '图片',
        value: 'picture',
      },
      {
        label: '签发者规定的用户唯一标识(用户 ID)',
        value: 'sub',
      },
      {
        label: '用户池 ID',
        value: 'userpoolId',
      },
      {
        label: '更新时间',
        value: 'updatedAt',
      },
      {
        label: '手机号国际区号',
        value: 'phoneCountryCode',
      },
    ],
  },
  {
    label: '用户扩展字段',
    options: [],
  },
])
const claimList = ref([
  {
    name: 'sub',
    value: 'id',
    disabled: true,
    tips: false,
  },
  {
    name: 'name',
    value: 'name',
    disabled: true,
    tips: false,
  },
  {
    name: 'username',
    value: 'username',
    disabled: true,
    tips: false,
  },
  {
    name: 'email',
    value: 'email',
    disabled: true,
    tips: false,
  },
])
const addClaimItem = () => {
  claimList.value.push({
    name: '',
    disabled: false,
    value: '',
    tips: false,
  })
}
const delClaimItem = (index: number) => {
  claimList.value.splice(index, 1)
}
const claimNameInput = claimItem => {
  const index = claimList.value.findIndex(item => item.name === claimItem.name)
  if (claimList.value.length - 1 !== index) {
    claimItem.tips = true
  } else {
    claimItem.tips = false
  }
}

const scopeOpts = ref([
  {
    label: '用户 ID',
    value: 'id',
  },
  {
    label: '姓名',
    value: 'name',
  },
  {
    label: '用户名',
    value: 'username',
  },
  {
    label: '账号状态',
    value: 'status',
  },
  {
    label: '工作状态',
    value: 'workStatus',
  },
  {
    label: '昵称',
    value: 'nickname',
  },
  {
    label: '性别',
    value: 'gender',
  },
  {
    label: '生日',
    value: 'birthdate',
  },
  {
    label: '手机号',
    value: 'phone',
  },
  {
    label: '邮箱',
    value: 'email',
  },
  {
    label: '居民身份证号',
    value: 'identityNumber',
  },
  {
    label: '名',
    value: 'givenName',
  },
  {
    label: '姓',
    value: 'familyName',
  },
  {
    label: '中间名',
    value: 'middleName',
  },
  {
    label: '曾用名',
    value: 'preferredUsername',
  },
  {
    label: '个人主页',
    value: 'profile',
  },
  {
    label: '国家',
    value: 'country',
  },
  {
    label: '时区',
    value: 'zoneinfo',
  },
  {
    label: '个人网站',
    value: 'website',
  },
  {
    label: '住址',
    value: 'address',
  },
  {
    label: '公司',
    value: 'company',
  },
  {
    label: '城市',
    value: 'city',
  },
  {
    label: '省份',
    value: 'province',
  },
  {
    label: '街道地址',
    value: 'streetAddress',
  },
  {
    label: '原系统ID',
    value: 'externalId',
  },
  {
    label: '邮政编码',
    value: 'postalCode',
  },
  {
    label: '完整的邮寄地址',
    value: 'formatted',
  },
  {
    label: '注册时间',
    value: 'signedUp',
  },
  {
    label: '地区',
    value: 'locale',
  },
  {
    label: '最后登录时间',
    value: 'lastLogin',
  },
  {
    label: '登录次数',
    value: 'loginsCount',
  },
  {
    label: '最后登录应用',
    value: 'lastLoginApp',
  },
  {
    label: '用户来源',
    value: 'userSource',
  },
  {
    label: '所属部门',
    value: 'departmentName',
  },
  {
    label: '岗位',
    value: 'post',
  },
  {
    label: '用户类型',
    value: 'usertype',
  },
  {
    label: '手机号是否验证',
    value: 'phoneVerified',
  },
  {
    label: '邮箱是否验证',
    value: 'emailVerified',
  },
  {
    label: '手机号是否验证',
    value: 'phoneVerified',
  },
  {
    label: '图片',
    value: 'picture',
  },
  {
    label: '签发者规定的用户唯一标识(用户 ID)',
    value: 'sub',
  },
  {
    label: '用户池 ID',
    value: 'userpoolId',
  },
  {
    label: '更新时间',
    value: 'updatedAt',
  },
  {
    label: '手机号国际区号',
    value: 'phoneCountryCode',
  },
])
const scopeList = ref([
  {
    label: 'offline_access',
    value: [],
    disabled: true,
    tips: false,
  },
  {
    label: 'openid',
    value: ['sub'],
    disabled: true,
    tips: false,
  },
  {
    label: 'username',
    value: ['username'],
    disabled: true,
    tips: false,
  },
  {
    label: 'address',
    value: ['address'],
    disabled: true,
    tips: false,
  },
])
const addScopeItem = () => {
  scopeList.value.push({
    label: '',
    value: [],
    disabled: false,
    tips: false,
  })
}
const delScopeItem = (index: number) => {
  scopeList.value.splice(index, 1)
}
const scopeNameInput = scopeItem => {
  const index = scopeList.value.findIndex(item => item.label === scopeItem.label)
  if (scopeList.value.length - 1 !== index) {
    scopeItem.tips = true
  } else {
    scopeItem.tips = false
  }
}

const onSubmit = async () => {
  try {
    let data = {}
    let endpoint = ''

    switch (form.protocolConfig) {
      case 'OIDC':
        data = getOIDCData()
        endpoint = `/iam/applications/${appCode.value}`
        break
      case 'OAuth 2.0':
        data = getOAuth2Data()
        endpoint = `/iam/applications/${appCode.value}`
        break
      case 'SAML2':
        data = getSAML2Data()
        endpoint = `/iam/applications/${appCode.value}`
        break
      case 'CAS':
        data = getCASData()
        endpoint = `/iam/applications/${appCode.value}`
        break
      default:
        throw new Error('Unsupported protocol')
    }

    const response = await http.put(endpoint, data)

    if (response.status === 200) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    console.error('保存出错:', error)
    ElMessage.error('保存出错，请稍后重试')
  }
}
interface OidcJWEConfig {
  enabled: boolean
  id_token_enc_alg?: string
  id_token_enc_method?: string
  id_token_enc_key?: string
}
// Helper functions to get data for each protocol
const getOIDCData = () => {
  const oidcConfig = {
    grant_types: form.licensingMode,
    response_types: form.backType,
    id_token_signed_response_alg: form.signature,
    rotate_refresh_token: form.unknown.includes('启用 refresh_token 轮换'),
    refresh_duration_when_rotate_refresh_token: form.unknown.includes('轮换 refresh_token 时，同时刷新 refresh_token 有效时间'),
    skip_implicit_flow_rules: form.unknown.includes('不强制 implicit 模式回调链接为 https'),
    display_prompt_page: form.unknown.includes('用户知情同意页面'),
    token_endpoint_auth_method: form.exchangeVerificationMethod,
    introspection_endpoint_auth_method: form.inspectionVerificationMethod,
    revocation_endpoint_auth_method: form.withdrawalVerificationMethod,
    authorization_code_expire: convertToSeconds(form.authorizationCode, form.authorizationCodeUnit),
    id_token_expire: convertToSeconds(form.idToken, form.idTokenUnit),
    access_token_expire: convertToSeconds(form.accessToken, form.accessTokenUnit),
    refresh_token_expire: convertToSeconds(form.refreshToken, form.refreshTokenUnit),
  }

  const oidcJWEConfig: OidcJWEConfig = {
    enabled: form.unknown.includes('启用 id_token 加密'),
  }

  if (oidcJWEConfig.enabled) {
    oidcJWEConfig.id_token_enc_alg = form.idTokenEncryptionAlgorithm
    oidcJWEConfig.id_token_enc_method = form.idTokenSecret
    oidcJWEConfig.id_token_enc_key = form.idTokenPublic
  }

  return {
    protocolType: form.protocolType,
    protocolConfig: form.protocolConfig,
    oidcConfig,
    oidcJWEConfig,
  }
}

// 辅助函数：将时间单位转换为秒
const convertToSeconds = (value: number, unit: string): number => {
  switch (unit) {
    case '1': // 秒
      return value
    case '2': // 分钟
      return value * 60
    case '3': // 小时
      return value * 3600
    case '4': // 天
      return value * 86400
    default:
      return value
  }
}
const getOAuth2Data = () => {
  return {
    protocolConfig: form.protocolConfig,
    oauthProviderEnabled: form.openOAuth2,
    oauthConfig: {
      grants: form.licensingMmode,
      introspection_endpoint_auth_method: form.tokenAuthentication,
      revocation_endpoint_auth_method: form.withdrawTokenAuthentication,
    },
  }
}
const getSAML2Data = () => {
  return {
    protocolConfig: form.protocolConfig,
    samlProviderEnabled: form.openSAML2,
    samlConfig: {
      nameIdentifierFormat: form.nameIdFormat,
      _acs: acsAddressList.value.map((acs, index) => ({
        index,
        url: acs.address,
        binding: acs.responseMode === 'HTTP-POST' ? 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST' : 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect',
      })),
      digestAlgorithm:
        form.SAMLResponse === 'SHA1'
          ? 'http://www.w3.org/2000/09/xmldsig#sha1'
          : form.SAMLResponse === 'SHA256'
          ? 'http://www.w3.org/2001/04/xmlenc#sha256'
          : 'http://www.w3.org/2001/04/xmlenc#sha512',
      signatureAlgorithm:
        form.SAMLResponseSign === 'RSA SHA1'
          ? 'http://www.w3.org/2000/09/xmldsig#rsa-sha1'
          : form.SAMLResponseSign === 'RSA SHA256'
          ? 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256'
          : 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha512',
      lifetimeInSeconds: Number(form.SAMLExpiration),
      signResponse: form.isSAMLResponseSign,
      verifyRequest: form.isSAMLRequestSign,
      sloConfig: {
        binding: form.SAMLLogoutWay === 'HTTP-REDIRECT' ? 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect' : 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST',
        url: form.SAMLLogout,
      },
      customAttributes: customSAMLResponse.value.map(attr => ({
        nameFormat: 'urn:oasis:names:tc:SAML:2.0:attrname-format:basic',
        name: attr.label,
        value: attr.val,
      })),
    },
  }
}

const getCASData = () => {
  return {
    protocolConfig: form.protocolConfig,
    casProviderEnabled: form.openCAS,
    casConfig: {
      stLifetime: Number(form.serviceTicketAt),
      casUserIdentifier: form.CASUserId,
      customAttributes: Object.fromEntries(customCASList.value.map(item => [item.label, item.value])),
    },
    cas_stLifetime_66decd3c177d60fd57f6fb32: convertToTimeUnit(form.serviceTicketAt, form.authorizationCodeUnit),
  }
}
const convertToTimeUnit = (value: number, unit: string): string => {
  switch (unit) {
    case '1': // 秒
      return 'Second'
    case '2': // 分钟
      return 'Minute'
    case '3': // 小时
      return 'Hour'
    case '4': // 天
      return 'Day'
    default:
      return 'Second'
  }
}

const changeProtocolType = async (val: string) => {
  try {
    const res = await http.put(`/iam/applications/${appCode.value}`, {
      protocol: val,
    })
    ElMessage.success('更新成功')
  } catch (error) {
    ElMessage.error('更新失败' + error)
  }
}

onMounted(() => {})
</script>

<style lang="scss" scoped>

.section-card {
  margin-bottom: 26px;
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin-bottom: 16px;
}

.section-desc {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
}

.link-text {
  color: #007ee5;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

.claim-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.scope-container {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.scope-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.scope-collapse {
  :deep(.el-collapse-item__header) {
    font-size: 16px;
    color: #000;
  }
  
  :deep(.el-collapse-item__content) {
    padding: 20px 0;
  }
}

.error-text {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 4px;
}

.add-btn {
  font-size: 14px;
  color: #007ee5;

  &:hover {
    color: #007ee5;
  }
}

.delete-btn {
  &.hide {
    opacity: 0;
    position: relative;
    z-index: -1;
  }
}

.scope-item {
  margin-bottom: 16px;
  border-radius: 8px;
  padding: 16px 16px 16px 20px;
  position: relative;
  background-color: #fff;
  overflow: hidden;

  .line {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background-color: #007ee5;
    width: 4px;
  }

  .el-input {
    width: 280px;
  }

  .label {
    padding-left: 40px;
    padding-right: 10px;
  }

  .el-select {
    .el-select__wrapper {
      box-shadow: none;
      background-color: transparent;
    }
  }
}

.el-checkbox,
.el-radio {
  margin-right: 0;
}
</style>
