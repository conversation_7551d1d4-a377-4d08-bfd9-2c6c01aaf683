<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <!-- 标题区域可以根据需要添加 -->
    </div>
    <div class="module-con">
      <!-- 标签导航区域 -->
      <div class="tab-header-container">
        <div class="tab-nav">
          <div class="tab-item" 
               :class="{ active: activeName === 'application' }"
               @click="activeName = 'application'">
            {{ $t('Iam.frontend.tabs.application') }}
          </div>
          <div class="tab-item" 
               :class="{ active: activeName === 'protocol' }"
               @click="activeName = 'protocol'">
            {{ $t('Iam.frontend.tabs.protocol') }}
          </div>
          <div class="tab-item" 
               :class="{ active: activeName === 'loginControl' }"
               @click="activeName = 'loginControl'">
            {{ $t('Iam.frontend.tabs.loginControl') }}
          </div>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div class="tab-content-box">
        <!-- 应用程序内容 -->
        <div v-if="activeName === 'application'" class="tab-content-box-scroll scroll-bar-custom">
          <Application :data="applicationData" />
        </div>
        
        <!-- 协议内容 -->
        <div v-if="activeName === 'protocol'" class="tab-content-box-scroll scroll-bar-custom">
          <Protocol :data="protocolData" />
        </div>
        
        <!-- 登录控制内容 -->
        <div v-if="activeName === 'loginControl'" class="tab-content-box-scroll scroll-bar-custom">
          <LoginCtrl :data="loginCtrlData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Application from './components/Application.vue'
import Protocol from './components/Protocol.vue'
import LoginCtrl from './components/LoginCtrl.vue'
import http from '/admin/support/http'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const applicationData = ref({})
const protocolData = ref({})
const loginCtrlData = ref({})
const ConfigListRefs = ref()
const activeName = ref('application')
const appCode = ref(localStorage.getItem('app_code') || 'admin')
const getApplicationInfo = async () => {
  const res = await http.get(`/iam/applications/${appCode.value}`)
  if (res.data.code === 200) {
    applicationData.value = {
      name: res.data.data[0].name,
      desc: res.data.data[0].description,
      logo: res.data.data[0].logo,
      visibility: res.data.data[0].applicationInvisibilityType,
      certifiedAddress: res.data.data[0].app_id,
      loginUrl: res.data.data[0].initLoginUrl || '',
      logoutUrl: res.data.data[0].logoutRedirectUris[0] || '',
      initiateLoginUrl: res.data.data[0].redirectUris[0] || '',
      appSecret: res.data.data[0].app_secret,
      identifier: res.data.data[0].identifier,
    }

    protocolData.value = {
      protocolType: res.data.data[0].protocol || 'OIDC',
      oidcProviderEnabled: res.data.data[0].oidcProviderEnabled,
      oauthProviderEnabled: res.data.data[0].oauthProviderEnabled,
      samlProviderEnabled: res.data.data[0].samlProviderEnabled,
      casProviderEnabled: res.data.data[0].casProviderEnabled,
      tokenExpiresAfter: res.data.data[0].tokenExpiresAfter,
      oidcConfig: res.data.data[0].oidcConfig,
      oauthConfig: res.data.data[0].oauthConfig,
      samlConfig: res.data.data[0].samlConfig,
      casConfig: res.data.data[0].casConfig,
      jwks: res.data.data[0].jwks,
    }

    loginCtrlData.value = {
      loginWay: res.data.data[0].loginTabs,
      defaultLoginTab: res.data.data[0].defaultLoginTab,
      registerTabs: res.data.data[0].registerTabs,
      defaultRegisterTab: res.data.data[0].defaultRegisterTab,
      loginRequireEmailVerified: res.data.data[0].loginRequireEmailVerified,
      agreementEnabled: res.data.data[0].agreementEnabled,
      ssoEnabled: res.data.data[0].ssoEnabled,
      mfaType: res.data.data[0].mfaType,
      loginFailStrategy: res.data.data[0].loginFailStrategy,
      loginSmsConfig: res.data.data[0].loginSmsConfig,
      registerSmsConfig: res.data.data[0].registerSmsConfig,
      accountLock: res.data.data[0].accountLock,
      emailVerifiedDefault: res.data.data[0].emailVerifiedDefault,
      sendWelcomeEmail: res.data.data[0].sendWelcomeEmail,
      autoRegisterThenLogin: res.data.data[0].autoRegisterThenLogin,
      enableCompletePassword: res.data.data[0].enableCompletePassword,
      passwordStrength: res.data.data[0].passwordStrength,
      verifyCodeLength: res.data.data[0].verifyCodeLength,
      verifyCodeMaxAttempts: res.data.data[0].verifyCodeMaxAttempts,
    }
  }
}

onMounted(() => {
  getApplicationInfo()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.module-header {
  padding: 0 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-con {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  .tab-header-container {
    margin-bottom: 20px;
    
    .tab-nav {
      display: flex;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
      overflow: hidden;
      border: 1px solid #f0f0f0;
      width: fit-content;
      
      .tab-item {
        padding: 12px 40px;
        font-size: 18px;
        color: #000;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: #f5f7fa;
        }
        
        &.active {
          background: #032f6e;
          color: #fff;
        }
      }
    }
  }
  
  .tab-content-box {
    flex: 1;
    overflow: hidden;
  }
  
  .tab-content-box-scroll {
    height: 100%;
    &::-webkit-scrollbar-thumb {
      background-color: transparent;
      
      &:hover {
        background-color: transparent;
      }
    }
  }
}

</style>
