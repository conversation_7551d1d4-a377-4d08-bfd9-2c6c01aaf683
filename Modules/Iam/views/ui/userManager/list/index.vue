<template>
  <div class="table-page bwms-module" v-loading="loading">
    <div class="module-header">
     
      <div class="btn-list">
        <el-button @click="dialogFilter = true">
          <el-icon>
            <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
          </el-icon>
          <span>{{ $t('Iam.routes.userManager.list.filter') }}</span>
        </el-button>
        <el-button type="primary" @click="createHandle">
          <el-icon>
            <Plus />
          </el-icon>
          <span>{{ $t('Iam.routes.userManager.list.createUser') }}</span>
        </el-button>
      </div>
    </div>
    <div class="module-con">
      <div class="box scroll-bar-custom" v-if="list.length">
        <el-table :data="list" @selection-change="checkedHandle" style="width: 100%; height: 100%">
          <template #empty>
              <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column type="selection" />
          <el-table-column prop="username" :label="$t('Iam.routes.userManager.list.username')" />
          <el-table-column prop="phone" :label="$t('Iam.routes.userManager.list.phone')" />
          <el-table-column prop="email" :label="$t('Iam.routes.userManager.list.email')" />
          <el-table-column prop="lastLogin" :label="$t('Iam.routes.userManager.list.lastLoginTime')" />
          <el-table-column prop="status" :label="$t('Iam.status')" />
          <el-table-column prop="createdAt" :label="$t('Iam.routes.userManager.list.createdAt')" />
          <el-table-column :label="$t('Iam.operations')" width="150">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <el-button class="bwms-operate-btn" @click="upHandle(scope.row)">
                  <el-icon>
                    <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                  </el-icon>
                </el-button>
                <el-button class="bwms-operate-btn" @click="delHandle(scope.row)">
                  <el-icon>
                    <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                  </el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <el-empty class="mt-36" v-else>
        <div class="mb-2 text-2xl font-semibold">{{ $t('Iam.routes.userManager.list.title') }}</div>
        <div class="mb-5 text-sm">{{ $t('Iam.routes.userManager.list.emptyDescription') }}</div>
        <el-button color="#007ee5" @click="createHandle">{{ $t('Iam.routes.userManager.list.createUser') }}</el-button>
      </el-empty>

      <!-- 分页器 -->
      <div class="box-footer" v-if="list.length">
          <div class="table-pagination-style">
            <div class="pagination-left">
              <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
              <el-select
                v-model="limit"
                class="page-size-select"
                @change="changePage"
              >
                <el-option
                  v-for="size in [10, 20, 50, 100]"
                  :key="size"
                  :label="size"
                  :value="size"
                  class="page-size-option"
                />
                <template #empty>
                  <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                    {{ t('Cms.list.no_data') }}
                  </div>
                </template>
              </el-select>
              <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
            </div>
            <div class="pagination-right">
              <el-pagination
                v-model:current-page="page"
                background
                layout="prev, pager, next"
                :page-size="limit"
                :total="total"
                @current-change="changePage"
              />
            </div>
          </div>
      </div>
    </div>

    <CreateComp ref="CreateCompRefs" :getList="getList" />

    <el-dialog class="el-dialog-common-cls" v-model="dialogDel" :title="$t('Iam.routes.userManager.list.confirmOperation')" width="500">
      <div>{{ $t('Iam.routes.userManager.list.confirmDelete') }}</div>
      <template #footer>
        <div class="flex justify-center">
          <el-button @click="dialogDel = false" size="large">{{ $t('Iam.cancel') }}</el-button>
          <el-button type="primary" @click="delConfirmHandle" size="large">{{ $t('Iam.confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog class="el-dialog-common-cls" v-model="dialogFilter" :title="$t('Iam.routes.userManager.list.filterConditions')" width="1000">
      <div class="flex items-center mb-4" v-for="(item, index) in filter" :key="index">
        <el-select size="large" v-model="item.field" :placeholder="$t('Iam.routes.userManager.list.selectAttribute')" class="flex-1 pr-2">
          <el-option v-for="attr in attributeOptions" :key="attr.value" :label="attr.label" :value="attr.value" />
        </el-select>
        <el-select size="large" v-model="item.operator" :placeholder="$t('Iam.routes.userManager.list.selectRelation')" class="flex-1 pr-2">
          <el-option v-for="relation in relationOptions" :key="relation.value" :label="$t(`Iam.routes.userManager.list.relations.${relation.key}`)" :value="relation.value" />
        </el-select>
        <el-input size="large" v-model="item.value" :placeholder="$t('Iam.routes.userManager.list.inputAttributeValue')" class="flex-1 pr-2" />
      </div>
      <el-button type="primary" plain @click="addRuleHandle" class="mt-4">{{ $t('Iam.routes.userManager.list.addRule') }}</el-button>
      <template #footer>
        <div class="flex justify-center">
          <el-button @click="cancelHandle" size="large">{{ $t('Iam.cancel') }}</el-button>
          <el-button type="primary" @click="getList" size="large">{{ $t('Iam.confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, ComponentPublicInstance, reactive } from 'vue'
import { useAppStore } from '/admin/stores/modules/app'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

import { UserInfo } from '../domain/UserInfo'
import UserInfoService from '../application/UserInfoService'
import UserInfoRepositoryImpl from '../infrastructure/UserInfoRepositoryImpl'

import CreateComp from './components/Create.vue'

const api = 'iam'
const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const { t } = useI18n()
const pageName = ref(appStore.getPageName)
const UserInfoInstance = new UserInfoService(new UserInfoRepositoryImpl(api))

// 列表
const list = ref<UserInfo[]>([])
const checkList = ref<UserInfo[]>([])
const dialogDel = ref(false)
const dialogFilter = ref(false)
const loading = ref(true)
const CreateCompRefs = ref<ComponentPublicInstance>()
const checkedHandle = (items: UserInfo[]) => {
  checkList.value = items
}
// 增
const createHandle = () => {
  if (CreateCompRefs.value && 'dialog' in CreateCompRefs.value) {
    CreateCompRefs.value.dialog = true
  }
}
// 删
const delHandle = (item: UserInfo) => {
  dialogDel.value = true
  checkList.value.push(item)
}
// 删 (确认)
const delConfirmHandle = () => {
  UserInfoInstance.del(checkList.value.map(item => item.id)).finally(() => {
    getList()
    dialogDel.value = false
    checkList.value = []
  })
}
// 改
const upHandle = (item: UserInfo) => {
  router.push({
    name: 'iamUserManagerListForm',
    params: {
      id: item.id,
    },
  })
}
// 查
const filter = ref([{ field: '', operator: '', value: '' }])
const attributeOptions = reactive([
  { label: t('Iam.routes.userManager.list.attributes.userId'), value: 'id' },
  { label: t('Iam.routes.userManager.list.attributes.name'), value: 'name' },
  { label: t('Iam.routes.userManager.list.attributes.username'), value: 'username' },
  { label: t('Iam.routes.userManager.list.attributes.status'), value: 'status' },
  { label: t('Iam.routes.userManager.list.attributes.workStatus'), value: 'work_status' },
  { label: t('Iam.routes.userManager.list.attributes.gender'), value: 'gender' },
  { label: t('Iam.routes.userManager.list.attributes.phone'), value: 'phone' },
  { label: t('Iam.routes.userManager.list.attributes.email'), value: 'email' },
])
// 关系
const relationOptions = reactive([
  {
    key: 'equal',
    value: 'eq',
  },
  {
    key: 'notEqual',
    value: 'ne',
  },
  {
    key: 'contains',
    value: 'contains',
  },
])
const addRuleHandle = () => {
  filter.value.push({ field: '', operator: '', value: '' })
}
function getList() {
  loading.value = true
  const data = {
    filter: filter.value[0].field ? filter.value : [{}],
    page: page.value,
    limit: limit.value,
    options: {
      withCustomData: '',
      withLastLoginApp: '',
      withUserSource: '',
      withDepartments: '',
      withPost: '',
      withMetadata: '',
    },
  }
  UserInfoInstance.get(data)
    .then(res => {
      list.value = res.list
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
      dialogFilter.value = false
    })
}
const cancelHandle = () => {
  filter.value = [{ field: '', operator: '', value: '' }]
  getList()
}

// 分页器
const page = ref(1)
const limit = ref(15)
const total = ref(0)
const changePage = () => {
  getList()
}

watch(
  () => route.path,
  () => {
    pageName.value = appStore.getPageName
  },
)
onMounted(() => {
  pageName.value = appStore.getPageName
  getList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .box {
      padding-top: 20px;
    }
  }
}





:deep(.el-loading-mask) {
  --el-mask-color: transparent;
}
</style>
