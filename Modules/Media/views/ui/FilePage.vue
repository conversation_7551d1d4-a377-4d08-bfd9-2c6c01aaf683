<template>
  <div class="table-page bwms-module">
    <div class="module-header"></div>

    <div class="module-con">
      <div class="box">
        <div class="view-tabs">
          <el-tabs v-model="activeView">
            <el-tab-pane :label="$t('Media.FilePage.listView')" name="list">
              <FileManagePage 
                :BaseUrl="BaseUrl" 
                :token="token" 
                :locale="lang"
                view-mode="list"
              />
            </el-tab-pane>
            <el-tab-pane :label="$t('Media.FilePage.gridView')" name="grid">
              <gridList 
               @returnTab="handleReturnTab"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { FileManagePage} from 'filestudio-bingo'
import { ref, computed, onMounted, watchEffect, onActivated } from 'vue'
import { env, getAuthToken } from '/admin/support/helper'
import { useI18n } from 'vue-i18n'
import gridList from './mediaGridPage/gridList.vue'

const { t } = useI18n()

let tokens: string | null = getAuthToken()
const token = ref<string>(tokens ?? '')
const BaseUrl = ref<string>(env('VITE_BASE_URL').replace('/admin/', '/'))
const lang = computed(() => localStorage.getItem('bwms_language') ?? 'zh_HK')

// 当前激活的视图模式
const activeView = ref('list')

// 监听localStorage中的tab变化
watchEffect(() => {
  const savedTab = localStorage.getItem('media_active_tab')
  if (savedTab && ['list', 'grid'].includes(savedTab)) {
    activeView.value = savedTab
    // 使用后立即清除，避免影响其他操作
    localStorage.removeItem('media_active_tab')
  }
})

// 当组件被激活时也检查localStorage
onActivated(() => {
  const savedTab = localStorage.getItem('media_active_tab')
  if (savedTab && ['list', 'grid'].includes(savedTab)) {
    activeView.value = savedTab
    localStorage.removeItem('media_active_tab')
  }
})

const handleReturnTab = (tab: string) => {
  if (tab && ['list', 'grid'].includes(tab)) {
    activeView.value = tab
  }
}
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 0;
      .view-tabs {
        height: 100%;
      }
    }
  }
}
</style>
