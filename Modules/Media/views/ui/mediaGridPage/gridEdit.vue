<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    </div>
    <!-- 内容区域 -->
    <div class="module-con scroll-bar-custom">
      <div class="page-container">
        <!-- 左侧内容：图像预览和图片描述 -->
        <div class="left-section">
          <!-- 图像预览区 -->
          <div class="preview-container">
            <div class="preview-header">
              <div class="title">{{ t('Media.GridEdit.image_preview') }}</div>
              <div class="actions">
                <div class="action-item" @click="handlePreview"><el-icon><ZoomIn /></el-icon></div>
              </div>
            </div>
            <div class="image-preview" @click="handlePreview">
              <img :src="formData.url" :alt="t('Media.GridEdit.image_preview')">
            </div>
          </div>

          <!-- 图片预览弹窗 -->
          <el-image-viewer
            v-if="previewVisible"
            :url-list="[formData.url]"
            :initial-index="0"
            :hide-on-click-modal="true"
            @close="previewVisible = false"
            class="custom-image-viewer"
            :zoom-rate="1.2"
          />

          <!-- 图片描述区 -->
          <div class="description-container">
            <div class="description-header">
              <div class="title">{{ t('Media.GridEdit.image_description') }}</div>
              <div class="actions">
                <el-button type="primary" class="ai-button" @click="handleAiGenerate" :loading="aiLoading" 
                :disabled="refreshingAlt || refreshingDetail || refreshingTags">
                  <el-icon><Place /></el-icon>
                  {{ t('Media.GridEdit.ai_regenerate') }}
                </el-button>
                <el-button class="manage-button" @click="openTagsDialog">
                  {{ t('Media.GridEdit.batch_manage_tags') }}
                </el-button>
              </div>
            </div>

            <div class="description-content">
              <!-- Alt Text区域 -->
              <div class="alt-text-section">
                <div class="section-label">{{ t('Media.GridEdit.alt_text') }}</div>
                <el-input 
                  v-model="formData.alt_text" 
                  type="textarea" 
                  :rows="2"
                  :placeholder="t('Media.GridEdit.enter_short_desc')"
                />
                <div class="char-count">{{ formData.alt_text.length }} {{ t('Media.GridEdit.characters') }}</div>
                
                <!-- 替代文字建议区域 -->
                <div class="alt-suggestions">
                  <div class="suggestions-header">
                    <div class="suggestions-title">{{ t('Media.GridEdit.alt_suggestions') }}</div>
                    <el-button 
                      type="primary"
                      link
                      class="refresh-btn" 
                      @click="refreshAltSuggestions" 
                      :loading="refreshingAlt"
                      :disabled="aiLoading"
                    >
                      {{ t('Media.GridEdit.refresh_suggestions') }}
                    </el-button>
                  </div>
                  <div class="suggestions-list">
                    <div 
                      v-for="(suggestion, index) in altSuggestions" 
                      :key="index"
                      class="suggestion-item"
                      @click="applyAltSuggestion(suggestion)"
                    >
                      <el-icon><Place /></el-icon>
                      <span>{{ suggestion }}</span>
                      <div class="apply-btn">{{ t('Media.GridEdit.apply') }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 详细描述区域 -->
              <div class="detailed-desc-section">
                <div class="section-label">{{ t('Media.GridEdit.detailed_desc') }}</div>
                <el-input 
                  v-model="formData.description" 
                  type="textarea" 
                  :rows="4"
                  :placeholder="t('Media.GridEdit.enter_detailed_desc')"
                />
                <div class="char-count">{{ formData.description.length }} {{ t('Media.GridEdit.characters') }}</div>
                
                <!-- 详细描述建议区域 -->
                <div class="detail-suggestions">
                  <div class="suggestions-header">
                    <div class="suggestions-title">{{ t('Media.GridEdit.description_suggestions') }}</div>
                    <el-button 
                      type="primary"
                      link 
                      class="refresh-btn" 
                      @click="refreshDetailSuggestions" 
                      :loading="refreshingDetail"
                      :disabled="aiLoading"
                    >
                      {{ t('Media.GridEdit.refresh_suggestions') }}
                    </el-button>
                  </div>
                  <div class="suggestions-list">
                    <div 
                      v-for="(suggestion, index) in detailSuggestions" 
                      :key="index"
                      class="suggestion-item"
                      @click="applyDetailSuggestion(suggestion)"
                    >
                      <el-icon><Place /></el-icon>
                      <span>{{ suggestion }}</span>
                      <div class="apply-btn">{{ t('Media.GridEdit.apply') }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 标签区域 -->
              <div class="tags-section">
                <div class="section-label flex justify-between">{{ t('Media.GridEdit.tags') }}
                  <el-button type="text" link size="small" @click="openTagsDialog">
                    <el-icon size="16"><Plus /></el-icon> {{ t('Media.GridEdit.add_new_tag') }}
                  </el-button>
                </div>
                <div class="tags-container">
                  <el-tag
                    v-for="tag in formData.tags"
                    :key="tag"
                    closable
                    @close="handleTagRemove(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                </div>

                <!-- 标签建议区域 -->
                <div class="tag-suggestions">
                  <div class="suggestions-header">
                    <div class="suggestions-title">{{ t('Media.GridEdit.tag_suggestions') }}</div>
                    <el-button 
                      type="primary"
                      link
                      class="refresh-btn" 
                      @click="refreshTagSuggestions" 
                      :loading="refreshingTags"
                      :disabled="aiLoading"
                    >
                      {{ t('Media.GridEdit.refresh_suggestions') }}
                    </el-button>
                  </div>
                  <div class="tags-suggestions-list">
                    <el-tag
                      v-for="(suggestion, index) in tagSuggestions" 
                      :key="index"
                      class="suggestion-tag"
                      @click="applyTagSuggestion(suggestion)"
                      type="success"
                      effect="plain"
                    >
                      <el-icon class="tag-plus-icon"><Plus /></el-icon>
                      {{ suggestion }}
                    </el-tag>
                  </div>
                </div>

                <el-input
                  v-if="tagInputVisible"
                  ref="tagInputRef"
                  v-model="tagInputValue"
                  class="tag-input"
                  size="small"
                  @keyup.enter="handleTagConfirm"
                  @blur="handleTagConfirm"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容：图像信息和版本历史 -->
        <div class="right-section">
          <!-- 信息和版本历史标签页 -->
          <div class="info-tabs-panel">
            <el-tabs tab-position="top" v-model="activeInfoTab" class="info-tabs">
              <el-tab-pane :label="t('Media.GridEdit.image_info')" name="info">
                <div class="tab-content info-content">
                  <div class="info-item">
                    <span class="label">{{ t('Media.GridEdit.file_name') }}</span>
                    <span class="value">{{ formData.file_name }}</span>
                  </div>
                  
                  <div class="info-item">
                    <span class="label">{{ t('Media.GridEdit.file_size') }}</span>
                    <span class="value">{{ formData.file_size }}</span>
                  </div>
                  
                  <div class="info-item">
                    <span class="label">{{ t('Media.GridEdit.image_size') }}</span>
                    <span class="value">{{ formData.width }} × {{ formData.height }} {{ t('Media.GridEdit.pixels') }}</span>
                  </div>
                  
                  <div class="info-item">
                    <span class="label">{{ t('Media.GridEdit.upload_date') }}</span>
                    <span class="value">{{ formData.created_at }}</span>
                  </div>
                  
                  <div class="info-item">
                    <span class="label">{{ t('Media.GridEdit.uploader') }}</span>
                    <span class="value">{{ formData.created_by }}</span>
                  </div>
                  
                  <div class="info-item">
                    <span class="label">{{ t('Media.GridEdit.update_time') }}</span>
                    <span class="value">{{ formData.updated_at }}</span>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane :label="t('Media.GridEdit.version_history')" name="history">
                <div class="tab-content version-content">
                  <div class="version-list" v-if="formData.versions && formData.versions.length > 0">
                    <div class="version-item" v-for="(version, index) in formData.versions" :key="index">
                      <div class="version-icon">
                        <el-icon v-if="version.type === 'update'"><CircleCheck /></el-icon>
                        <el-icon v-if="version.type === 'edit'"><EditPen /></el-icon>
                        <el-icon v-if="version.type === 'upload'"><Upload /></el-icon>
                        <el-icon v-if="version.type === 'ai'"><Place /></el-icon>
                      </div>
                      <div class="version-content">
                        <div class="version-title">{{ version.title }}</div>
                        <div class="version-meta">{{ version.date }} · {{ version.user }}</div>
                        <div class="version-details" v-if="version.details">
                          {{ version.details }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="no-data" v-else>
                    <el-empty :description="t('Media.GridEdit.no_version_history')" :image-size="80" />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- AI内容分析 -->
          <div class="ai-analysis-panel" v-if="formData.aiAnalysis.contentTypes.length > 0 || formData.aiAnalysis.scenes.length > 0 || formData.aiAnalysis.emotionTags.length > 0">
            <div class="panel-title">
              {{ t('Media.GridEdit.ai_content_analysis') }}
              <el-tooltip :content="t('Media.GridEdit.ai_analysis_tooltip')" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            
            <div class="ai-analysis-content">
              <!-- 内容类型辨别 -->
              <div class="analysis-section" v-if="formData.aiAnalysis.contentTypes.length > 0">
                <div class="section-label">
                  {{ t('Media.GridEdit.content_type') }}
                  <span class="confidence-right">{{ t('Media.GridEdit.confidence') }}</span>
                </div>
                
                <div class="content-type-item" v-for="(item, index) in formData.aiAnalysis.contentTypes" :key="index">
                  <div class="type-name">{{ item.type }}</div>
                  <div class="confidence-bar-container">
                    <div class="confidence-bar" :style="{ width: item.confidence + '%' }"></div>
                  </div>
                  <div class="confidence-value">{{ item.confidence }}%</div>
                </div>
              </div>
              
              <!-- 场景分析 -->
              <div class="analysis-section" v-if="formData.aiAnalysis.scenes && formData.aiAnalysis.scenes.length > 0">
                <div class="section-label">{{ t('Media.GridEdit.scene_analysis') }}</div>
                
                <div class="scene-tags">
                  <el-tag 
                    v-for="scene in formData.aiAnalysis.scenes" 
                    :key="scene"
                    size="small"
                    class="scene-tag"
                    type="success"
                  >
                    {{ scene }}
                  </el-tag>
                </div>
              </div>
              
              <!-- 情感分析 -->
              <div class="analysis-section" v-if="formData.aiAnalysis.emotionTags.length > 0">
                <div class="section-label">{{ t('Media.GridEdit.emotion_analysis') }}</div>
                
                <div class="emotion-tags">
                  <el-tag
                    v-for="emotion in formData.aiAnalysis.emotionTags"
                    :key="emotion"
                    class="emotion-tag"
                    size="small"
                    type="warning"
                  >
                    <el-icon class="emotion-icon"><Star /></el-icon>
                    {{ emotion }}
                  </el-tag>
                </div>
                
                <div class="emotion-analysis" v-if="formData.aiAnalysis.emotionAnalysis">
                  {{ formData.aiAnalysis.emotionAnalysis }}
                </div>
              </div>
              
              <!-- 相关标签 - 使用AI分析返回的相关标签，与表单中的tags独立 -->
              <div class="analysis-section" v-if="formData.aiAnalysis.relatedTags && formData.aiAnalysis.relatedTags.length > 0">
                <div class="section-label">{{ t('Media.GridEdit.related_tags') }}</div>
                
                <div class="related-tags">
                  <el-tag 
                    v-for="tag in formData.aiAnalysis.relatedTags" 
                    :key="tag"
                    size="small"
                    class="related-tag"
                    type="info"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          
          <!-- AI内容分析无数据时显示 -->
          <div class="ai-analysis-panel" v-else>
            <div class="panel-title">
              {{ t('Media.GridEdit.ai_content_analysis') }}
              <el-tooltip :content="t('Media.GridEdit.ai_analysis_tooltip')" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="no-data">
              <el-empty :description="t('Media.GridEdit.no_ai_analysis')" :image-size="80" />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="form-actions flex justify-center">
        <el-button class="button-cancel" @click="handleCancel">{{ t('Media.GridList.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="saveLoading">{{ t('Media.GridEdit.save') }}</el-button>
      </div>
    </div>

    <!-- 标签管理对话框 -->
    <el-dialog
      v-model="tagsDialogVisible"
      :title="t('Media.GridEdit.tag_management')"
      width="550px"
      :close-on-click-modal="false"
      class="el-dialog-common-cls"
    >
      <div class="tags-dialog-content">
        <!-- 标签搜索和新增 -->
        <div class="tags-search">
          <el-autocomplete
            v-model="tagSearchValue"
            :fetch-suggestions="queryTagsSuggestions"
            :placeholder="t('Media.GridEdit.enter_tag_keywords')"
            class="tags-search-input"
            @select="handleTagSelect"
            :trigger-on-focus="false"
          >
            <template #suffix>
              <el-button 
                style="height: 34px;"
                type="primary" 
                @click="addNewTag" 
                :disabled="!tagSearchValue.trim()"
              >
                {{ t('Media.GridEdit.add_tag') }}
              </el-button>
            </template>
            <template #default="{ item }">
              <div class="tag-suggestion-item">
                <span>{{ item.value }}</span>
              </div>
            </template>
          </el-autocomplete>
        </div>
        
        <!-- 已选标签 -->
        <div class="selected-tags">
          <div class="section-title">{{ t('Media.GridEdit.selected_tags') }}</div>
          <div class="tags-list">
            <el-tag
              v-for="tag in formData.tags"
              :key="tag"
              closable
              @close="handleTagRemove(tag)"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
        
        <!-- 所有标签 -->
        <div class="all-tags">
          <div class="section-title">
            {{ tagSearchValue ? t('Media.GridEdit.search_results') : t('Media.GridEdit.all_tags') }}
            <span v-if="tagSearchValue && searchResults.length > 0" class="result-count">({{ searchResults.length }})</span>
            <el-button v-if="tagSearchValue" type="text" @click="clearTagSearch" style="font-size: 12px;">{{ t('Media.GridEdit.view_all') }}</el-button>
          </div>
          <div class="tags-list">
            <el-tag
              v-for="tag in tagSearchValue ? searchResults : allTags"
              :key="tag.id"
              @click="toggleTag(tag.tag_name)"
              :class="{ 'is-selected': formData.tags.includes(tag.tag_name) }"
              class="tag-item"
            >
              {{ tag.tag_name }}
            </el-tag>
            <div v-if="tagSearchValue && searchResults.length === 0" class="no-result">
              {{ t('Media.GridEdit.no_results') }}
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElImageViewer } from 'element-plus'
import { Download, ZoomIn, Place, CircleCheck, EditPen, Upload, Star, Plus, InfoFilled, Refresh } from '@element-plus/icons-vue'
import http from '/admin/support/http'
import { useI18n } from 'vue-i18n'

// 初始化路由
const router = useRouter()
const route = useRoute()
const mediaId = route.params.id as string
const formRef = ref<FormInstance>()
const tagInputRef = ref()
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const loading = ref(false)
const aiLoading = ref(false)
const saveLoading = ref(false)
const activeInfoTab = ref('info')

// 获取国际化实例
const { locale, t } = useI18n()

// 表单数据
interface Version {
  title: string;
  date: string;
  user: string;
  type: 'update' | 'edit' | 'upload' | 'ai';
  details?: string;
}

interface MediaDetailForm {
  id: number;
  file_name: string;
  url: string;
  title: string | null;
  alt_text: string;
  description: string;
  file_size: string;
  content_type: string;
  tags: string[];
  dir: string;
  width: number;
  height: number;
  creator_id: number;
  created_by: string;
  created_at: string;
  updated_at: string;
  versions: Version[];
  aiAnalysis: {
    contentTypes: { type: string; confidence: string }[];
    emotionTags: string[];
    emotionAnalysis: string;
    scenes: string[];
    relatedTags: string[];
  }
}

const formData = reactive<MediaDetailForm>({
  id: 0,
  file_name: '',
  url: '',
  title: null,
  alt_text: '',
  description: '',
  file_size: '',
  content_type: '',
  tags: [],
  dir: '',
  width: 0,
  height: 0,
  creator_id: 0,
  created_by: '',
  created_at: '',
  updated_at: '',
  versions: [],
  aiAnalysis: {
    contentTypes: [],
    emotionTags: [],
    emotionAnalysis: '',
    scenes: [],
    relatedTags: []
  }
})

// 标签管理相关
const tagsDialogVisible = ref(false)
const tagSearchValue = ref('')
const allTags = ref<{ id: number, tag_name: string }[]>([])
const searchResults = ref<{ id: number, tag_name: string }[]>([])

// 预览相关
const previewVisible = ref(false)

// 替代文字建议
const altSuggestions = ref<string[]>([])

// 详细描述建议
const detailSuggestions = ref<string[]>([])

// 标签建议
const tagSuggestions = ref<string[]>([])

// 刷新相关状态
const refreshingAlt = ref(false)
const refreshingDetail = ref(false)
const refreshingTags = ref(false)

// 应用替代文字建议
const applyAltSuggestion = (suggestion: string) => {
  formData.alt_text = suggestion
}

// 应用详细描述建议
const applyDetailSuggestion = (suggestion: string) => {
  formData.description = suggestion
}

// 应用标签建议
const applyTagSuggestion = (suggestion: string) => {
  if (!formData.tags.includes(suggestion)) {
    formData.tags.push(suggestion)
    // 从建议中移除这个标签
    // tagSuggestions.value = tagSuggestions.value.filter(tag => tag !== suggestion)
  }
}

// 刷新标签建议
const refreshTagSuggestions = async () => {
  try {
    refreshingTags.value = true
    ElMessage.info(t('Media.GridEdit.generating'))
    const response = await http.post(`/media/${mediaId}/ai/genterate`, {
      enhance_type: 'tags',
      lang: locale.value
    })
    
    if (response.data.code === 200) {
      const aiData = response.data.data
      
      if (aiData && aiData.tags && Array.isArray(aiData.tags)) {
        // 只推荐未添加的标签
        tagSuggestions.value = aiData.tags.filter(
          (tag: string) => !formData.tags.includes(tag)
        )
      }
      
      ElMessage.success(t('Media.GridEdit.suggestions_refreshed'))
    } else {
      ElMessage.error(response.data.message || t('Media.GridEdit.refresh_failed'))
    }
  } catch (error) {
  } finally {
    refreshingTags.value = false
  }
}

// 刷新替代文字建议
const refreshAltSuggestions = async () => {
  try {
    refreshingAlt.value = true
    ElMessage.info(t('Media.GridEdit.generating'))
    const response = await http.post(`/media/${mediaId}/ai/genterate`, {
      enhance_type: 'alt_text',
      lang: locale.value
    })
    
    if (response.data.code === 200) {
      const aiData = response.data.data
      
      if (aiData && aiData.alt_text && Array.isArray(aiData.alt_text)) {
        altSuggestions.value = aiData.alt_text
      }
      
      ElMessage.success(t('Media.GridEdit.suggestions_refreshed'))
    } else {
      ElMessage.error(response.data.message || t('Media.GridEdit.refresh_failed'))
    }
  } catch (error) {
  } finally {
    refreshingAlt.value = false
  }
}

// 刷新详细描述建议
const refreshDetailSuggestions = async () => {
  try {
    refreshingDetail.value = true
    ElMessage.info(t('Media.GridEdit.generating'))
    const response = await http.post(`/media/${mediaId}/ai/genterate`, {
      enhance_type: 'description',
      lang: locale.value
    })
    
    if (response.data.code === 200) {
      const aiData = response.data.data
      
      if (aiData && aiData.description && Array.isArray(aiData.description)) {
        detailSuggestions.value = aiData.description
      }
      
      ElMessage.success(t('Media.GridEdit.suggestions_refreshed'))
    } else {
      ElMessage.error(response.data.message || t('Media.GridEdit.refresh_failed'))
    }
  } catch (error) {
  } finally {
    refreshingDetail.value = false
  }
}

// 处理标签确认
const handleTagConfirm = () => {
  if (tagInputValue.value.trim()) {
    if (!formData.tags.includes(tagInputValue.value.trim())) {
      formData.tags.push(tagInputValue.value.trim())
    }
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

// 移除标签
const handleTagRemove = (tag: string) => {
  formData.tags = formData.tags.filter(item => item !== tag)
}

const emit = defineEmits(['returnTab'])
// 取消
const handleCancel = () => {
  const returnTab = route.query.returnTab as string || 'grid'
  // 使用localStorage存储需要返回的tab
  localStorage.setItem('media_active_tab', returnTab)
  router.back()
}

// 获取AI分析
const fetchAiAnalyze = async () => {
  try {
    const response = await http.get(`/media/${mediaId}/analyze`)
    const responseData = response.data
    
    if (responseData.code === 200) {
      const analyzeData = responseData.data
      
      if (analyzeData) {
        // 填充AI分析数据，但不更新图片描述和标签
        formData.aiAnalysis = {
          contentTypes: analyzeData.analysis?.content_types || [],
          emotionTags: analyzeData.analysis?.emotions?.emotion_list || [],
          emotionAnalysis: analyzeData.analysis?.emotions?.description || '',
          scenes: analyzeData.analysis?.scenes || [],
          relatedTags: analyzeData.tags || [] // 使用API返回的tags作为相关标签
        }
       
      }
    }
  } catch (error) {
  }
}

// 获取媒体详情
const fetchMediaDetail = async () => {
  try {
    loading.value = true
    const response = await http.get(`/media/${mediaId}`)
    const responseData = response.data
    
    if (responseData.code === 200) {
      const mediaData = responseData.data
      
      // 直接将API返回的字段赋值给表单数据
      formData.id = mediaData.id || 0
      formData.file_name = mediaData.file_name || ''
      formData.url = mediaData.url || ''
      formData.title = mediaData.title
      formData.alt_text = mediaData.alt_text || ''
      formData.description = mediaData.description || ''
      formData.file_size = mediaData.file_size || ''
      formData.content_type = mediaData.content_type || ''
      formData.tags = mediaData.tags || []
      formData.dir = mediaData.dir || ''
      formData.width = mediaData.width || 0
      formData.height = mediaData.height || 0
      formData.creator_id = mediaData.creator_id || 0
      formData.created_by = mediaData.created_by || ''
      formData.created_at = mediaData.created_at || ''
      formData.updated_at = mediaData.updated_at || ''
      
      // 获取日志和AI分析
      fetchMediaLogs()
      fetchAiAnalyze()
    } else {
      ElMessage.error(responseData.message || t('Media.GridEdit.load_failed'))
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}

// 获取媒体日志
const fetchMediaLogs = async () => {
  try {
    const response = await http.get(`/media/${mediaId}/logs`)
    const responseData = response.data
    
    if (responseData.code === 200) {
      const logsData = responseData.data || []
      
      // 转换日志数据为版本历史格式
      formData.versions = logsData.map((log: any) => {
        const version: Version = {
          title: log.action_type_str || t('Media.GridEdit.unknown_operation'),
          date: log.created_at || '',
          user: log.creator_name || t('Media.GridEdit.system'),
          type: mapActionToType(log.action_type || ''),
          details: log.action_details || ''
        }
        return version
      })
    }
  } catch (error) {
  }
}

// 将日志动作映射为版本类型
const mapActionToType = (action: string): 'update' | 'edit' | 'upload' | 'ai' => {
  const actionMap: Record<string, 'update' | 'edit' | 'upload' | 'ai'> = {
    'upload': 'upload',
    'update': 'update',
    'edit': 'edit',
    'ai_generate': 'ai'
  }
  
  return actionMap[action] || 'edit'
}

// AI重新生成
const handleAiGenerate = async () => {
  try {
    aiLoading.value = true
    ElMessage.info(t('Media.GridEdit.generating'))
    
    const response = await http.post(`/media/${mediaId}/ai/genterate`, {
      enhance_type: 'all',
      lang: locale.value
    })
    const responseData = response.data
    
    if (responseData.code === 200) {
      ElMessage.success(t('Media.GridEdit.generate_success'))
      
      // 接口数据处理
      if (responseData.data) {
        const aiData = responseData.data
        
        // 更新建议数据而不是直接赋值给表单
        if (aiData.alt_text && Array.isArray(aiData.alt_text)) {
          altSuggestions.value = aiData.alt_text
        }
        
        if (aiData.description && Array.isArray(aiData.description)) {
          detailSuggestions.value = aiData.description
        }
        
        // 更新标签建议
        if (aiData.tags && Array.isArray(aiData.tags)) {
          // 只推荐未添加的标签
          tagSuggestions.value = aiData.tags.filter(
            (tag: string) => !formData.tags.includes(tag)
          )
        }
        
        // 更新AI分析数据
        if (aiData.analysis) {
          formData.aiAnalysis = {
            contentTypes: aiData.analysis.content_types || formData.aiAnalysis.contentTypes,
            emotionTags: aiData.analysis.emotions?.emotion_list || formData.aiAnalysis.emotionTags,
            emotionAnalysis: aiData.analysis.emotions?.description || formData.aiAnalysis.emotionAnalysis,
            scenes: aiData.analysis.scenes || formData.aiAnalysis.scenes,
            relatedTags: aiData.tags || formData.aiAnalysis.relatedTags // 使用API返回的tags作为相关标签
          }
        }
      }
      
      // 重新获取媒体日志
      fetchMediaLogs()
    } else {
      ElMessage.error(responseData.message || t('Media.GridEdit.generate_failed'))
    }
  } catch (error) {
  } finally {
    aiLoading.value = false
  }
}

// 提交
const handleSubmit = async () => {
  try {
    saveLoading.value = true
    
    // 准备提交的数据
    const submitData = {
      alt_text: formData.alt_text,
      description: formData.description,
      tags: formData.tags
    }
    
    const response = await http.post(`/media/${mediaId}`, submitData)
    const responseData = response.data
    
    if (responseData.code === 200) {
      const returnTab = route.query.returnTab as string || 'grid'
      // 使用localStorage存储需要返回的tab
      localStorage.setItem('media_active_tab', returnTab)
      ElMessage.success(t('Media.GridEdit.save_success'))
      router.back()
    } else {
      ElMessage.error(responseData.message || t('Media.GridEdit.save_failed'))
    }
  } catch (error) {
  } finally {
    saveLoading.value = false
  }
}

// 打开标签管理对话框
const openTagsDialog = async () => {
  tagsDialogVisible.value = true
  await fetchAllTags()
  tagSearchValue.value = ''
  searchResults.value = []
}

// 获取所有标签
const fetchAllTags = async (searchKeyword?: string) => {
  try {
    const params = searchKeyword ? { search: searchKeyword } : {}
    const response = await http.get('/media/tags', params)
    if (response.data.code === 200) {
      if (searchKeyword) {
        searchResults.value = response.data.data || []
      } else {
        allTags.value = response.data.data || []
      }
    } else {
      ElMessage.warning(response.data.message || t('Media.GridEdit.get_tags_failed'))
    }
  } catch (error) {
  }
}

// 搜索标签建议
const queryTagsSuggestions = async (queryString: string, callback: (data: any[]) => void) => {
  if (queryString) {
    await fetchAllTags(queryString)
  }
  
  const suggestions = searchResults.value.map(tag => ({
    value: tag.tag_name,
    id: tag.id
  }))
  
  callback(suggestions)
}

// 处理标签选择
const handleTagSelect = (item: { value: string }) => {
  if (item.value && !formData.tags.includes(item.value)) {
    formData.tags.push(item.value)
  }
  tagSearchValue.value = ''
}

// 清除标签搜索
const clearTagSearch = () => {
  tagSearchValue.value = ''
  searchResults.value = []
}

// 添加新标签
const addNewTag = async () => {
  if (!tagSearchValue.value.trim()) return
  
  const existingTag = [...allTags.value, ...searchResults.value].find(
    tag => tag.tag_name.toLowerCase() === tagSearchValue.value.trim().toLowerCase()
  )
  
  if (existingTag) {
    if (!formData.tags.includes(existingTag.tag_name)) {
      formData.tags.push(existingTag.tag_name)
    } else {
      ElMessage.info(t('Media.GridEdit.tag_already_added'))
    }
  } else {
    try {
      loading.value = true
      const response = await http.post('/media/tags', {
        name: tagSearchValue.value.trim()
      })
      
      if (response.data.code === 200) {
        ElMessage.success(t('Media.GridList.add_tag_success'))
        formData.tags.push(tagSearchValue.value.trim())
        await fetchAllTags()
      } else {
        ElMessage.error(response.data.message || t('Media.GridList.add_tag_failed'))
      }
    } catch (error) {
    } finally {
      loading.value = false
    }
  }
  
  tagSearchValue.value = ''
  searchResults.value = []
}

// 切换标签选择状态
const toggleTag = (tagName: string) => {
  const index = formData.tags.indexOf(tagName)
  if (index > -1) {
    formData.tags.splice(index, 1)
  } else {
    formData.tags.push(tagName)
  }
}

// 处理预览
const handlePreview = () => {
  if (!formData.url) {
    ElMessage.warning(t('Media.GridEdit.image_not_exists'))
    return
  }
  
  // 对于非图片类型，可能需要不同的处理
  if (formData.content_type && !formData.content_type.startsWith('image/')) {
    // 如果是视频或其他媒体类型，可以在新窗口打开
    window.open(formData.url, '_blank')
    return
  }
  
  // 图片类型使用预览组件
  previewVisible.value = true
}

// 获取图片描述建议
const fetchImageSuggestions = async () => {
  try {
    const response = await http.get(`/media/${mediaId}/suggestions`, {
      params: { lang: locale.value }
    })
    
    if (response.data.code === 200) {
      const data = response.data.data || {}
      
      // 更新建议数据
      altSuggestions.value = data.alt_text_suggestions || []
      detailSuggestions.value = data.description_suggestions || []
      
      // 过滤掉已存在的标签建议
      if (data.tag_suggestions) {
        tagSuggestions.value = data.tag_suggestions.filter(
          (tag: string) => !formData.tags.includes(tag)
        )
      }
    }
  } catch (error) {
    console.error('Failed to fetch suggestions:', error)
  }
}

// 模拟获取图片数据
onMounted(() => {
  fetchMediaDetail()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    background-color: #f5f7fa;
    height: 100%;
    
    &::-webkit-scrollbar-thumb {
      background-color: transparent;
      
      &:hover {
        background-color: transparent;
      }
    }

    .page-container {
      display: flex;
      gap: 20px;
    }
    
    /* 左侧区域样式 */
    .left-section {
      flex: 3;
      display: flex;
      flex-direction: column;
      gap: 20px;
      .action-item {
        cursor: pointer;
        padding: 0 8px;
        font-size: 18px;
        &:hover {
          color: #409EFF;
        }
      }
      .preview-container {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 65px; 
          padding: 0 16px;
          border-bottom: 1px solid #e8e8e8;

          .title {
            font-size: 16px;
            color: #000;
          }

          .actions {
            display: flex;
            gap: 8px;
          }
        }

        .image-preview {
          height: 400px;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            max-width: 94%;
            max-height: 94%;
            object-fit: contain;
          }
        }
      }

      .description-container {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        flex: 1;
        display: flex;
        flex-direction: column;

        .description-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 65px; 
          padding: 0 16px;
          border-bottom: 1px solid #e8e8e8;

          .title {
            font-size: 16px;
            color: #000;
          }

          .actions {
            display: flex;
            gap: 8px;

            .ai-button {
              background-color: #4a6ee0;
              border-color: #4a6ee0;
            }
          }
        }

        .description-content {
          padding: 16px;
          overflow-y: auto;
          flex: 1;
          
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          
          &::-webkit-scrollbar-thumb {
            background-color: rgba(64, 158, 255, 0.3);
            border-radius: 3px;
            
            &:hover {
              background-color: rgba(64, 158, 255, 0.5);
            }
          }

          .alt-text-section,
          .detailed-desc-section,
          .tags-section {
            margin-bottom: 26px;

            .section-label {
              margin-bottom: 8px;
              color: #000;
            }

            .char-count {
              text-align: right;
              font-size: 12px;
              color: #909399;
              margin-top: 4px;
            }
          }

          .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;

            .el-tag {
              margin: 0;
              border-radius: 2px;
            }

            .add-tag-btn {
              background: transparent;
              color: #409EFF;
            }
          }

          .tag-input {
            width: 120px;
          }
          
          .tag-suggestions {
            margin-top: 15px;
            border-radius: 4px;
            
            .suggestions-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 10px;
              
              .suggestions-title {
                font-size: 14px;
                color: #000;
              }
              
              .refresh-btn {
                font-size: 14px;
                .el-icon {
                  margin-right: 4px;
                }
              }
            }
            
            .tags-suggestions-list {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;
              
              .suggestion-tag {
                cursor: pointer;
                padding: 0 10px;
                height: 28px;
                line-height: 26px;
                
                .tag-plus-icon {
                  margin-right: 4px;
                  font-size: 12px;
                }
                
                &:hover {
                  opacity: 0.8;
                }
              }
            }
          }
        }
      }
    }

    /* 右侧区域样式 */
    .right-section {
      flex: 2;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .info-tabs-panel {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        height: 465px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        
        .info-tabs {
          height: 100%;
          display: flex;
          
          :deep(.el-tabs__header) {
            margin-bottom: 0;
            border-bottom: 1px solid #e8e8e8;
          }
          
          :deep(.el-tabs__nav-wrap::after) {
            display: none;
          }
          
          :deep(.el-tabs__nav-wrap) {
            padding: 0 16px;
          }
          
          :deep(.el-tabs__item) {
            font-size: 16px;
            color: #000;
            height: 65px;
            line-height: 65px;
            padding: 0;
            margin-right: 25px;
            
            &.is-active {
              color: var(--theme-text-color);
              padding: 0;
            }
          }
          
          :deep(.el-tabs__content) {
            flex: 1;
            overflow: hidden;
            padding: 0;
          }
          
          :deep(.el-tab-pane) {
            height: 100%;
            overflow: hidden;
          }
        }
        
        .tab-content {
          height: 100%;
          padding: 16px;
          overflow-y: auto;
          
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          
          &::-webkit-scrollbar-thumb {
            background-color: rgba(64, 158, 255, 0.3);
            border-radius: 3px;
            
            &:hover {
              background-color: rgba(64, 158, 255, 0.5);
            }
          }
        }
        
        .info-content {
          .info-item {
            display: flex;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              width: 80px;
              color: #000;
              flex-shrink: 0;
            }

            .value {
              color: #000;
              word-break: break-word;
            }
          }
        }
        
        .version-content {
          .version-list {
            .version-item {
              display: flex;
              gap: 10px;
              padding: 10px 0;
              border-bottom: 1px solid #ebeef5;

              &:last-child {
                border-bottom: none;
              }

              .version-icon {
                width: 22px;
                height: 22px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #4a6ee0;
                flex-shrink: 0;
              }

              .version-content {
                flex: 1;

                .version-title {
                  margin-bottom: 4px;
                  color: #000;
                }

                .version-meta {
                  font-size: 12px;
                  color: #909399;
                  margin-bottom: 4px;
                }

                .version-details {
                  font-size: 12px;
                  color: #606266;
                  line-height: 1.5;
                }
              }
            }
          }
        }
      }

      .ai-analysis-panel {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        flex: 1;
        display: flex;
        flex-direction: column;
        max-height: 636px;
        overflow: hidden;

        .panel-title {
          font-size: 16px;
          color: #000;
          height: 65px; 
          padding: 0 16px;
          border-bottom: 1px solid #e8e8e8;
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex-shrink: 0;

          .info-icon {
            margin-left: 8px;
            color: #909399;
            font-size: 14px;
            cursor: help;
          }
        }
        
        .ai-analysis-content {
          overflow-y: auto;
          flex: 1;
          padding: 16px;
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          
          &::-webkit-scrollbar-thumb {
            background-color: rgba(64, 158, 255, 0.3);
            border-radius: 3px;
            
            &:hover {
              background-color: rgba(64, 158, 255, 0.5);
            }
          }
        }
        
        .no-data {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 20px 0;
          flex: 1;
          
          .el-empty {
            padding: 10px 0;
          }
        }

        .analysis-section {
          margin-bottom: 24px;

          .section-label {
            margin-bottom: 12px;
            color: #000;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            
            .confidence-right {
              font-size: 12px;
              color: #909399;
              font-weight: normal;
            }
          }

          .content-type-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            position: relative;

            .type-name {
              font-size: 14px;
              color: #000;
              width: 80px;
              flex-shrink: 0;
            }

            .confidence-bar-container {
              flex: 1;
              height: 8px;
              background-color: #ebeef5;
              border-radius: 4px;
              overflow: hidden;
              margin: 0 12px;

              .confidence-bar {
                height: 100%;
                background-color: #4080ff;
                border-radius: 4px;
              }
            }

            .confidence-value {
              font-size: 13px;
              color: #606266;
              width: 40px;
              text-align: right;
              flex-shrink: 0;
            }
          }

          .scene-tags {
            margin-bottom: 14px;
            
            .scene-tag {
              background-color: #f0f9eb;
              color: #67c23a;
              border-color: #e1f3d8;
              
              &:hover {
                color: #67c23a;
                border-color: #c2e7b0;
                background-color: #f0f9eb;
              }
            }
          }

          .emotion-tags {
            margin-bottom: 14px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .emotion-tag {
              display: inline-flex;
              align-items: center;
              background-color: #fcf6ed;
              color: #e6a23c;
              
              .emotion-icon {
                color: #e6a23c;
                margin-right: 6px;
                font-size: 14px;
              }
            }
          }

          .emotion-analysis {
            margin-bottom: 10px;
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
          }

          .related-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            
            .related-tag {
              margin: 0;
              background-color: #f4f4f5;
              color: #606266;
              border-color: #e9e9eb;
              
              &:hover {
                color: #409EFF;
                border-color: #c6e2ff;
                background-color: #ecf5ff;
              }
            }
          }
        }
      }
    }

    /* 底部按钮样式 */
    .form-actions {
      padding-top: 34px;
    }
  }
}

/* 自定义图片查看器样式 */
:deep(.el-image-viewer__canvas) {
  img {
    width: 60%;
    max-width: 60%;
  }
}

/* 标签管理对话框样式 */
.tags-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 60vh;
  
  .tags-search {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .tags-search-input {
      width: 100%;
    }
  }
  
  .section-title {
    margin-bottom: 12px;
    color: #000;
    font-size: 14px;
    display: flex;
    align-items: center;
    
    .result-count {
      font-size: 12px;
      color: #909399;
      margin-left: 4px;
      font-weight: normal;
    }
  }
  
  .selected-tags,
  .all-tags {
    .tags-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .tag-item {
        cursor: pointer;
        margin: 0;
      }
      
      .is-selected {
        background-color: #409EFF;
        color: #fff;
        border-color: #409EFF;
      }
    }
  }
  
  .all-tags {
    .tags-list {
      max-height: 200px;
      overflow-y: auto;
      
      .no-result {
        color: #909399;
        font-size: 14px;
        text-align: center;
        padding: 20px 0;
      }
      
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(64, 158, 255, 0.3);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(64, 158, 255, 0.5);
        }
      }
    }
  }
}

.scene-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  
  .scene-tag {
    background-color: #f0f9eb;
    color: #67c23a;
    border-color: #e1f3d8;
    
    &:hover {
      color: #67c23a;
      border-color: #c2e7b0;
      background-color: #f0f9eb;
    }
  }
}

/* 替代文字和详细描述建议样式 */
.alt-suggestions, .detail-suggestions {
  border-radius: 4px;
  
  .suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .suggestions-title {
      font-size: 14px;
      color: #000;
    }
    
    .refresh-btn {
      font-size: 14px;
      .el-icon {
        margin-right: 4px;
      }
    }
  }
  
  .suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(64, 158, 255, 0.3);
      border-radius: 3px;
      
      &:hover {
        background-color: rgba(64, 158, 255, 0.5);
      }
    }
    
    .suggestion-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: #fff;
      border: 1px solid #e1f3d8;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        border-color: #b3e19d;
        background-color: #f7faf5;
      }
      
      .el-icon {
        margin-right: 8px;
        color: #67c23a;
        font-size: 16px;
      }
      
      span {
        flex: 1;
        font-size: 14px;
        color: #000;
      }
      
      .apply-btn {
        color: #67c23a;
        font-size: 14px;
        background-color: #f0f9eb;
        padding: 4px 8px;
        border-radius: 2px;
        margin-left: 10px;
        
        &:hover {
          background-color: #e1f3d8;
        }
      }
    }
  }
}
</style>
