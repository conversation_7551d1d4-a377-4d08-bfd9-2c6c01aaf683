<template>
  <div class="grid-list table-page">
    <!-- 内容区域 -->
    <div class="grid-con">
      <div class="box">
        <!-- 搜索和筛选区域 -->
        <div class="search-area">
          <div class="search-left">
            <el-form :inline="true" :model="searchForm">
              <el-form-item>
                <el-input 
                  v-model="searchForm.search"
                  :placeholder="t('Media.GridList.enter_keywords')"
                  prefix-icon="Search"
                  size="large"
                  clearable
                />
              </el-form-item>
              
              <el-form-item>
                <el-select 
                  v-model="searchForm.sort"
                  :placeholder="t('Media.GridList.recent_update')"
                  style="width: 120px;"
                  @change="handleSortChange"
                >
                  <el-option :label="t('Media.GridList.recent_update')" :value=1 />
                  <el-option :label="t('Media.GridList.file_name')" :value=2 />
                  <el-option :label="t('Media.GridList.file_size')" :value=3 />
                  <el-option :label="t('Media.GridList.create_date')" :value=4 />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="search-right">
            <el-button @click="handleClear">{{ t('Media.GridList.reset') }}</el-button>
            <el-button style="margin-right: 12px;" type="primary" @click="handleSearch">
              {{ t('Media.GridList.search') }}
            </el-button>
            <el-dropdown @command="handleUploadType" trigger="hover">
              <el-button type="primary" class="upload-btn">
                {{ t('Media.GridList.upload') }}
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="image">{{ t('Media.GridList.image') }}</el-dropdown-item>
                  <el-dropdown-item command="video">{{ t('Media.GridList.video') }}</el-dropdown-item>
                  <el-dropdown-item command="audio">{{ t('Media.GridList.audio') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 标签筛选区域 -->
        <div class="tags-filter">
          <div class="filter-group">
            <div class="filter-label">{{ t('Media.GridList.tags') }}</div>
            <div class="filter-tags">
              <el-tag 
                :class="{ active: selectedTags.length === 0 }" 
                @click="handleTagsFilter([])"
              >{{ t('Media.GridList.all') }}</el-tag>
              <el-tag 
                v-for="tag in tagOptions" 
                :key="tag.value"
                :class="{ active: selectedTags.includes(tag.value) }"
                @click="handleTagFilter(tag.value)"
                closable
                @close.stop="handleDeleteTag(tag)"
              >{{ tag.label }}</el-tag>
              <div class="add-tag-btn" @click="showAddTag = true">
                <el-icon size="16"><Plus /></el-icon>
              </div>
            </div>
          </div>
          <div class="filter-options">
            <el-radio-group v-model="mediaType" size="small" @change="handleTypeChange">
              <el-radio-button label="all">{{ t('Media.GridList.all') }}</el-radio-button>
              <el-radio-button label="image">{{ t('Media.GridList.image') }}</el-radio-button>
              <el-radio-button label="video">{{ t('Media.GridList.video') }}</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 添加标签对话框 -->
        <el-dialog 
          v-model="showAddTag" 
          :title="t('Media.GridList.add_tag')" 
          :close-on-click-modal="false"
          width="500px"
          class="el-dialog-common-cls"
        >
          <el-form :model="tagForm" label-position="top">
            <el-form-item :label="t('Media.GridList.tag_name')">
              <el-input v-model="tagForm.name" :placeholder="t('Media.GridList.enter_tag_name')"></el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button @click="showAddTag = false">{{ t('Media.GridList.cancel') }}</el-button>
              <el-button type="primary" @click="handleAddTag">{{ t('Media.GridList.confirm') }}</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 媒体网格区域 -->
        <div ref="mediaGridRef" class="media-grid" v-loading="loading" v-infinite-scroll="loadMore" :infinite-scroll-disabled="infiniteDisabled" :infinite-scroll-distance="10">
          <el-empty v-if="mediaList.length <= 0 && !loading" :description="t('Media.GridList.no_data')" />
          <div class="waterfall-container">
            <div 
              v-for="item in mediaList" 
              :key="item.id" 
              class="waterfall-item"
            >
              <div class="media-card">
                <div class="media-item">
                  <!-- 媒体预览 -->
                  <div class="media-preview">
                    <el-image 
                      :src="item.url" 
                      fit="cover"
                      :preview-src-list="[item.url]"
                      loading="lazy"
                      overflow="hidden"
                      border-radius="4px 4px 0 0"
                    >
                      <template #error>
                        <div class="image-error">
                          <el-icon style="font-size: 46px; color: #BDBDBD;"><img :src="$asset('Cms/Asset/no-photo.png')" /></el-icon>
                        </div>
                      </template>
                    </el-image>
                    
                    <!-- 悬浮操作按钮 -->
                    <div class="hover-options">
                      <div class="edit-option" @click.stop="handleEdit(item)">
                        <el-icon><Edit /></el-icon>
                      </div>
                      <div class="delete-option" @click.stop="handleDelete(item)">
                        <el-icon><Delete /></el-icon>
                      </div>
                      <div class="more-option" @click.stop="handleMore(item)">
                        <el-icon><More /></el-icon>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 媒体信息 -->
                  <div class="media-info">
                    <div class="media-name" :title="item.file_name">{{ item.file_name }}</div>
                    <div class="media-meta">
                      <span class="media-size">{{ item.file_size }}</span>
                      <span class="upload-time">{{ t('Media.GridList.uploaded_at') }}: {{ formatDate(item.created_at) }}</span>
                    </div>
                    <div class="media-tags">
                      <el-tag 
                        v-for="tag in item.tags?.slice(0, 3)" 
                        :key="tag"
                        size="small"
                      >
                        {{ tag }}
                      </el-tag>
                      <el-tooltip v-if="item.tags?.length > 3" class="item" placement="top">
                        <template #content>
                          <div>
                            <div v-for="tag in item.tags" :key="tag">
                              {{ tag }}
                            </div>
                          </div>
                          
                        </template>
                        <span class="tag-count">+{{ item.tags.length - 3 }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="loading && mediaList.length > 0" class="loading-more">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <span>{{ t('Media.GridList.loading') }}</span>
          </div>
          <div v-if="!hasMore && mediaList.length > 0" class="no-more">
            {{ t('Media.GridList.no_more_data') }}
          </div>
        </div>

        <!-- 返回顶部按钮 -->
        <el-backtop 
          :right="40" 
          :bottom="100" 
          target=".media-grid"
          :visibility-height="300"
        >
          <div class="back-to-top">
            <el-icon><Top /></el-icon>
          </div>
        </el-backtop>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed, nextTick } from 'vue'
import { 
  Upload, Edit, Delete, More, Picture, Search, Plus, ArrowDown, Loading, Top
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import http from '/admin/support/http'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

// 国际化
const { t } = useI18n()

// 定义媒体项接口
interface MediaItem {
  id: number;
  file_name: string;
  title: string | null;
  url: string;
  file_size: string;
  content_type: string;
  tags: string[];
  is_public: number;
  dir: string | null;
  updated_at: string;
  created_at: string;
}

// 修改搜索表单
const searchForm = reactive({
  search: undefined as string | undefined,
  sort: 1,
  page: 1,
  limit: 20, // 每页加载更多内容
  tags: [] as string[]
})

// 媒体类型
const mediaType = ref('all')

// 选中的标签
const selectedTags = ref<string[]>([])

// 标签选项
const tagOptions = ref<{ label: string; value: string; id?: number }[]>([])

// 添加标签相关变量
const showAddTag = ref(false)
const tagForm = reactive({
  name: ''
})

const router = useRouter()
// 列表数据
const loading = ref(false)
const mediaList = ref<MediaItem[]>([])
const currentPage = ref(1)
const pageSize = ref(20) // 每页加载更多内容
const total = ref(0)
const hasMore = ref(true)
const mediaGridRef = ref<HTMLElement | null>(null)
const infiniteDisabled = computed(() => loading.value || !hasMore.value)

// 获取媒体列表
const fetchMediaList = async (reset = true) => {
  if (reset) {
    // 重置时清空现有列表
    if (currentPage.value === 1) {
      mediaList.value = []
    }
  }
  
  loading.value = true
  try {
    const params = {
      search: searchForm.search,
      sort: searchForm.sort,
      page: currentPage.value,
      limit: pageSize.value,
      tags: [] as string[]
    }
    
    // 添加标签筛选
    if (selectedTags.value.length > 0) {
      params.tags = selectedTags.value
    }
    
    // 根据选择的类型调用不同的接口
    const endpoint = `/media/list/${mediaType.value}`
    
    const response = await http.get(endpoint, params) 
    const responseData = response.data
    
    if (responseData.code === 200) {
      // 直接使用返回的数据，避免多余的格式转换
      const newItems = responseData.data?.items || []
      
      if (reset && currentPage.value === 1) {
        mediaList.value = newItems
      } else {
        mediaList.value = [...mediaList.value, ...newItems]
      }
      
      total.value = responseData.data?.total || 0
      
      // 判断是否还有更多数据
      
      hasMore.value = mediaList.value.length < total.value
    } else {
      if (reset) {
        mediaList.value = []
      }
      total.value = 0
      hasMore.value = false
      ElMessage.warning(responseData.message || t('Media.GridList.no_data'))
    }
  } catch (error) {
    if (reset) {
      mediaList.value = []
    }
    total.value = 0
    hasMore.value = false
    ElMessage.error(t('Media.GridList.load_failed'))
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = async () => {
  if (loading.value || !hasMore.value) return
  
  currentPage.value++
  await fetchMediaList(false)
}

// 处理标签筛选
const handleTagFilter = (tag: string) => {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tag)
  }
  resetAndSearch()
}

// 处理标签批量筛选
const handleTagsFilter = (tags: string[]) => {
  selectedTags.value = tags
  resetAndSearch()
}

// 处理类型切换
const handleTypeChange = () => {
  resetAndSearch()
}

// 重置搜索并查询
const resetAndSearch = () => {
  currentPage.value = 1
  hasMore.value = true
  fetchMediaList()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  searchForm.search = searchForm.search || undefined
  resetAndSearch()
}

// 清除
const handleClear = () => {
  searchForm.search = undefined
  searchForm.sort = 1
  resetAndSearch()
}

// 处理排序
const handleSortChange = (val: number) => {
  searchForm.sort = val
  resetAndSearch()
}

// 处理上传类型选择
const handleUploadType = (type: string) => {
  const input = document.createElement('input')
  input.type = 'file'
  
  // 设置接受的文件类型
  switch (type) {
    case 'image':
      input.accept = 'image/*'
      break
    case 'video':
      input.accept = 'video/*'
      break
    case 'audio':
      input.accept = 'audio/*'
      break
    default:
      input.accept = '*'
  }
  
  // 监听文件选择事件
  input.onchange = async (event) => {
    const target = event.target as HTMLInputElement
    const files = target.files
    
    if (files && files.length > 0) {
      await uploadFile({ file: files[0] })
    }
  }
  
  // 触发文件选择
  input.click()
}

// 文件上传
const uploadFile = async (options: { file: File }) => {
  const { file } = options
  const acceptedFileTypes = ['image/*', 'video/*', 'audio/*']
  const isLt10M = file.size / 1024 / 1024 < 10

  // if (!acceptedFileTypes.includes(file.type)) {
  //   ElMessage.error('格式错误')
  //   return
  // }
  // if (!isLt10M) {
  //   ElMessage.error('文件大小超过10MB')
  //   return
  // }
  
  loading.value = true
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('dir', '/files')
    formData.append('mode', 'OVERWRITE')

    const response = await http.post('/media/upload', formData)
    if (response && response.data && response.data.data && response.data.data.file && response.data.data.file.url) {
      ElMessage.success(t('Media.GridList.upload_success'))
      resetAndSearch() // 刷新列表
    } else {
      ElMessage.error(response.data.message || t('Media.GridList.upload_failed'))
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}

// 编辑媒体
const handleEdit = (item: MediaItem) => {
  router.push({
    path: `/media/edit/${item.id}`,
    query: {
      returnTab: 'grid'
    }
  })
}

// 删除媒体
const handleDelete = async (item: MediaItem) => {
  try {
    await ElMessageBox.confirm(
      t('Media.GridList.delete_confirm'),
      t('Media.GridList.tip'),
      {
        confirmButtonText: t('Media.GridList.confirm'),
        cancelButtonText: t('Media.GridList.cancel'),
        type: 'warning'
      }
    )
    
    loading.value = true
    const response = await http.delete(`/media/${item.id}`)
    
    if (response.data.code === 200) {
      ElMessage.success(t('Media.GridList.delete_success'))
      
      // 高效更新列表：如果列表中只剩少量项目，则直接重新获取；否则只从本地列表移除
      if (mediaList.value.length <= 5) {
        // 列表项目很少，直接刷新整个列表
        resetAndSearch()
      } else {
        // 从现有列表中移除被删除的项目
        mediaList.value = mediaList.value.filter(media => media.id !== item.id)
        
        // 更新总数
        if (total.value > 0) {
          total.value--
        }
        
        // 如果列表项目较少了，可能需要获取更多数据填充
        if (hasMore.value && mediaList.value.length < 5) {
          loadMore()
        }
      }
    } else {
      ElMessage.error(response.data.message || t('Media.GridList.delete_failed'))
    }
  } catch (error) {
    // 用户取消删除，不做处理
  } finally {
    loading.value = false
  }
}

// 更多操作
const handleMore = (item: MediaItem) => {
  // 实现更多操作逻辑
}

// 格式化文件大小
const formatSize = (size: string | number) => {
  if (typeof size === 'string') {
    return size; // 如果已经是格式化的字符串，直接返回
  }
  
  if (size === 0) return '0B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return `${(size / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`
}

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

// 获取标签列表
const fetchTags = async () => {
  try {
    const response = await http.get('/media/tags')
    const responseData = response.data
    
    if (responseData.code === 200) {
      tagOptions.value = (responseData.data || []).map((tag: any) => ({
        label: tag.tag_name,
        value: tag.tag_name,
        id: tag.id
      }))
    } else {
      ElMessage.warning(responseData.message || t('Media.GridList.get_tags_failed'))
    }
  } catch (error) {
  }
}

// 添加标签
const handleAddTag = async () => {
  if (!tagForm.name.trim()) {
    ElMessage.warning(t('Media.GridList.tag_name_required'))
    return
  }
  
  try {
    const response = await http.post('/media/tags', {
      name: tagForm.name.trim()
    })
    
    if (response.data.code === 200) {
      ElMessage.success(t('Media.GridList.add_tag_success'))
      tagForm.name = ''
      showAddTag.value = false
      await fetchTags() // 重新获取标签列表
    } else {
      ElMessage.error(response.data.message || t('Media.GridList.add_tag_failed'))
    }
  } catch (error) {
  }
}

// 删除标签
const handleDeleteTag = async (tag: { id?: number; label: string; value: string }) => {
  if (!tag.id) {
    ElMessage.warning(t('Media.GridList.tag_id_not_exist'))
    return
  }
  
  ElMessageBox.confirm(
    t('Media.GridList.delete_tag_confirm', { tag: tag.label }),
    t('Media.GridList.tip'),
    {
      confirmButtonText: t('Media.GridList.confirm'),
      cancelButtonText: t('Media.GridList.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await http.delete(`/media/tags/${tag.id}`)
      
      if (response.data.code === 200) {
        ElMessage.success(t('Media.GridList.delete_tag_success'))
        // 从选中标签中移除
        if (selectedTags.value.includes(tag.value)) {
          const index = selectedTags.value.indexOf(tag.value)
          if (index > -1) {
            selectedTags.value.splice(index, 1)
          }
        }
        await fetchTags() // 重新获取标签列表
        resetAndSearch() // 重新获取媒体列表
      } else {
        ElMessage.error(response.data.message || t('Media.GridList.delete_tag_failed'))
      }
    } catch (error) {
    }
  }).catch(() => {
    // 取消删除，不做操作
  })
}

onMounted(() => {
  fetchMediaList()
  fetchTags()
})
</script>

<script lang="ts">
export default {
  name: 'gridList'
}
</script>

<style lang="scss" scoped>
.grid-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
  .tooltip-content {
    display: flex;
    flex-wrap: wrap;
    gap: 8px; 
    max-width: 200px;
    max-height: 200px;
    overflow: auto;
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(64, 158, 255, 0.3);
      border-radius: 3px;
      
      &:hover {
        background-color: rgba(64, 158, 255, 0.5);
      }
    }
  }
  .grid-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      padding: 0;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(64, 158, 255, 0.3);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(64, 158, 255, 0.5);
        }
      }

      .search-area {
        padding-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ebeef5;
        flex-shrink: 0;
        
        .search-left {
          flex: 1;
          display: flex;
          align-items: center;
          
          .el-form {
            display: flex;
            align-items: center;
            margin-left: 0;
            
            .el-form-item {
              margin-bottom: 0;
              margin-right: 8px;
             
            }
          }
        }
        
        .search-right {
          margin-left: 16px;
          display: flex;
          .upload-btn {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            font-weight: 500;
          }
        }
      }
      
      .tab-nav {
        display: flex;
        margin-bottom: 0;
        
        .tab-item {
          padding: 6px 16px 6px 0;
          cursor: pointer;
          position: relative;
          color: #606266;
          font-size: 14px;
          font-weight: 500;
          
          &.active {
            color: #409eff;
          }
        }
      }
      .filter-options {
      flex-shrink: 0;}
      .tags-filter {
        padding: 8px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ebeef5;
        
        .filter-group {
          display: flex;
          align-items: center;
          
          .filter-label {
            font-size: 14px;
            color: #606266;
            margin-right: 12px;
          }
          
          .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
            
            .el-tag {
              cursor: pointer;
              margin: 0;
              padding: 0 8px;
              height: 24px;
              line-height: 22px;
              
              &.active {
                background-color: #409eff;
                color: #fff;
                border-color: #409eff;
              }
            }
            
            .add-tag-btn {
              margin-left: 8px;
              height: 24px;
              width: 24px;
            }
          }
        }
        
        .filter-options {
          .el-radio-group {
            margin-left: 16px;
            
            .el-radio-button__inner {
              padding: 4px 12px;
            }
          }
        }
      }

      .media-grid {
        flex: 1;
        padding: 16px 10px 16px 0;
        overflow-y: auto;
        
        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        
        &::-webkit-scrollbar-thumb {
          background-color: rgba(64, 158, 255, 0.3);
          border-radius: 3px;
          
          &:hover {
            background-color: rgba(64, 158, 255, 0.5);
          }
        }

        .waterfall-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
          grid-gap: 20px;
          grid-auto-flow: dense;
        }

        .loading-more, .no-more {
          text-align: center;
          padding: 20px 0;
          color: #909399;
          font-size: 14px;
          
          .loading-icon {
            animation: rotating 2s linear infinite;
            margin-right: 5px;
          }
        }

        .media-card {
          margin-bottom: 0;
          
          .media-item {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            overflow: hidden;
            transition: all 0.3s;
            cursor: pointer;
            
            .media-preview {
              position: relative;
              padding-bottom: 75%;
              background-color: #f5f7fa;
              overflow: hidden;
              border-radius: 4px 4px 0 0;
              
              .el-image {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
              }

              .image-error {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                color: #BDBDBD;
                font-size: 14px;
              }
              
              .hover-options {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
                opacity: 0;
                transition: opacity 0.3s;
                z-index: 2;
                height: 100%;
                width: 100%;
                background-color: rgba(0, 0, 0, 0.1);
                
                .edit-option, .delete-option, .more-option {
                  width: 32px;
                  height: 32px;
                  border-radius: 50%;
                  background-color: rgba(255, 255, 255, 0.9);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;
                  transition: all 0.2s;
                  
                  &:hover {
                    background-color: #fff;
                    transform: scale(1.05);
                  }
                  
                  .el-icon {
                    font-size: 16px;
                    color: #606266;
                  }
                }
                
                .edit-option:hover .el-icon {
                  color: #409eff;
                }
                
                .delete-option:hover .el-icon {
                  color: #f56c6c;
                }
              }
              
              .media-actions {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s;
                
                .action-buttons {
                  display: flex;
                  gap: 8px;
                  
                  .el-button {
                    width: 36px;
                    height: 36px;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  }
                }
              }
            }

            .media-info {
              padding: 12px;
              
              .media-name {
                font-size: 14px;
                font-weight: 500;
                color: #303133;
                margin-bottom: 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .media-meta {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #909399;
                margin-bottom: 8px;
              }

              .media-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                min-height: 22px;
                max-height: 26px;
                overflow: hidden;

                .tag-count {
                  font-size: 12px;
                  color: #909399;
                  line-height: 22px;
                  margin-left: 4px;
                }
              }
            }
            
            &:hover {
              box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
              
              .media-actions {
                opacity: 1;
              }
              
              .hover-options {
                opacity: 1;
              }
            }

          }
        }
      }
    }
  }
}

.el-dropdown-link {
  cursor: pointer;
}

.back-to-top {
  height: 100%;
  width: 100%;
  box-shadow: 0 0 6px rgba(0,0,0,.12);
  text-align: center;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all .3s;
  border-radius: 50%;
  .el-icon {
    font-size: 20px;
    color: #dbdbdb;
    margin-top: 2px;
  }
  &:hover {
    .el-icon {
      color: #007ee5;
    }
  }
  
  
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
