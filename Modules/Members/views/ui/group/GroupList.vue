<template>
  <div class="bwms-module table-page">
    <div class="module-header">
       <el-button class="button-no-border" @click="handleImport">
        <el-icon><img src="/resources/admin/assets/icon/UploadIcon.png" alt="UploadIcon" /></el-icon>
        <span>{{ $t('Members.GroupList.import') }}</span>
      </el-button>
       <el-button class="button-no-border" @click="handleExport">
        <el-icon><img src="/resources/admin/assets/icon/DownloadIcon.png" alt="DownloadIcon" /></el-icon>
        <span>{{ $t('Members.GroupList.export') }}</span>
      </el-button>
      <!-- 筛选 -->
      <FilterPopover 
          v-model="showFilterDropdown"
        >
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :model="searchQuery" label-position="top">
            <el-form-item :label="$t('Members.GroupList.columns.groupName')">
              <el-input 
                v-model="searchQuery"
                :placeholder="$t('Members.GroupList.searchPlaceholder')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
       <el-button type="primary" @click="handleCreate">
        <el-icon><img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" /></el-icon>
        <span>{{ $t('Members.GroupList.createGroup') }}</span>
      </el-button>
    </div>

    <div class="module-con">
      <div class="box">
        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="group_name" :label="$t('Members.GroupList.columns.groupName')" min-width="280" />
          <!-- <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip /> -->
          <el-table-column prop="status" :label="$t('Members.GroupList.columns.status')" width="120">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="member_count" :label="$t('Members.GroupList.columns.memberCount')" width="200" />
          <el-table-column prop="updated_at" :label="$t('Members.GroupList.columns.lastUpdate')" width="300" />
          <el-table-column :label="$t('Members.GroupList.columns.actions')" width="240" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleEdit(row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button link type="primary" @click="handleCopy(row)">
                <el-icon><CopyDocument /></el-icon>
              </el-button>
              <el-button link type="primary" @click="handleDelete(row)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="currentPage"
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
    

    <!-- 添加导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      :title="$t('Members.GroupList.importDialog.title')"
      width="500px"
      class="el-dialog-common-cls"
    >
      <div class="import-container">
        <el-upload
          class="upload-demo"
          drag
          action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            {{ $t('Members.GroupList.importDialog.dragText') }}
          </div>
          <template #tip>
            <div class="el-upload__tip">
              {{ $t('Members.GroupList.importDialog.fileTip') }}
            </div>
          </template>
        </el-upload>
        
        <div class="template-download">
          <el-button type="primary" link @click="handleDownloadTemplate">
            {{ $t('Members.GroupList.importDialog.downloadTemplate') }}
          </el-button>
        </div>
      </div>
      <template #footer>
        <span class="flex justify-center">
          <el-button @click="importDialogVisible = false">{{ $t('Members.GroupList.importDialog.cancel') }}</el-button>
          <el-button 
            type="primary" 
            @click="handleUpload"
            :loading="uploading"
            :disabled="!selectedFile"
          >
            {{ $t('Members.GroupList.importDialog.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { env, getBaseUrl } from '/admin/support/helper'
// import http from '/admin/support/http'
import { Plus, Search, Edit, Delete, More, Upload, CopyDocument, Download } from '@element-plus/icons-vue'
import { memberGroupService } from '../../services/memberGroupService'
import type { UploadInstance, UploadFile } from 'element-plus'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

// 修改接口类型定义
interface IMemberGroup {
  id: number
  group_name: string // 修改为 group_name
  description: string
  status: number
  created_at: string
  updated_at: string
}

const router = useRouter()
const loading = ref(false)
const searchQuery = ref('')
const tableData = ref<IMemberGroup[]>([]) // 添加类型
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedRows = ref<IMemberGroup[]>([]) // 添加类型

// 添加上传相关的状态
const importDialogVisible = ref(false)
const uploadRef = ref<UploadInstance>()
const selectedFile = ref<UploadFile>()
const uploading = ref(false)

const showFilterDropdown = ref(false)

// 修改获取列表数据方法
const getList = async () => {
  loading.value = true
  try {
    const params = {
      keyword: searchQuery.value,
      page: currentPage.value,
      limit: pageSize.value
    }
    const { data } = await memberGroupService.getList(params)
    if (data.data) {
      tableData.value = data.data.list // 修改为 list
      total.value = data.data.total
    }
  } catch (error) {
    ElMessage.error(t('Members.GroupList.errors.fetchListFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getList()
  showFilterDropdown.value = false
}
// 重置
const handleReset = () => {
  searchQuery.value = ''
  currentPage.value = 1
  pageSize.value = 10
  getList()
  showFilterDropdown.value = false
}

// 分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  getList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getList()
}

// 选择
const handleSelectionChange = (val: IMemberGroup[]) => {
  selectedRows.value = val
}

// 新增
const handleCreate = () => {
  router.push('/members/group/create')
}

// 编辑
const handleEdit = (row: IMemberGroup) => {
  router.push(`/members/group/${row.id}/edit`)
}

// 复制
const handleCopy = async (row: IMemberGroup) => {
  try {
    loading.value = true
    await memberGroupService.copy(row.id)
    ElMessage.success(t('Members.GroupList.success.copySuccess'))
    getList()
  } catch (error) {
    ElMessage.error(t('Members.GroupList.errors.copyFailed'))
  } finally {
    loading.value = false
  }
}

// 删除
const handleDelete = (row: IMemberGroup) => {
  ElMessageBox.confirm(
    t('Members.GroupList.deleteConfirm.message'),
    t('Members.GroupList.deleteConfirm.title'),
    {
      confirmButtonText: t('Members.GroupList.deleteConfirm.confirm'),
      cancelButtonText: t('Members.GroupList.deleteConfirm.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      loading.value = true
      await memberGroupService.delete(row.id)
      ElMessage.success(t('Members.GroupList.success.deleteSuccess'))
      getList()
    } catch (error) {
      ElMessage.error(t('Members.GroupList.errors.deleteFailed'))
    } finally {
      loading.value = false
    }
  })
}

// 状态变更
const handleStatusChange = async (row: IMemberGroup) => {
  try {
    loading.value = true
    await memberGroupService.updateStatus(row.id, row.status)
    ElMessage.success(t('Members.GroupList.success.updateSuccess'))
  } catch (error) {
    ElMessage.error(t('Members.GroupList.errors.updateFailed'))
    row.status = row.status === 1 ? 0 : 1 // 恢复状态
  } finally {
    loading.value = false
  }
}
const baseURL = getBaseUrl()

// 文件选择改变
const handleFileChange = (uploadFile: UploadFile) => {
  selectedFile.value = uploadFile
}

// 导入按钮点击
const handleImport = () => {
  importDialogVisible.value = true
  selectedFile.value = undefined
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 处理文件上传
const handleUpload = async () => {
  if (!selectedFile.value?.raw) {
    ElMessage.warning(t('Members.GroupList.warnings.selectFile'))
    return
  }

  uploading.value = true
  try {
    await memberGroupService.import(selectedFile.value.raw)
    ElMessage.success(t('Members.GroupList.success.importSuccess'))
    importDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    ElMessage.error(t('Members.GroupList.errors.importFailed'))
  } finally {
    uploading.value = false
  }
}

// 批量导出
const handleExport = async () => {
  try {
    loading.value = true
    // 创建下载链接
    const response = `${baseURL}members/group-export?page=${currentPage.value}&limit=${pageSize.value}`
    const link = document.createElement('a')
    link.href = response
    // 添加文件名
    link.setAttribute('download', t('Members.GroupList.exportFileName'))
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success(t('Members.GroupList.success.exportSuccess'))
  } catch (error) {
    ElMessage.error(t('Members.GroupList.errors.exportFailed'))
  } finally {
    loading.value = false
  }
}

// 修改下载模板方法
const handleDownloadTemplate = async () => {
  try {
    loading.value = true
    const response = `${baseURL}members/group-import-template`
    const link = document.createElement('a')
    link.href = response
    // 添加文件名
    link.setAttribute('download', t('Members.GroupList.templateFileName'))
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success(t('Members.GroupList.success.templateDownloadSuccess'))
  } catch (error) {
    ElMessage.error(t('Members.GroupList.errors.templateDownloadFailed'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  .module-header {
    flex-shrink: 0; // 防止头部被压缩
  }
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      padding-top: 20px;
      flex: 1; // 让盒子占据剩余空间
      display: flex;
      flex-direction: column;
      overflow: auto; // 改为 auto，允许整个内容区域滚动

    }
  }
}

.import-container {
  padding: 10px;
  
  .upload-demo {
    margin-bottom: 15px;
  }
  
  .template-download {
    margin-top: 15px;
  }
}

.el-upload__text {
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.el-upload__tip {
  margin-top: 10px;
  color: #9E9E9E;
  font-size: 13px;
  width: 100%;
}
</style> 