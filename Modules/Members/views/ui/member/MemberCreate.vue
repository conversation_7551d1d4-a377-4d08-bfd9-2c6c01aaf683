<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      
    </div>
    <div class="module-con">
      <div class="box">
        <!-- Tab 切换 -->
        <el-tabs v-model="activeTab">
          <el-tab-pane :label="$t('Members.MemberCreate.tabs.typeFields')" name="type">
            <el-form 
              ref="typeFormRef"
              :model="typeForm"
              :rules="typeRules"
              label-position="top"
              v-loading="loading"
            >
              <!-- 基础信息 -->
              <div class="section">
                <!-- <h3 v-if="memberTypeInfo && memberTypeInfo.custom_field_details.length > 0">{{ memberTypeInfo?.type_name }}</h3> -->
                
                <!-- 动态生成表单字段 -->
                <div class="form-grid">
                  <template v-if="memberTypeInfo && memberTypeInfo.custom_field_details.length > 0">
                    <el-form-item 
                      v-for="field in memberTypeInfo.custom_field_details" 
                      :key="field.id"
                      :label="field.label"
                      :prop="field.name"
                      :required="field.is_required === 1"
                    >
                      <!-- 文本输入框 -->
                      <el-input 
                        v-if="field.type === 'text'"
                        v-model="typeForm[field.name]"
                        :placeholder="$t('Members.MemberCreate.placeholders.pleaseEnter')"
                      />
                      
                      <!-- 文本域 -->
                      <el-input 
                        v-else-if="field.type === 'textarea'"
                        v-model="typeForm[field.name]"
                        type="textarea"
                        :rows="3"
                        :placeholder="$t('Members.MemberCreate.placeholders.pleaseEnter')"
                      />
                      
                      <!-- 数字输入框 -->
                      <el-input-number 
                        v-else-if="field.type === 'number'"
                        v-model="typeForm[field.name]"
                        :placeholder="$t('Members.MemberCreate.placeholders.pleaseEnter')"
                        :controls="true"
                      />
                      
                      <!-- 日期选择器 -->
                      <el-date-picker 
                        v-else-if="field.type === 'date'"
                        v-model="typeForm[field.name]"
                        value-format="YYYY-MM-DD"
                        type="date"
                        :placeholder="$t('Members.MemberCreate.placeholders.pleaseSelect')"
                      />
                      
                      <!-- 下拉选择框 -->
                      <el-select 
                        v-else-if="field.type === 'select'"
                        v-model="typeForm[field.name]"
                        :placeholder="$t('Members.MemberCreate.placeholders.pleaseSelect')"
                        :filterable="field.is_searchable"
                        clearable
                        multiple
                        collapse-tags
                      >
                        <el-option 
                          v-for="(option, index) in field.options" 
                          :key="index" 
                          :label="option" 
                          :value="index"
                        />
                      </el-select>
                      
                      <!-- 单选按钮组 -->
                      <el-radio-group 
                        v-else-if="field.type === 'radio'"
                        v-model="typeForm[field.name]"
                      >
                        <el-radio 
                          v-for="(option, index) in field.options" 
                          :key="index" 
                          :label="index"
                        >
                          {{ option }}
                        </el-radio>
                      </el-radio-group>
                      
                      <!-- 复选框组 -->
                      <el-checkbox-group 
                        v-else-if="field.type === 'checkbox'"
                        v-model="typeForm[field.name]"
                      >
                        <el-checkbox 
                          v-for="(option, index) in field.options" 
                          :key="index" 
                          :label="index"
                        >
                          {{ option }}
                        </el-checkbox>
                      </el-checkbox-group>
                      
                      <!-- 默认情况 -->
                      <el-input 
                        v-else
                        v-model="typeForm[field.name]"
                        :placeholder="$t('Members.MemberCreate.placeholders.pleaseEnter')"
                      />
                    </el-form-item>
                  </template>
                </div>
              </div>

              <!-- 按钮组 -->
            <el-empty v-if="!memberTypeInfo || memberTypeInfo.custom_field_details.length === 0" :description="$t('Members.MemberCreate.noCustomFields')" image-size="100px" style="margin-bottom: 36px;" />
              
              <div class="form-buttons">
                <el-button class="button-cancel" @click="handleCancel">{{ $t('Members.MemberCreate.buttons.cancel') }}</el-button>
                <el-button @click="handleNext">{{ $t('Members.MemberCreate.buttons.next') }}</el-button>
              </div>
            </el-form>

          </el-tab-pane>

          <el-tab-pane :label="$t('Members.MemberCreate.tabs.profileInfo')" name="profile">
            <el-form 
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-position="top"
            >
              <!-- 基础信息 -->
              <div class="section">
                <div class="form-grid">
                  <el-form-item 
                    :label="$t('Members.MemberCreate.formItems.account')" 
                    prop="account"
                    required
                  >
                    <el-input 
                      v-model="profileForm.account"
                      :placeholder="$t('Members.MemberCreate.placeholders.pleaseEnterAccount')"
                    />
                  </el-form-item>

                  <el-form-item 
                    :label="$t('Members.MemberCreate.formItems.email')" 
                    prop="email"
                    required
                  >
                    <el-input 
                      v-model="profileForm.email"
                      :placeholder="$t('Members.MemberCreate.placeholders.pleaseEnterEmail')"
                      @blur="handleEmailBlur"
                    />
                  </el-form-item>

                  <!-- 根据是否老会员条件显示密码输入框 -->
                  <template v-if="!isExistingMember">
                    <el-form-item 
                      :label="$t('Members.MemberCreate.formItems.password')" 
                      prop="password"
                      required
                    >
                      <el-input 
                        v-model="profileForm.password"
                        type="password"
                        :placeholder="$t('Members.MemberCreate.placeholders.pleaseEnterPassword')"
                        show-password
                      />
                    </el-form-item>

                    <el-form-item 
                      :label="$t('Members.MemberCreate.formItems.confirmPassword')" 
                      prop="confirm_password"
                      required
                    >
                      <el-input 
                        v-model="profileForm.confirm_password"
                        type="password"
                        :placeholder="$t('Members.MemberCreate.placeholders.pleaseConfirmPassword')"
                        show-password
                      />
                    </el-form-item>
                  </template>

                  <el-form-item 
                    :label="$t('Members.MemberCreate.formItems.memberGroups')" 
                    prop="groups"
                    required
                  >
                    <el-select 
                      v-model="profileForm.groups"
                      :placeholder="$t('Members.MemberCreate.placeholders.pleaseSelectGroups')"
                      clearable
                      filterable
                      remote
                      multiple
                      collapse-tags
                      :remote-method="remoteSearch"
                      :loading="groupLoading"
                      @visible-change="handleVisibleChange"
                      class="group-select"
                    >
                      <el-option
                        v-for="group in memberGroups"
                        :key="group.id"
                        :label="group.group_name"
                        :value="group.id"
                      >
                        <div class="group-option">
                          <el-icon><User /></el-icon>
                          <span>{{ group.group_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item :label="$t('Members.MemberCreate.formItems.isActive')">
                    <el-switch
                      v-model="profileForm.is_active"
                      :active-value="true"
                      :inactive-value="false"
                    />
                  </el-form-item>

                  <el-form-item :label="$t('Members.MemberCreate.formItems.enable2fa')">
                    <el-switch
                      v-model="profileForm.enable_2fa"
                      :active-value="true"
                      :inactive-value="false"
                    />
                  </el-form-item>
                </div>
              </div>
              <!-- 按钮组 -->
              <div class="form-buttons">
                <el-button class="button-cancel" @click="handleCancel">{{ $t('Members.MemberCreate.buttons.cancel') }}</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
                  {{ $t('Members.MemberCreate.buttons.save') }}
                </el-button>
              </div>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Loading, CircleCheck } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { memberService, memberTypeService } from '../../services/memberService'
import { memberGroupService } from '../../services/memberGroupService'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const submitLoading = ref(false)
const activeTab = ref('type')

// 会员类型ID
const memberTypeId = ref<number>(0)

// 会员类型详情
interface CustomField {
  id: number
  name: string
  type: string
  label: string
  options: string[]
  is_required: number
  is_searchable: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface Permission {
  id: number
  type_id: number
  resource_id: number
  access_type: number
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface MemberTypeDetail {
  id: number
  type_name: string
  description: string
  is_default: number
  status: number
  custom_fields: number[]
  creator_id: number
  created_at: string
  updated_at: string
  custom_field_details: CustomField[]
  permissions: Permission[]
}

// 定义会员组接口
interface MemberGroup {
  id: number
  group_name: string
  description: string
  status: number
  created_at: string
  updated_at: string
  member_count?: number
}

const memberTypeInfo = ref<MemberTypeDetail | null>(null)

// 类型表单 - 动态生成
const typeFormRef = ref<FormInstance>()
const typeForm = reactive<Record<string, any>>({})

// 根据自定义字段生成验证规则
const typeRules = computed(() => {
  const rules: Record<string, any[]> = {}
  
  if (!memberTypeInfo.value?.custom_field_details) return rules
  
  memberTypeInfo.value.custom_field_details.forEach(field => {
    const fieldRules: any[] = []
    
    // 必填验证
    if (field.is_required === 1) {
      fieldRules.push({ 
        required: true, 
        message: t('Members.MemberCreate.validations.required', { 
          field: '', 
          action: field.type === 'select' || field.type === 'date' 
            ? t('Members.MemberCreate.actions.select') 
            : t('Members.MemberCreate.actions.enter') 
        }), 
        trigger: ['blur', 'change'] 
      })
    }
    
    // 根据类型添加验证规则
    if (field.type === 'number') {
      fieldRules.push({ 
        type: 'number', 
        message: t('Members.MemberCreate.validations.numberRequired'), 
        trigger: 'blur' 
      })
    } else if (field.type === 'date') {
      fieldRules.push({ 
        type: 'date', 
        message: t('Members.MemberCreate.validations.validDateRequired'), 
        trigger: 'change' 
      })
    }
    
    // 设置验证规则
    if (fieldRules.length > 0) {
      rules[field.name] = fieldRules
    }
  })
  
  return rules
})

// 会员组数据 - 现在使用接口获取的实际数据
const memberGroups = ref<MemberGroup[]>([])
const groupLoading = ref(false)
const selectedGroup = ref(false)

// 添加搜索查询状态
const searchQuery = ref('')
const isSelectOpen = ref(false)

// 添加是否为老会员的状态
const isExistingMember = ref(true)
const emailChecking = ref(false)

// 资料表单 - 修改字段名称以匹配接口
const profileFormRef = ref<FormInstance>()
const profileForm = reactive({
  account: '',
  email: '',
  password: '',
  confirm_password: '',
  groups: [] as number[],
  is_active: true,
  enable_2fa: false,
})

// 获取会员组列表
const fetchMemberGroups = async (keyword = '') => {
  groupLoading.value = true
  try {
    const params = {
      page: 1,
      limit: 50,  // 获取更多数据以展示所有可选会员组
      status: 1, // 只获取启用的会员组
      keyword: '' 
    }
    
    // 添加搜索关键词
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const { data } = await memberGroupService.getList(params)
    
    if (data.data && data.data.list) {
      memberGroups.value = data.data.list
    } else {
      memberGroups.value = []
      if (!keyword) {
        ElMessage.warning(t('Members.MemberCreate.warnings.noMemberGroups'))
      }
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberCreate.errors.fetchGroupsFailed'))
    memberGroups.value = []
  } finally {
    groupLoading.value = false
  }
}

// 处理下拉框可见性变化
const handleVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框打开时，如果已有数据就不重新请求
    isSelectOpen.value = true
    if (memberGroups.value.length === 0) {
      fetchMemberGroups()
    }
  } else {
    // 下拉框关闭时，清除搜索关键词并标记关闭状态
    isSelectOpen.value = false
    searchQuery.value = ''
    
    // 当下拉框关闭时，延迟一会儿再重新请求全量数据
    // 这样避免与用户可能的选择操作冲突
    setTimeout(() => {
      if (!isSelectOpen.value) {
        fetchMemberGroups()
      }
    }, 200)
  }
}

// 远程搜索方法
const remoteSearch = (query: string) => {
  if (!isSelectOpen.value) return
  
  searchQuery.value = query
  // 延迟200ms执行搜索，避免频繁请求
  // setTimeout(() => {
    if (query) {
      fetchMemberGroups(query)
    }
  // }, 200)
}

// 获取会员类型详情
const fetchMemberTypeDetail = async (id: number) => {
  loading.value = true
  try {
    const { data } = await memberTypeService.getDetail(id)
    if (data.data) {
      memberTypeInfo.value = data.data
      
      // 初始化表单字段
      initTypeFormFields(data.data.custom_field_details)
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberCreate.errors.fetchTypeDetailFailed'))
  } finally {
    loading.value = false
  }
}

// 初始化表单字段
const initTypeFormFields = (fields: CustomField[]) => {
  fields.forEach(field => {
    // 根据字段类型设置初始值
    if (field.type === 'checkbox') {
      typeForm[field.name] = []
    } else if (field.type === 'number') {
      typeForm[field.name] = 0
    } else if (field.type === 'date') {
      typeForm[field.name] = ''
    } else {
      typeForm[field.name] = ''
    }
  })
}

// 添加邮箱校验方法
const checkEmail = async (email: string) => {
  if (!email || !email.trim()) return
  
  try {
    emailChecking.value = true
    const { data } = await memberService.checkEmail(email)
    if (data.code === 200) {
      isExistingMember.value = data.data.exists || false
      
      if (isExistingMember.value) {
        // 如果是老会员，清空密码字段
        profileForm.password = ''
        profileForm.confirm_password = ''
        
        // 可以显示一个提示消息
        ElMessage.info(t('Members.MemberCreate.info.existingEmail'))
      }
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberCreate.errors.checkEmailFailed'))
    isExistingMember.value = false
  } finally {
    emailChecking.value = false
  }
}

// 添加对邮箱输入的监听
const handleEmailBlur = () => {
  if (profileForm.email && profileForm.email !== '') {
    checkEmail(profileForm.email)
  } else {
    isExistingMember.value = false
  }
}

// 资料表单验证规则 - 更新字段名和使用翻译
const profileRules = computed(() => ({
  account: [
    { required: true, message: t('Members.MemberCreate.validations.accountRequired'), trigger: 'blur' },
    { min: 2, max: 20, message: t('Members.MemberCreate.validations.accountLength'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('Members.MemberCreate.validations.emailRequired'), trigger: 'blur' },
    { type: 'email', message: t('Members.MemberCreate.validations.validEmailRequired'), trigger: 'blur' }
  ],
  password: [
    { 
      required: (rule: any, value: string, callback: (error?: Error) => void) => {
        // 如果是老会员，密码不是必填
        if (isExistingMember.value) {
          callback()
        } else {
          if (!value) {
            callback(new Error(t('Members.MemberCreate.validations.passwordRequired')))
          } else {
            callback()
          }
        }
      }, 
      trigger: 'blur' 
    },
    { min: 6, max: 20, message: t('Members.MemberCreate.validations.passwordLength'), trigger: 'blur' }
  ],
  confirm_password: [
    { 
      required: (rule: any, value: string, callback: (error?: Error) => void) => {
        // 如果是老会员，确认密码不是必填
        if (isExistingMember.value) {
          callback()
        } else {
          if (!value) {
            callback(new Error(t('Members.MemberCreate.validations.confirmPasswordRequired')))
          } else if (value !== profileForm.password) {
            callback(new Error(t('Members.MemberCreate.validations.passwordMismatch')))
          } else {
            callback()
          }
        }
      }, 
      trigger: 'blur' 
    }
  ],
  groups: [
    { required: true, message: t('Members.MemberCreate.validations.groupsRequired'), trigger: 'change' }
  ]
}))

// 取消
const handleCancel = () => {
  router.push('/members/vip')
}

// 下一步
const handleNext = async () => {
  if (!typeFormRef.value) return

  try {
    submitLoading.value = true
    await typeFormRef.value.validate()
    activeTab.value = 'profile'
  } catch (error) {
    // 验证失败
    ElMessage.error(t('Members.MemberCreate.errors.completeForm'))
  } finally {
    submitLoading.value = false
  }
}

// 提交表单函数 - 简化提交数据构建
const handleSubmit = async () => {
  if (!typeFormRef.value || !profileFormRef.value) return

  try {
    await typeFormRef.value.validate()
    await profileFormRef.value.validate()
    
    submitLoading.value = true
    
    // 准备自定义字段数据
    const customFieldsData: Record<string, any> = {}
    
    // 将typeForm中的数据转换为customFields需要的格式
    Object.keys(typeForm).forEach(key => {
      customFieldsData[key] = typeForm[key]
    })
    
    // 构建提交数据
    const submitData = {
      ...profileForm,
      member_type: memberTypeId.value,
      custom_fields: customFieldsData,
    } as any // 使用类型断言

    // 如果是老会员，不提交密码字段
    if (isExistingMember.value) {
      delete submitData.password
      delete submitData.confirm_password
    }

    await memberService.create(submitData)
    ElMessage.success(t('Members.MemberCreate.success.createSuccess'))
    router.push('/members/vip')
  } catch (error) {
    ElMessage.error(t('Members.MemberCreate.errors.createFailed'))
  } finally {
    submitLoading.value = false
  }
}

// 初始化
onMounted(() => {
  // 获取路由中的会员类型ID
  const typeId = route.query.typeId
  if (typeId) {
    memberTypeId.value = parseInt(typeId as string)
    fetchMemberTypeDetail(memberTypeId.value)
    // 获取会员组数据
    fetchMemberGroups()
  } else {
    ElMessage.warning(t('Members.MemberCreate.warnings.selectMemberType'))
    router.push('/members/vip')
  }
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      padding-top: 0;
      flex: 1; // 让盒子占据剩余空间
      display: flex;
      flex-direction: column;
      overflow: auto; // 允许内容区域滚动

      .section {
        margin-bottom: 30px;
        flex-shrink: 0;
        width: 100%;

        h3 {
          font-size: 16px;
          margin-bottom: 20px;
          font-weight: 500;
          padding-bottom: 10px;
          border-bottom: 1px solid #ebeef5;
        }
      }

      .group-option {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .group-preview {
        margin: 20px 0;
        
        .preview-box {
          background: #f5f7fa;
          border-radius: 4px;
          padding: 20px;

          h4 {
            font-size: 14px;
            margin-bottom: 15px;
            font-weight: normal;
          }

          .group-list {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .group-item {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 10px;
              background: #fff;
              border-radius: 4px;
              cursor: pointer;
              transition: all 0.3s;

              &:hover {
                background: #ecf5ff;
              }

              &.active {
                background: #ecf5ff;
                color: #409eff;
              }
            }
          }
        }
      }

      .form-buttons {
        display: flex;
        justify-content: center;
      }
    }
  }
}

/* 添加会员组下拉框的样式 */
:deep(.group-select) {
  .el-input__wrapper {
    padding-right: 30px;
  }
  
  .el-input__suffix {
    right: 5px;
  }
  
  .el-select__tags {
    max-width: calc(100% - 30px);
  }
}

.group-option {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.empty-text) {
  padding: 10px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 为下拉选项添加样式 */
:deep(.el-select-dropdown__item) {
  &.hover, &:hover {
    .group-option {
      color: #409eff;
    }
  }
  
  &.selected {
    .group-option {
      color: #409eff;
      font-weight: bold;
    }
  }
}

.email-tip {
  margin-left: 10px;
  font-size: 13px;
  color: #67C23A;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0 26px;
  width: 100%;

  :deep(.el-form-item) {
    
    .el-form-item__content {
      width: 100%;
      
      .el-input,
      .el-input-number,
      .el-date-picker,
      .el-select,
      .el-radio-group,
      .el-checkbox-group {
        width: 100%;
      }
    }
  }
}
</style> 