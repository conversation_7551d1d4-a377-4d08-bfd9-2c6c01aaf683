<template>
  <div class="table-page bwms-module">
    <div class="module-con scroll-bar-custom-transparent">
      <div class="member-detail">
        <!-- 左侧基本资料 -->
        <div class="detail-left">
          <div class="detail-box">
            <h3>{{ $t('Members.MemberDetail.basicInfo') }}</h3>
            <div class="detail-form">
              <div class="form-item">
                <label>{{ $t('Members.MemberDetail.fields.account') }}</label>
                <div class="value">{{ memberInfo.account || '--' }}</div>
              </div>
              <div class="form-item">
                <label>E-mail</label>
                <div class="value">{{ memberInfo.email || '--' }}</div>
              </div>
              <div class="form-item">
                <label>{{ $t('Members.MemberDetail.fields.phone') }}</label>
                <div class="value">{{ memberInfo.phone || '--' }}</div>
              </div>
              <div class="form-item">
                <label>{{ $t('Members.MemberDetail.fields.memberType') }}</label>
                <div class="value">{{ memberInfo.member_type_name || '--' }}</div>
              </div>
              <div class="form-item">
                <label>{{ $t('Members.MemberDetail.fields.memberGroup') }}</label>
                <div class="value">{{ formatGroupNames }}</div>
              </div>
              <div class="form-item">
                <label>{{ $t('Members.MemberDetail.fields.subscription') }}</label>
                <div class="value">{{ memberInfo.subscription_no || '--' }}</div>
              </div>
            </div>
          </div>
          <!-- 活动记录 -->
          <div class="activity-log">
            <h3>{{ $t('Members.MemberDetail.activityLog') }}</h3>
            <div class="log-list">
              <div v-for="(log, index) in memberInfo.action_logs || []" :key="index" class="log-item">
                <div class="log-time">{{ log.datetime }}</div>
                <div class="log-content">
                  <div class="log-title">
                    <div class="action">{{ log.description }}</div>
                    <template v-if="log.ip">
                      <div class="ip">{{ $t('Members.MemberDetail.ip') }}：{{ log.ip }}</div>
                    </template>
                    <template v-if="log.device">
                      <div class="device">{{ $t('Members.MemberDetail.device') }}：{{ log.device }}</div>
                    </template>
                  </div>
                  <template v-if="log.amount">
                    <div class="log-amount">{{ $t('Members.MemberDetail.amount') }}：{{ log.currency }} {{ log.amount }}/{{ log.period }}</div>
                  </template>
                  <!-- <div class="log-detail">
                    <el-button link type="primary" size="small" @click="viewLogDetail(log)">
                      {{ $t('Members.MemberDetail.viewDetails') }}
                    </el-button>
                  </div> -->
                </div>
              </div>
              <!-- 如果没有活动记录，显示提示信息 -->
              <div v-if="!memberInfo.action_logs || memberInfo.action_logs.length === 0" class="no-log">
                <el-empty :description="$t('Members.MemberDetail.noActivityLog')" image-size="100px" />
              </div>
              
            </div>
          </div>
        </div>

        <!-- 右侧状态信息 -->
        <div class="detail-right">
          <div class="user-card">
            <div class="avatar">
              <el-avatar class="user-avatar" :size="80" :src="memberInfo.photo || defaultAvatarUrl" @error="handleAvatarError($event)" />
            </div>
            <div class="user-info">
              <h4>{{ memberInfo.account || memberInfo.name || memberInfo.email || '--' }}</h4>
              <div class="status-buttons">
                <el-button 
                  type="primary" 
                  plain 
                  size="small" 
                  v-if="memberInfo.status !== 1"
                  :loading="statusLoading"
                  @click="handleUpdateStatus(1)"
                >
                  {{ $t('Members.MemberDetail.buttons.enable') }}
                </el-button>
                <el-button size="small" @click="handleDelete">{{ $t('Members.MemberDetail.buttons.deleteMember') }}</el-button>
                <!-- <div><el-button type="primary" plain size="small">重设密码</el-button></div> -->
                <!-- <div><el-button size="small" @click="handleDelete">{{ $t('Members.MemberDetail.buttons.deleteMember') }}</el-button></div> -->
              </div>
            </div>
            <div class="user-status">
              <div class="status-item">
                <label>{{ $t('Members.MemberDetail.fields.status') }}</label>
                <el-tag :type="getStatusType(memberInfo.status)">
                  {{ memberInfo.status_text || getStatusText(memberInfo.status) }}
                </el-tag>
              </div>
              <div class="status-item">
                <label>{{ $t('Members.MemberDetail.fields.lastLoginTime') }}</label>
                <span>{{ getLastLoginTime || '--' }}</span>
              </div>
              <!-- <div class="status-item">
                <label>登录尝试失败次数</label>
                <span>{{ formatDate(memberInfo.last_login_time) || '--' }}</span>
              </div>
              <div class="status-item">
                <label>密码最后修改日期</label>
                <span>{{ formatDate(memberInfo.password_updated_at) || '--' }}</span>
              </div> -->
              <div class="status-item">
                <label>{{ $t('Members.MemberDetail.fields.createdAt') }}</label>
                <span>{{ formatDate(memberInfo.created_at) || '--' }}</span>
              </div>
              <div class="status-item">
                <label>{{ $t('Members.MemberDetail.fields.enable2fa') }}</label>
                <el-switch
                  v-model="enable2fa"
                  disabled
                />
              </div>
              <div class="status-item">
                <label>{{ $t('Members.MemberDetail.fields.accountLocked') }}</label>
                <el-switch
                  v-model="isAccountLocked"
                  disabled
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { memberService } from '../../services/memberService'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

const route = useRoute()
const router = useRouter()
const memberId = route.params.id as string
const statusLoading = ref(false)

// 添加默认头像 URL
const defaultAvatarUrl = 'https://cube.elemecdn.com/3/7c/********************************.png'

// 会员信息
const memberInfo = ref({
  id: 0,
  user_id: 0,
  member_type_id: 0,
  subscription_no: null,
  plan_id: 0,
  create_method: 1,
  start_time: 0,
  end_time: 0,
  status: 1,
  remark: '',
  enable_2fa: 0,
  creator_id: 0,
  created_at: null,
  updated_at: null,
  is_active: 1,
  expired_at: 0,
  member_type_name: '',
  name: null,
  email: '',
  phone: null,
  gender: null,
  birthdate: null,
  account: '',
  group_ids: [] as number[],
  group_names: [] as string[],
  custom_fields: {} as Record<string, any>,
  action_logs: [] as Array<{
    datetime: string;
    type: string;
    description: string;
    ip?: string;
    device?: string;
    amount?: string;
    currency?: string;
    period?: string;
  }>,
  status_text: '',
  // 以下字段在接口中可能不存在，但为了兼容组件需要
  photo: '',
  last_login_time: null,
  password_updated_at: null
})

// 定义会员状态枚举
enum MemberStatus {
  ACTIVE = 1,
  DISABLED = 2,
  SUSPENDED = 3,
  PENDING = 4
}

// 计算属性：是否启用2FA
const enable2fa = computed(() => {
  return memberInfo.value.enable_2fa === 1
})

// 计算属性：账户是否锁定
const isAccountLocked = computed(() => {
  // 假设状态为2或3时表示账户被锁定，根据实际情况调整
  return memberInfo.value.status === 2 || memberInfo.value.status === 3
})

// 格式化群组
const formatGroupNames = computed(() => {
  if (memberInfo.value.group_names && memberInfo.value.group_names.length > 0) {
    return memberInfo.value.group_names.join(', ')
  }
  if (memberInfo.value.group_ids && memberInfo.value.group_ids.length > 0) {
    return memberInfo.value.group_ids.join(', ')
  }
  return '--'
})

// 获取最后登录时间
const getLastLoginTime = computed(() => {
  if (memberInfo.value.action_logs && memberInfo.value.action_logs.length > 0) {
    // 找出类型为login的最新记录
    const loginLogs = memberInfo.value.action_logs.filter(log => log.type === 'login')
    if (loginLogs.length > 0) {
      return loginLogs[0].datetime
    }
  }
  return formatDate(memberInfo.value.last_login_time)
})

// 格式化数组值显示
const formatArrayValue = (arr: any[]) => {
  if (!arr || !Array.isArray(arr)) return '--'
  return arr.join(', ')
}

// 获取状态标签类型
const getStatusType = (status: number) => {
  const types: Record<number, string> = {
    [MemberStatus.ACTIVE]: 'success',
    [MemberStatus.DISABLED]: 'danger',
    [MemberStatus.SUSPENDED]: 'warning',
    [MemberStatus.PENDING]: 'info'
  }
  return types[status] || ''
}

// 获取状态文本
const getStatusText = (status: number) => {
  return t(`Members.MemberDetail.status.${status}`) || t('Members.MemberDetail.status.unknown')
}

// 格式化日期
const formatDate = (date: string | number | null) => {
  if (!date) return '--'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 获取会员详情
const getMemberDetail = async () => {
  try {
    const response = await memberService.getDetail(Number(memberId))
    if (response.data && response.data.code === 200 && response.data.data) {
      memberInfo.value = response.data.data
    } else {
      ElMessage.error(t('Members.MemberDetail.errors.fetchDetailFailed'))
    }
  } catch (error) {
    console.error('获取会员详情失败:', error)
    ElMessage.error(t('Members.MemberDetail.errors.fetchDetailFailed'))
  }
}

// 更新用户状态
const handleUpdateStatus = async (status: number) => {
  const actionText = status === 1 ? t('Members.MemberDetail.actions.enable') : t('Members.MemberDetail.actions.disable')
  
  try {
    await ElMessageBox.confirm(
      t('Members.MemberDetail.confirmMessages.updateStatus', { action: actionText }), 
      t('Members.MemberDetail.confirmTitles.prompt'), 
      {
        confirmButtonText: t('Members.MemberDetail.buttons.confirm'),
        cancelButtonText: t('Members.MemberDetail.buttons.cancel'),
        type: 'warning'
      }
    )
    
    statusLoading.value = true
    await memberService.updateStatus(Number(memberId), status)
    
    ElMessage.success(t('Members.MemberDetail.success.statusUpdated', { action: actionText }))
    memberInfo.value.status = status
    memberInfo.value.status_text = getStatusText(status)
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(t('Members.MemberDetail.errors.updateStatusFailed', { action: actionText }))
    }
  } finally {
    statusLoading.value = false
  }
}

// 查看日志详情
const viewLogDetail = (log: any) => {
  // TODO: 实现查看日志详情的逻辑
  console.log('查看日志详情:', log)
}

// 处理头像加载失败
const handleAvatarError = (e: Event) => {
  const imgElement = e.target as HTMLImageElement
  imgElement.src = defaultAvatarUrl
}

// 删除会员
const handleDelete = () => {
  ElMessageBox.confirm(
    t('Members.MemberDetail.confirmMessages.deleteMember'),
    t('Members.MemberDetail.confirmTitles.delete'),
    {
      confirmButtonText: t('Members.MemberDetail.buttons.confirm'),
      cancelButtonText: t('Members.MemberDetail.buttons.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      const { data } = await memberService.delete(memberInfo.value.id)
      if (data.code === 200) {
        ElMessage.success(t('Members.MemberDetail.success.deleteMember'))
        // 返回到会员列表页面
        router.push('/members/list')
      } else {
        ElMessage.error(data.message || t('Members.MemberDetail.errors.deleteFailed'))
      }
    } catch (error) {
      ElMessage.error(t('Members.MemberDetail.errors.deleteFailed'))
    }
  }).catch(() => {
    ElMessage.info(t('Members.MemberDetail.info.deleteCancelled'))
  })
}

onMounted(() => {
  getMemberDetail()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  height: auto;
  margin-top: 12px;

  &::-webkit-scrollbar-thumb {
    background-color: rgb(0, 126, 229, 0.3);
    border-radius: 5px;
    background-clip: content-box;
  }
  
  .module-con {
    height: auto;
    overflow: visible;
    
    .member-detail {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      flex-wrap: wrap; /* 允许在小屏幕上换行 */

      .detail-left {
        flex: 1;
        min-width: 300px; /* 确保有最小宽度 */
        
        .detail-box {
          background: #fff;
          border-radius: 4px;
          padding: 20px;
          margin-bottom: 20px;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

          h3 {
            font-size: 16px;
            margin-bottom: 20px;
            font-weight: 500;
            padding-bottom: 20px;
            border-bottom: 1px solid #ebeef5;
            color: #000;
          }

          .custom-field-title {
            font-size: 16px;
            margin: 15px 0;
            font-weight: 500;
            color: #000;
          }

          .detail-form {
            font-size: 16px;
            .form-item {
              display: flex;
              margin-bottom: 16px;
              flex-wrap: wrap; /* 允许在小屏幕上换行 */

              label {
                width: 120px;
                color: #000;
                padding-right: 10px;
              }

              .value {
                flex: 1;
                color: #000;
                min-width: 200px; /* 确保值有足够的显示空间 */
                word-break: break-word; /* 允许长文本换行 */
              }
            }
          }
        }
      }

      .detail-right {
        width: 300px;

        .user-card {
          background: #fff;
          border-radius: 4px;
          padding: 20px;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

          .avatar {
            flex-shrink: 0;
            text-align: center;
            margin-bottom: 16px;
          }
          .user-avatar {
            flex-shrink: 0;
            object-fit: cover;
            background-image: url(https://cube.elemecdn.com/3/7c/********************************.png);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
          }

          .user-info {
            text-align: center;
            margin-bottom: 20px;

            h4 {
              font-size: 16px;
              margin-bottom: 16px;
              word-break: break-word; /* 允许长用户名换行 */
            }

            .status-buttons {
              display: flex;
              justify-content: center;
            }
          }

          .user-status {
            .status-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
              flex-wrap: wrap; /* 允许在小屏幕上换行 */

              label {
                color: #000;
                margin-right: 10px;
              }

              span {
                color: #000;
                word-break: break-word; /* 允许长文本换行 */
              }
            }
          }
        }
      }
    }

    .activity-log {
      background: #fff;
      border-radius: 4px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      h3 {
        font-size: 16px;
        margin-bottom: 20px;
        font-weight: 500;
        padding-bottom: 20px;
        border-bottom: 1px solid #ebeef5;
      }

      .log-list {
        .log-item {
          display: flex;
          gap: 20px;
          padding: 16px 0;
          border-bottom: 1px solid #ebeef5;
          flex-wrap: wrap; /* 允许在小屏幕上换行 */

          &:last-child {
            border-bottom: none;
          }

          .log-time {
            width: 150px;
            color: #000;
          }

          .log-content {
            flex: 1;
            min-width: 200px; /* 确保内容有足够的显示空间 */

            .log-title {
              margin-bottom: 8px;
              display: flex;
              flex-wrap: wrap; /* 允许在小屏幕上换行 */
              gap: 8px;

              .action {
                margin-right: 16px;
                font-weight: 500;
              }

              .ip, .device {
                color: #000;
                margin-right: 16px;
                word-break: break-word; /* 允许长文本换行 */
              }
            }

            .log-amount {
              color: #67c23a;
              margin-bottom: 8px;
            }
          }
        }
        
        .no-log {
          font-size: 16px;
          text-align: center;
          padding: 20px 0;
          color: #000;
        }
      }
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .bwms-module {
    .module-con {
      .member-detail {
        flex-direction: column;
        
        .detail-right {
          width: 100%;
        }
      }
      
      .activity-log {
        .log-list {
          .log-item {
            flex-direction: column;
            
            .log-time {
              width: 100%;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }
}
</style> 