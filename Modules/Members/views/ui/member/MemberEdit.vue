<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    </div>
    <div class="module-con">
      <div class="box">
        <!-- Tab 切换 -->
        <el-tabs v-model="activeTab">
          <el-tab-pane :label="$t('Members.MemberEdit.tabs.typeFields')" name="type">
            <el-form 
              ref="typeFormRef"
              :model="typeForm"
              :rules="typeRules"
              label-position="top"
              v-loading="loading"
            >
              <!-- 基础信息 -->
              <div class="section">
                <!-- <h3 v-if="memberTypeInfo && memberTypeInfo.custom_field_details.length > 0">{{ memberTypeInfo?.type_name }}</h3> -->
                
                <!-- 动态生成表单字段 -->
                <div class="form-grid">
                  <template v-if="memberTypeInfo && memberTypeInfo.custom_field_details.length > 0">
                    <el-form-item 
                      v-for="field in memberTypeInfo.custom_field_details" 
                      :key="field.id"
                      :label="field.label"
                      :prop="field.name"
                      :required="field.is_required === 1"
                    >
                      <!-- 文本输入框 -->
                      <el-input 
                        v-if="field.type === 'text'"
                        v-model="typeForm[field.name]"
                        :placeholder="$t('Members.MemberEdit.placeholders.pleaseEnter')"
                      />
                      
                      <!-- 文本域 -->
                      <el-input 
                        v-else-if="field.type === 'textarea'"
                        v-model="typeForm[field.name]"
                        type="textarea"
                        :rows="3"
                        :placeholder="$t('Members.MemberEdit.placeholders.pleaseEnter')"
                      />
                      
                      <!-- 数字输入框 -->
                      <el-input-number 
                        v-else-if="field.type === 'number'"
                        v-model="typeForm[field.name]"
                        :placeholder="$t('Members.MemberEdit.placeholders.pleaseEnter')"
                        :controls="true"
                      />
                      
                      <!-- 日期选择器 -->
                      <el-date-picker 
                        v-else-if="field.type === 'date'"
                        v-model="typeForm[field.name]"
                        value-format="YYYY-MM-DD"
                        type="date"
                        :placeholder="$t('Members.MemberEdit.placeholders.pleaseSelect')"
                      />
                      
                      <!-- 下拉选择框 -->
                      <el-select 
                        v-else-if="field.type === 'select'"
                        v-model="typeForm[field.name]"
                        :placeholder="$t('Members.MemberEdit.placeholders.pleaseSelect')"
                        :filterable="field.is_searchable"
                        clearable
                        multiple
                        collapse-tags
                      >
                        <el-option 
                          v-for="(option, index) in field.options" 
                          :key="index" 
                          :label="option" 
                          :value="index"
                        />
                      </el-select>
                      
                      <!-- 单选按钮组 -->
                      <el-radio-group 
                        v-else-if="field.type === 'radio'"
                        v-model="typeForm[field.name]"
                      >
                        <el-radio 
                          v-for="(option, index) in field.options" 
                          :key="index" 
                          :label="index"
                        >
                          {{ option }}
                        </el-radio>
                      </el-radio-group>
                      
                      <!-- 复选框组 -->
                      <el-checkbox-group 
                        v-else-if="field.type === 'checkbox'"
                        v-model="typeForm[field.name]"
                      >
                        <el-checkbox 
                          v-for="(option, index) in field.options" 
                          :key="index" 
                          :label="index"
                        >
                          {{ option }}
                        </el-checkbox>
                      </el-checkbox-group>
                      
                      <!-- 默认情况 -->
                      <el-input 
                        v-else
                        v-model="typeForm[field.name]"
                        :placeholder="$t('Members.MemberEdit.placeholders.pleaseEnter')"
                      />
                    </el-form-item>
                  </template>
                </div>
              </div>

              <el-empty v-if="!memberTypeInfo || memberTypeInfo.custom_field_details.length === 0" :description="$t('Members.MemberEdit.noCustomFields')" image-size="100px" style="margin-bottom: 36px;" />

              <!-- 按钮组 -->
              <div class="form-buttons">
                <el-button class="button-cancel" @click="handleCancel">{{ $t('Members.MemberEdit.buttons.cancel') }}</el-button>
                <el-button @click="handleNext" type="primary">{{ $t('Members.MemberEdit.buttons.next') }}</el-button>
              </div>
            </el-form>
          </el-tab-pane>

          <el-tab-pane :label="$t('Members.MemberEdit.tabs.profileInfo')" name="profile">
            <el-form 
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-position="top"
            >
              <!-- 基础信息 -->
              <div class="section">
                <div class="form-grid">
                  <el-form-item 
                    :label="$t('Members.MemberEdit.formItems.account')"
                    prop="account"
                    required
                  >
                    <el-input 
                      v-model="profileForm.account"
                      :placeholder="$t('Members.MemberEdit.placeholders.pleaseEnterAccount')"
                      disabled
                    />
                  </el-form-item>

                  <el-form-item 
                    :label="$t('Members.MemberEdit.formItems.email')"
                    prop="email"
                    required
                  >
                    <el-input 
                      v-model="profileForm.email"
                      :placeholder="$t('Members.MemberEdit.placeholders.pleaseEnterEmail')"
                      disabled
                    />
                  </el-form-item>

                  <el-form-item 
                    :label="$t('Members.MemberEdit.formItems.memberGroups')"
                    prop="groups"
                    required
                  >
                    <el-select 
                      v-model="profileForm.groups"
                      :placeholder="$t('Members.MemberEdit.placeholders.pleaseSelectGroups')"
                      multiple
                      collapse-tags
                      clearable
                      filterable
                      remote
                      :remote-method="remoteSearch"
                      :loading="groupLoading"
                      @visible-change="handleVisibleChange"
                      class="group-select"
                    >
                      <el-option
                        v-for="group in memberGroups"
                        :key="group.id"
                        :label="group.group_name"
                        :value="group.id"
                      >
                        <div class="group-option">
                          <el-icon><User /></el-icon>
                          <span>{{ group.group_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item :label="$t('Members.MemberEdit.formItems.isActive')">
                    <el-switch
                      v-model="profileForm.is_active"
                      :active-value="true"
                      :inactive-value="false"
                    />
                  </el-form-item>

                  <el-form-item :label="$t('Members.MemberEdit.formItems.enable2fa')">
                    <el-switch
                      v-model="profileForm.enable_2fa"
                      :active-value="true"
                      :inactive-value="false"
                    />
                  </el-form-item>
                </div>
              </div>

              <!-- 按钮组 -->
              <div class="form-buttons">
                <el-button class="button-cancel" @click="handleCancel">{{ $t('Members.MemberEdit.buttons.cancel') }}</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
                  {{ $t('Members.MemberEdit.buttons.save') }}
                </el-button>
              </div>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { memberService, memberTypeService } from '../../services/memberService'
import { memberGroupService } from '../../services/memberGroupService'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const submitLoading = ref(false)
const activeTab = ref('type')

// 使用国际化
const { t } = useI18n()

// 会员ID
const memberId = ref<number>(0)
// 会员类型ID
const memberTypeId = ref<number>(0)

// 会员类型详情
interface CustomField {
  id: number
  name: string
  type: string
  label: string
  options: string[]
  is_required: number
  is_searchable: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface Permission {
  id: number
  type_id: number
  resource_id: number
  access_type: number
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface MemberTypeDetail {
  id: number
  type_name: string
  description: string
  is_default: number
  status: number
  custom_fields: number[]
  creator_id: number
  created_at: string
  updated_at: string
  custom_field_details: CustomField[]
  permissions: Permission[]
}

// 定义会员组接口
interface MemberGroup {
  id: number
  group_name: string
  description: string
  status: number
  created_at: string
  updated_at: string
  member_count?: number
}

// 更新会员详情接口定义，匹配新的数据格式
interface IMemberDetail {
  id: number
  user_id: number
  member_type_id: number
  subscription_no: string | null
  plan_id: number
  create_method: number
  start_time: number
  end_time: number
  status: number
  remark: string
  enable_2fa: number
  creator_id: number
  created_at: string
  updated_at: string
  is_active: number
  member_type_name: string
  name: string | null
  email: string
  phone: string | null
  gender: string | null
  birthdate: string | null
  account: string       // 账号直接从此字段获取
  group_ids: number[]   // 会员组 ID 列表
  custom_fields: Record<string, any>
}

const memberTypeInfo = ref<MemberTypeDetail | null>(null)
const memberInfo = ref<IMemberDetail | null>(null)

// 类型表单 - 动态生成
const typeFormRef = ref<FormInstance>()
const typeForm = reactive<Record<string, any>>({})

// 根据自定义字段生成验证规则
const typeRules = computed(() => {
  const rules: Record<string, any[]> = {}
  
  if (!memberTypeInfo.value?.custom_field_details) return rules
  
  memberTypeInfo.value.custom_field_details.forEach(field => {
    const fieldRules: any[] = []
    
    // 必填验证
    if (field.is_required === 1) {
      fieldRules.push({ 
        required: true, 
        message: t('Members.MemberEdit.validations.required', { 
          field: '', 
          action: field.type === 'select' || field.type === 'date' 
            ? t('Members.MemberEdit.actions.select') 
            : t('Members.MemberEdit.actions.enter') 
        }), 
        trigger: ['blur', 'change'] 
      })
    }
    
    // 根据类型添加验证规则
    if (field.type === 'number') {
      fieldRules.push({ 
        type: 'number', 
        message: t('Members.MemberEdit.validations.numberRequired'), 
        trigger: 'blur' 
      })
    } else if (field.type === 'date') {
      fieldRules.push({ 
        type: 'date', 
        message: t('Members.MemberEdit.validations.validDateRequired'), 
        trigger: 'change' 
      })
    }
    
    // 设置验证规则
    if (fieldRules.length > 0) {
      rules[field.name] = fieldRules
    }
  })
  
  return rules
})

// 会员组数据
const memberGroups = ref<MemberGroup[]>([])
const groupLoading = ref(false)

// 添加搜索查询状态
const searchQuery = ref('')
const isSelectOpen = ref(false)

// 资料表单
const profileFormRef = ref<FormInstance>()
const profileForm = reactive({
  account: '',  // 对应 account 字段
  email: '',    // 对应 email 字段
  groups: [] as number[],  // 修改为数组，对应 group_ids 字段
  is_active: true,  // 对应 is_active 字段 (0/1 -> false/true)
  enable_2fa: false, // 对应 enable_2fa 字段 (0/1 -> false/true)
})

// 资料表单验证规则
const profileRules = computed(() => ({
  account: [
    { required: true, message: t('Members.MemberEdit.validations.accountRequired'), trigger: 'blur' },
    { min: 4, max: 20, message: t('Members.MemberEdit.validations.accountLength'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('Members.MemberEdit.validations.emailRequired'), trigger: 'blur' },
    { type: 'email', message: t('Members.MemberEdit.validations.validEmailRequired'), trigger: 'blur' }
  ],
  groups: [
    { required: true, message: t('Members.MemberEdit.validations.groupsRequired'), trigger: 'change' }
  ]
}))

// 获取会员组列表
const fetchMemberGroups = async (keyword = '') => {
  groupLoading.value = true
  try {
    const params = {
      page: 1,
      limit: 50,
      status: 1,
      keyword: ''
    }
    
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const { data } = await memberGroupService.getList(params)
    
    if (data.data && data.data.list) {
      memberGroups.value = data.data.list
    } else {
      memberGroups.value = []
      if (!keyword) {
        ElMessage.warning(t('Members.MemberEdit.warnings.noMemberGroups'))
      }
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberEdit.errors.fetchGroupsFailed'))
    memberGroups.value = []
  } finally {
    groupLoading.value = false
  }
}

// 处理下拉框可见性变化
const handleVisibleChange = (visible: boolean) => {
  if (visible) {
    isSelectOpen.value = true
    if (memberGroups.value.length === 0) {
      fetchMemberGroups()
    }
  } else {
    isSelectOpen.value = false
    searchQuery.value = ''
    
    setTimeout(() => {
      if (!isSelectOpen.value) {
        fetchMemberGroups()
      }
    }, 200)
  }
}

// 远程搜索方法
const remoteSearch = (query: string) => {
  if (!isSelectOpen.value) return
  
  searchQuery.value = query
  if (query) {
    fetchMemberGroups(query)
  }
}

// 获取会员类型详情
const fetchMemberTypeDetail = async (id: number) => {
  loading.value = true
  try {
    const { data } = await memberTypeService.getDetail(id)
    if (data.data) {
      memberTypeInfo.value = data.data
      
      // 初始化表单字段
      initTypeFormFields(data.data.custom_field_details)
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberEdit.errors.fetchTypeDetailFailed'))
  } finally {
    loading.value = false
  }
}

// 初始化表单字段
const initTypeFormFields = (fields: CustomField[]) => {
  fields.forEach(field => {
    // 只有当字段不存在时才初始化
    if (typeForm[field.name] === undefined) {
      // 根据字段类型设置初始值
      if (field.type === 'checkbox') {
        typeForm[field.name] = []
      } else if (field.type === 'number') {
        typeForm[field.name] = 0
      } else if (field.type === 'date') {
        typeForm[field.name] = ''
      } else {
        typeForm[field.name] = ''
      }
    }
  })
}

// 取消
const handleCancel = () => {
  router.push('/members/vip')
}

// 下一步
const handleNext = async () => {
  if (!typeFormRef.value) return

  try {
    submitLoading.value = true
    await typeFormRef.value.validate()
    activeTab.value = 'profile'
  } catch (error) {
    ElMessage.error(t('Members.MemberEdit.errors.completeForm'))
  } finally {
    submitLoading.value = false
  }
}

// 添加会员详情获取方法
const fetchMemberDetail = async (id: number) => {
  loading.value = true
  try {
    const { data } = await memberService.getDetail(id)
    if (data.code === 200 && data.data) {
      memberInfo.value = data.data
      
      // 设置会员类型ID，并获取会员类型详情
      memberTypeId.value = data.data.member_type_id
      await fetchMemberTypeDetail(data.data.member_type_id)
      
      // 填充表单数据
      populateFormData(data.data)
      
    } else {
      ElMessage.error(t('Members.MemberEdit.errors.fetchMemberDetailFailed'))
      router.push('/members/vip')
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberEdit.errors.fetchMemberDetailFailed'))
    router.push('/members/vip')
  } finally {
    loading.value = false
  }
}

// 修改填充表单数据的方法，确保正确处理多选会员组
const populateFormData = (memberData: IMemberDetail) => {
  // 填充会员资料表单
  // 直接使用 account 字段
  profileForm.account = memberData.account || ''
  profileForm.email = memberData.email || ''
  
  // 设置状态 - 将 0/1 转换为 boolean
  profileForm.is_active = memberData.is_active === 1
  profileForm.enable_2fa = memberData.enable_2fa === 1
  
  // 处理会员组数据 - 直接使用 group_ids 数组
  if (memberData.group_ids && memberData.group_ids.length > 0) {
    profileForm.groups = memberData.group_ids
    
    // 确保会员组数据已加载
    if (memberGroups.value.length === 0) {
      // 需要从API获取会员组名称
      fetchMemberGroups()
    }
  }
  
  // 如果有自定义字段数据，直接填充到 typeForm
  if (memberData.custom_fields) {
    Object.keys(memberData.custom_fields).forEach(key => {
      typeForm[key] = memberData.custom_fields[key]
    })
  }
}

// 更新提交表单函数，处理多选会员组
const handleSubmit = async () => {
  if (!profileFormRef.value) return

  try {
    await profileFormRef.value.validate()
    
    submitLoading.value = true
    
    // 构建提交数据
    const submitData: Record<string, any> = {
      // 使用 account 字段作为账号
      account: profileForm.account,
      email: profileForm.email,
      member_type: memberTypeId.value,
      groups: profileForm.groups,
      // 将布尔值转换为 0/1
      is_active: profileForm.is_active,
      enable_2fa: profileForm.enable_2fa,
      custom_fields: {} as Record<string, any>
    }
    
    
    // 添加自定义字段数据
    Object.keys(typeForm).forEach(key => {
      submitData.custom_fields[key] = typeForm[key]
    })
    
    // 调用更新接口
    await memberService.update(memberId.value, submitData)
    ElMessage.success(t('Members.MemberEdit.success.updateSuccess'))
    
    router.push('/members/vip')
  } catch (error) {
    ElMessage.error(t('Members.MemberEdit.errors.updateFailed'))
  } finally {
    submitLoading.value = false
  }
}

// 初始化
onMounted(() => {
  // 获取路由参数
  const id = route.params.id
  
  if (!id) {
    ElMessage.warning(t('Members.MemberEdit.warnings.memberIdRequired'))
    router.push('/members/vip')
    return
  }
  
  memberId.value = parseInt(id as string)
  
  // 获取会员详情
  fetchMemberDetail(memberId.value)
  
  // 获取会员组数据
  fetchMemberGroups()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    padding: 0 20px;
    
    h2 {
      margin: 0;
      padding: 15px 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      padding-top: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;

      .section {
        margin-bottom: 30px;
        flex-shrink: 0;
        width: 100%;

        h3 {
          font-size: 16px;
          margin-bottom: 20px;
          font-weight: 500;
          padding-bottom: 10px;
          border-bottom: 1px solid #ebeef5;
        }
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0 26px;
        width: 100%;

        :deep(.el-form-item) {
          .el-form-item__content {
            width: 100%;
            
            .el-input,
            .el-input-number,
            .el-date-picker,
            .el-select,
            .el-radio-group,
            .el-checkbox-group {
              width: 100%;
            }
          }
        }
      }

      .form-buttons {
        display: flex;
        justify-content: center;
      }
    }
  }
}

.form-hint {
  margin-left: 10px;
  font-size: 13px;
  color: #909399;
}

/* 添加会员组下拉框的样式 */
:deep(.group-select) {
  .el-input__wrapper {
    padding-right: 30px;
  }
  
  .el-input__suffix {
    right: 5px;
  }
  
  .el-select__tags {
    max-width: calc(100% - 30px);
  }
}

.group-option {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 5px;
  
  .group-tag {
    margin-right: 10px;
  }
}

:deep(.empty-text) {
  padding: 10px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 为下拉选项添加样式 */
:deep(.el-select-dropdown__item) {
  &.hover, &:hover {
    .group-option {
      color: #409eff;
    }
  }
  
  &.selected {
    .group-option {
      color: #409eff;
      font-weight: bold;
    }
  }
}
</style> 