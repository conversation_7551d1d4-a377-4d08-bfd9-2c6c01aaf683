<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <FilterPopover 
        v-model="popoverVisible"
      >
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="popoverVisible = !popoverVisible">
            <el-icon><img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" /></el-icon>
            <span>{{ $t('Cms.categories.filter') }}</span>
          </el-button>
        </template>
        
        <el-form :model="searchForm" label-position="top" @submit.prevent.stop>
          <el-form-item :label="$t('Members.MemberList.columns.username')">
            <el-input 
              v-model="searchForm.keyword"
              :placeholder="$t('Members.MemberList.searchPlaceholder')"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="handleReset">
              <el-icon size="16"><Refresh /></el-icon>
              <span>{{ $t('Members.MemberList.reset') }}</span>
            </el-button>
            <el-button class="button-no-border" type="primary" @click="handleSearch">
              <el-icon size="16"><Filter /></el-icon>
              <span>{{ $t('Cms.categories.filter') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>

      <div class="btn-list" style="margin-left: 12px;">
        <el-dropdown @command="handleCreate">
          <el-button type="primary">
            <span>{{ $t('Members.MemberList.createMember') }}</span>
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu style="overflow: auto; max-height: 300px">
              <el-dropdown-item 
                v-for="type in memberTypes" 
                :key="type.id" 
                :command="type.id"
              >
                {{ type.type_name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格 -->
        <el-table 
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="name" :label="$t('Members.MemberList.columns.name')" min-width="120">
            <template #default="{ row }">
              {{ row.name || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="member_type" :label="$t('Members.MemberList.columns.memberType')" min-width="120">
            <template #default="{ row }">
              {{ getMemberTypeName(row.member_type) }}
            </template>
          </el-table-column>
          <el-table-column prop="username" :label="$t('Members.MemberList.columns.username')" min-width="120" />
          <el-table-column prop="email" :label="$t('Members.MemberList.columns.email')" min-width="180" />
          <el-table-column prop="last_login_at" :label="$t('Members.MemberList.columns.lastLoginTime')" min-width="160">
            <template #default="{ row }">
              {{ formatTime(row.last_login_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="$t('Members.MemberList.columns.status')" min-width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ row.status_text }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Members.MemberList.columns.actions')" width="180" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <el-button
                  class="bwms-operate-btn"
                  link 
                  type="primary" 
                  @click="handleEdit(row)"
                >
                  <el-icon><img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" /></el-icon>
                </el-button>
                <el-button 
                  class="bwms-operate-btn"
                  link 
                  type="primary" 
                  @click="handleDelete(row)"
                >
                  <el-icon><img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" /></el-icon>
                </el-button>
                <el-dropdown>
                  <el-button link type="primary">
                    <el-icon color="#486fb2"><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleDetail(row)">{{ $t('Members.MemberList.detail') }}</el-dropdown-item>
                      <el-dropdown-item @click="handleChangeStatus(row)">{{ $t('Members.MemberList.changeStatus') }}</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              background
              layout="prev, pager, next"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 状态修改框 -->
    <el-dialog
      v-model="statusDialogVisible"
      :title="$t('Members.MemberList.changeStatusDialog.title')"
      width="400px"
      class="el-dialog-common-cls"
    >
      <el-form :model="statusForm" label-position="top">
        <el-form-item :label="$t('Members.MemberList.changeStatusDialog.status')">
          <el-select v-model="statusForm.status" :placeholder="$t('Members.MemberList.changeStatusDialog.statusPlaceholder')" v-loading="statusLoading">
            <el-option 
              v-for="item in statusOptions" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="flex justify-center">
          <el-button @click="statusDialogVisible = false">{{ $t('Members.MemberList.changeStatusDialog.cancel') }}</el-button>
          <el-button 
            type="primary" 
            @click="confirmChangeStatus" 
            :loading="confirmStatusLoading"
          >
            {{ $t('Members.MemberList.changeStatusDialog.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Search, ArrowDown, Edit, CopyDocument, Delete, More, Filter, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { memberService, memberTypeService } from '../../services/memberService'
import { useI18n } from 'vue-i18n'
import FilterPopover from '/resources/admin/components/popover/index.vue'

// 定义状态选项接口
interface StatusOption {
  value: number
  label: string
}

// 路由
const router = useRouter()

// 加载状态
const loading = ref(false)

// 筛选弹窗显示状态
const popoverVisible = ref(false)

// 会员类型列表
interface IMemberType {
  id: number
  type_name: string
  description: string
  is_default: number
  status: number
}

// 定义会员状态枚举，不再使用import type
enum MemberStatus {
  ACTIVE = 1,
  DISABLED = 2,
  SUSPENDED = 3,
  PENDING = 4
}

// 更新会员接口定义以匹配新的数据结构
interface IMember {
  id: number
  name: string | null
  member_type: number
  username: string
  email: string
  status: number
  created_at: string
  last_login_at: string | null
  last_ip: string | null
  logins_count: number
  user_id: number
}

const memberTypes = ref<IMemberType[]>([])

// 状态修改对话框
const statusDialogVisible = ref(false)
const statusForm = reactive({
  status: 1
})
const currentRow = ref<IMember | null>(null)
const statusLoading = ref(false)
const statusOptions = ref<StatusOption[]>([])
const confirmStatusLoading = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 表格数据
const tableData = ref<IMember[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 使用国际化
const { t } = useI18n()

// 获取会员类型数据
const fetchMemberTypes = async () => {
  try {
    const params = {
      page: 1,
      limit: 100, // 获取足够的数据以展示所有会员类型
      status: 1 // 只获取启用的会员类型
    }
    const { data } = await memberTypeService.getList(params)
    
    if (data.code === 200 && data.data) {
      memberTypes.value = data.data.items || []
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberList.fetchMemberTypesFailed'))
  }
}

// 获取会员类型名称
const getMemberTypeName = (typeId: number) => {
  const type = memberTypes.value.find(t => t.id === typeId)
  return type ? type.type_name : '--'
}

// 获取状态标签类型
const getStatusType = (status: number) => {
  const types: Record<number, string> = {
    [MemberStatus.ACTIVE]: 'success',
    [MemberStatus.DISABLED]: 'danger',
    [MemberStatus.SUSPENDED]: 'warning',
    [MemberStatus.PENDING]: 'info'
  }
  return types[status] || ''
}

// 格式化时间
const formatTime = (datetime: string | null) => {
  if (!datetime) return '--'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm')
}

// 查询
const handleSearch = () => {
  pagination.page = 1 
  popoverVisible.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  popoverVisible.value = false
  handleSearch()
}

// 创建会员
const handleCreate = (typeId: number) => {
  router.push({
    name: 'MemberCreate',
    query: { typeId: typeId.toString() }
  })
}

// 编辑会员
const handleEdit = (row: IMember) => {
  router.push(`/members/${row.id}/edit`)
}

// 修改状态
const handleDetail = (row: IMember) => {
  router.push(`/members/${row.id}/detail`)
}

// 删除会员
const handleDelete = (row: IMember) => {
  ElMessageBox.confirm(
    t('Members.MemberList.deleteConfirm.message'),
    t('Members.MemberList.deleteConfirm.title'),
    {
      confirmButtonText: t('Members.MemberList.deleteConfirm.confirm'),
      cancelButtonText: t('Members.MemberList.deleteConfirm.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      const { data } = await memberService.delete(row.id)
      if (data.code === 200) {
        ElMessage.success(t('Members.MemberList.deleteSuccess'))
        fetchData() // 刷新列表
      } else {
        ElMessage.error(data.message || t('Members.MemberList.deleteFailed'))
      }
    } catch (error) {
    }
  }).catch(() => {
    ElMessage.info(t('Members.MemberList.deleteCancel'))
  })
}

// 获取状态列表
const fetchStatusList = async () => {
  statusLoading.value = true
  try {
    const { data } = await memberService.getStatusList()
    if (data.code === 200 && data.data) {
      // 直接使用接口返回的数组数据
      statusOptions.value = data.data
    } else {
      statusOptions.value = []
      ElMessage.warning(data.message || '获取会员状态列表失败')
    }
  } catch (error) {
    ElMessage.error('获取会员状态列表失败')
    statusOptions.value = []
  } finally {
    statusLoading.value = false
  }
}

// 修改状态
const handleChangeStatus = (row: IMember) => {
  currentRow.value = row
  statusForm.status = row.status
  statusDialogVisible.value = true
  
  // 如果还没有获取状态选项，则获取
  if (statusOptions.value.length === 0) {
    fetchStatusList()
  }
}

// 确认修改状态
const confirmChangeStatus = async () => {
  if (!currentRow.value) return
  
  try {
    confirmStatusLoading.value = true
    await memberService.updateStatus(currentRow.value.id, statusForm.status)
    ElMessage.success(t('Members.MemberList.statusChangeSuccess'))
    statusDialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error(t('Members.MemberList.statusChangeFailed'))
  } finally {
    confirmStatusLoading.value = false
  }
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.pageSize,
      keyword: searchForm.keyword
    }
    const { data } = await memberService.getList(params)
    
    // 根据接口返回的新数据结构调整
    if (data.code === 200 && data.data) {
      tableData.value = data.data.list || []
      pagination.total = data.data.total || 0
    } else {
      tableData.value = []
      pagination.total = 0
      ElMessage.warning(data.message || '获取会员列表失败')
    }
  } catch (error) {
    ElMessage.error('获取会员列表失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchMemberTypes() // 获取会员类型
  fetchData() // 获取会员列表
  fetchStatusList() // 获取会员状态列表
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  .module-header {
    flex-shrink: 0; // 防止头部被压缩
  }
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      padding-top: 20px;

      .search-area {
        margin-bottom: 20px;
        flex-shrink: 0; // 防止搜索区域被压缩
      }
    }
  }
}
</style> 