<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <div class="btn-list">
        <!-- 筛选 -->
        <FilterPopover 
          v-model="showFilterDropdown"
        >
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :model="searchForm" label-position="top" style="width: 100%;">
            <el-form-item :label="$t('Members.MemberTypes.columns.typeName')" style="width: 100%;">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="$t('Members.MemberTypes.searchPlaceholder')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
        <el-button type="primary" @click="handleCreate">
          <el-icon><img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" /></el-icon>
          <span>{{ $t('Members.MemberTypes.addType') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格 -->
        <el-table 
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="type_name" :label="$t('Members.MemberTypes.columns.typeName')" min-width="150" />
          <el-table-column prop="description" :label="$t('Members.MemberTypes.columns.description')" min-width="200" show-overflow-tooltip >
            <template #default="{ row }">
              <span v-if="row.description">{{ row.description }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="member_count" :label="$t('Members.MemberTypes.columns.memberCount')" min-width="120" />
          <el-table-column :label="$t('Members.MemberTypes.columns.actions')" width="180" fixed="right">
            <template #default="{ row }">
              <el-button 
                link 
                type="primary" 
                @click="handleEdit(row)"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button 
                link 
                type="primary" 
                @click="handleCopy(row)"
              >
                <el-icon><CopyDocument /></el-icon>
              </el-button>
              <el-button 
                link 
                type="primary" 
                @click="handleDelete(row)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
              <el-dropdown v-if="!row.is_default">
                <el-button link type="primary">
                  <el-icon><More /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleSetDefault(row)">
                      {{ $t('Members.MemberTypes.setAsDefault') }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              background
              layout="prev, pager, next"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Search, Edit, CopyDocument, Delete, More, Filter, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { memberTypeService } from '../../services/memberService'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

// 添加或修改类型定义
interface CustomField {
  id: number
  name: string
  type: string
  label: string
  options: string[]
  is_required: number
  is_searchable: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface Permission {
  id: number
  type_id: number
  resource_id: number
  access_type: number
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface IMemberType {
  id: number
  type_name: string
  description: string
  is_default: number
  status: number
  custom_fields: number[]
  creator_id: number
  created_at: string
  updated_at: string
  custom_field_details: CustomField[]
  permissions: Permission[]
}

// 路由
const router = useRouter()

// 加载状态
const loading = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 表格数据
const tableData = ref<IMemberType[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 添加筛选相关状态
const showFilterDropdown = ref(false)

// 查询
const handleSearch = () => {
  pagination.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  pagination.page = 1
  pagination.pageSize = 10
  showFilterDropdown.value = false
  handleSearch()
}

// 创建会员类型
const handleCreate = () => {
  router.push({ name: 'MemberTypeCreate' })
}

// 编辑会员类型
const handleEdit = (row: IMemberType) => {
  router.push(`/members/types/${row.id}/edit`)
}

// 复制会员类型
const handleCopy = async (row: IMemberType) => {
  try {
    loading.value = true
    await memberTypeService.copy(row.id)
    ElMessage.success(t('Members.MemberTypes.success.copySuccess'))
    fetchData()
  } catch (error) {
    ElMessage.error(t('Members.MemberTypes.errors.copyFailed'))
  } finally {
    loading.value = false
  }
}

// 删除会员类型
const handleDelete = (row: IMemberType) => {
  if (!row.id) return
  ElMessageBox.confirm(
    t('Members.MemberTypes.deleteConfirm.message'),
    t('Members.MemberTypes.deleteConfirm.title'),
    {
      confirmButtonText: t('Members.MemberTypes.deleteConfirm.confirm'),
      cancelButtonText: t('Members.MemberTypes.deleteConfirm.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      loading.value = true
      await memberTypeService.delete(row.id)
      ElMessage.success(t('Members.MemberTypes.success.deleteSuccess'))
      fetchData()
    } catch (error) {
      ElMessage.error(t('Members.MemberTypes.errors.deleteFailed'))
    } finally {
      loading.value = false
    }
  }).catch(() => {
    ElMessage.info(t('Members.MemberTypes.deleteConfirm.cancelled'))
  })
}

// 设置默认类型
const handleSetDefault = async (row: IMemberType) => {
  try {
    loading.value = true
    const data = {
      name: row.type_name,
      status: row.status,
      is_default: true // 设置为默认类型
    }
    await memberTypeService.setDefault(row.id, data)
    ElMessage.success(t('Members.MemberTypes.success.setDefaultSuccess'))
    fetchData()
  } catch (error) {
    ElMessage.error(t('Members.MemberTypes.errors.setDefaultFailed'))
  } finally {
    loading.value = false
  }
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.pageSize,
      keyword: searchForm.keyword
    }
    const { data } = await memberTypeService.getList(params)
    
    if (data.data) {
      tableData.value = data.data.items
      pagination.total = data.data.total
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberTypes.errors.fetchListFailed'))
  } finally {
    loading.value = false
  }
}

// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  .module-header {
    flex-shrink: 0; // 防止头部被压缩
  }
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1; // 让盒子占据剩余空间
      display: flex;
      flex-direction: column;
      overflow: auto; // 改为 auto，允许整个内容区域滚动

      .el-table {
        margin-bottom: 20px; // 添加底部边距，与分页保持间隔
      }

      .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0; // 防止分页区域被压缩
      }
    }
  }
  
  // 确保下拉菜单样式正确
  .el-dropdown {
    margin-left: 4px;
  }
}

.filter-trigger {
  display: flex;
  align-items: center;
  gap: 4px;
  
  .el-icon {
    display: flex;
    align-items: center;
    
    img {
      width: 16px;
      height: 16px;
    }
  }
}
</style> 