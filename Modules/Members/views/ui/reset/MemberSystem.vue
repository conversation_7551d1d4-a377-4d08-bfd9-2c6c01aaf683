<template>
  <div class="table-page bwms-module">
    <div class="module-header"></div>
    
    <div class="module-con scroll-bar-custom-transparent">
      <div class="config-bg">
        <!-- 基础设置模块 -->
        <div class="config-module">
          <div class="module-tit">
            <h2>{{ $t('Members.MemberSystem.basicSettings') }}</h2>
          </div>
          <div class="module-con">
            <div class="hei-box">
              <el-form 
                ref="formRef"
                :model="form"
                :rules="rules"
                label-position="top"
                v-loading="pageLoading"
              >
                <el-form-item 
                  :label="$t('Members.MemberSystem.registerEnabled')"
                  prop="register_enabled"
                  required
                >
                  <el-switch
                    v-model="form.register_enabled"
                    :active-value="1"
                    :inactive-value="0"
                  />
                  <el-select
                    v-if="form.register_enabled === 1"
                    v-model="form.register_range"
                    style="width: 50%; margin-left: 10px;"
                  >
                    <el-option :label="$t('Members.MemberSystem.registerRangeOptions.all')" :value="0" />
                    <el-option :label="$t('Members.MemberSystem.registerRangeOptions.memberOnly')" :value="1" />
                  </el-select>
                </el-form-item>
                
                <el-form-item 
                  :label="$t('Members.MemberSystem.registerAuditEnabled')"
                  prop="register_audit_enabled"
                  required
                >
                  <el-switch
                    v-model="form.register_audit_enabled"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>

                <el-form-item 
                  :label="$t('Members.MemberSystem.subscriptionEnabled')"
                  prop="subscription_enabled"
                  required
                >
                  <el-switch
                    v-model="form.subscription_enabled"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 通知设置模块 -->
        <div class="config-module">
          <div class="module-tit">
            <h2>{{ $t('Members.MemberSystem.notificationSettings') }}</h2>
          </div>
          <div class="module-con">
            <div class="hei-box">
              <el-form 
                ref="formRef"
                :model="form"
                :rules="rules"
                label-position="top"
                v-loading="pageLoading"
              >
                <el-form-item 
                  :label="$t('Members.MemberSystem.notificationEnabled')"
                  prop="notification_enabled"
                  required
                >
                  <el-switch
                    v-model="form.notification_enabled"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>

                <template v-if="form.notification_enabled === 1">
                  <el-form-item 
                    :label="$t('Members.MemberSystem.expiredNotificationEnabled')"
                    prop="send_expired_notification_enabled"
                  >
                    <el-switch
                      v-model="form.send_expired_notification_enabled"
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </el-form-item>

                  <el-form-item 
                    :label="$t('Members.MemberSystem.expiredNotificationTemplate')"
                    v-if="form.send_expired_notification_enabled === 1"
                  >
                    <el-select v-model="form.send_expired_notification_template" style="width: 50%;">
                      <el-option :label="$t('Members.MemberSystem.templateOptions.template1')" :value="1" />
                      <el-option :label="$t('Members.MemberSystem.templateOptions.template2')" :value="2" />
                    </el-select>
                  </el-form-item>

                  <el-form-item 
                    :label="$t('Members.MemberSystem.notificationTimes')"
                    v-if="form.send_expired_notification_enabled === 1"
                  >
                    <el-select v-model="form.send_expired_notification_times" style="width: 50%">
                      <el-option 
                        v-for="i in 5" 
                        :key="i" 
                        :label="`${i}${$t('Members.MemberSystem.times')}`" 
                        :value="i" 
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item 
                    :label="$t('Members.MemberSystem.statusChangeNotificationEnabled')"
                    prop="send_status_change_notification_enabled"
                  >
                    <el-switch
                      v-model="form.send_status_change_notification_enabled"
                      :active-value="1"
                      :inactive-value="0"
                    />
                  </el-form-item>

                  <el-form-item 
                    :label="$t('Members.MemberSystem.statusChangeNotificationTemplate')"
                    v-if="form.send_status_change_notification_enabled === 1"
                  >
                    <el-select v-model="form.send_status_change_notification_template" style="width: 50%;">
                      <el-option :label="$t('Members.MemberSystem.templateOptions.statusTemplate1')" :value="1" />
                      <el-option :label="$t('Members.MemberSystem.templateOptions.statusTemplate2')" :value="2" />
                    </el-select>
                  </el-form-item>
                </template>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 积分设置模块 -->
        <div class="config-module">
          <div class="module-tit">
            <h2>{{ $t('Members.MemberSystem.pointSettings') }}</h2>
          </div>
          <div class="module-con">
            <div class="hei-box">
              <el-form 
                ref="formRef"
                :model="form"
                :rules="rules"
                label-position="top"
                v-loading="pageLoading"
              >
                <el-form-item 
                  :label="$t('Members.MemberSystem.pointEnabled')"
                  prop="point_enabled"
                  required
                >
                  <el-switch
                    v-model="form.point_enabled"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>

                <el-form-item 
                  :label="$t('Members.MemberSystem.pointChannel')" 
                  v-if="form.point_enabled === 1"
                >
                  <el-select v-model="form.point_channel" style="width: 50%;">
                    <el-option :label="$t('Members.MemberSystem.pointChannelOptions.mall')" :value="1" />
                    <el-option :label="$t('Members.MemberSystem.pointChannelOptions.exchange')" :value="2" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 按钮组 -->
        <div class="form-buttons">
          <el-button class="button-cancel" @click="handleCancel" :disabled="submitLoading">{{ $t('Members.MemberSystem.cancel') }}</el-button>
          <el-button type="primary" @click="handleSave" :loading="submitLoading">
            {{ $t('Members.MemberSystem.save') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type { FormInstance } from 'element-plus'
import { memberService } from '../../services/memberService'

// 使用国际化
const { t } = useI18n()

// 表单实例
const formRef = ref<FormInstance>()

// 定义多个加载状态
const pageLoading = ref(false)      // 页面整体加载状态
const submitLoading = ref(false)    // 表单提交加载状态

// 表单数据
interface ISystemForm {
  register_enabled: number
  register_audit_enabled: number
  register_range: number
  subscription_enabled: number
  notification_enabled: number
  // 通知相关
  send_expired_notification_enabled: number
  send_expired_notification_template: number
  send_expired_notification_times: number
  send_status_change_notification_enabled: number
  send_status_change_notification_template: number
  send_status_change_notification_times: number
  // 积分相关
  point_enabled: number
  point_channel: number
}

const form = reactive<ISystemForm>({
  register_enabled: 0,
  register_audit_enabled: 0,
  register_range: 0,
  subscription_enabled: 0,
  notification_enabled: 0,
  // 通知相关
  send_expired_notification_enabled: 0,
  send_expired_notification_template: 1,
  send_expired_notification_times: 1,
  send_status_change_notification_enabled: 0,
  send_status_change_notification_template: 1,
  send_status_change_notification_times: 1,
  // 积分相关
  point_enabled: 0,
  point_channel: 1
})

// 表单验证规则 - 使用国际化
const rules = computed(() => ({
  register_enabled: [
    { required: true, message: t('Members.MemberSystem.rules.registerEnabled'), trigger: 'change' }
  ],
  register_audit_enabled: [
    { required: true, message: t('Members.MemberSystem.rules.registerAuditEnabled'), trigger: 'change' }
  ],
  subscription_enabled: [
    { required: true, message: t('Members.MemberSystem.rules.subscriptionEnabled'), trigger: 'change' }
  ],
  notification_enabled: [
    { required: true, message: t('Members.MemberSystem.rules.notificationEnabled'), trigger: 'change' }
  ],
  point_enabled: [
    { required: true, message: t('Members.MemberSystem.rules.pointEnabled'), trigger: 'change' }
  ],
  // 如果需要验证通知相关字段，可以添加对应的验证规则
  send_expired_notification_enabled: [
    { required: true, message: t('Members.MemberSystem.rules.expiredNotificationEnabled'), trigger: 'change' }
  ],
  send_status_change_notification_enabled: [
    { required: true, message: t('Members.MemberSystem.rules.statusChangeNotificationEnabled'), trigger: 'change' }
  ]
}))

// 取消
const handleCancel = () => {
  // 重新获取配置，恢复原始数据
  fetchConfig()
  ElMessage.info(t('Members.MemberSystem.cancelSuccess'))
}

// 加载配置
const fetchConfig = async () => {
  pageLoading.value = true
  try {
    const { data } = await memberService.getConfig()
    if (data.code === 200 && data.data) {
      // 数据格式转换处理
      const configData: Record<string, any> = {}
      
      // 遍历配置项，提取setting_value并进行类型转换
      Object.keys(data.data).forEach(key => {
        const item = data.data[key]
        if (item && item.setting_value !== undefined) {
          // 将字符串值转换为数字
          configData[key] = parseInt(item.setting_value, 10)
        }
      })
      
      // 更新表单数据
      Object.assign(form, configData)
    } else {
      ElMessage.warning(data.message || t('Members.MemberSystem.getConfigFailed'))
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberSystem.getConfigFailed'))
  } finally {
    pageLoading.value = false
  }
}

// 保存
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    // 如果注册功能关闭，重置注册相关设置
    let registerData = {}
    if (form.register_enabled === 0) {
      registerData = {
        register_range: 0
      }
    } else {
      registerData = {
        register_range: form.register_range
      }
    }
    
    // 如果通知功能关闭，重置所有通知相关设置为0
    let notificationData = {}
    if (form.notification_enabled === 0) {
      notificationData = {
        send_expired_notification_enabled: 0,
        send_expired_notification_template: 1,
        send_expired_notification_times: 1,
        send_status_change_notification_enabled: 0,
        send_status_change_notification_template: 1,
        send_status_change_notification_times: 1
      }
    } else {
      notificationData = {
        send_expired_notification_enabled: form.send_expired_notification_enabled,
        send_expired_notification_template: form.send_expired_notification_template,
        send_expired_notification_times: form.send_expired_notification_times,
        send_status_change_notification_enabled: form.send_status_change_notification_enabled,
        send_status_change_notification_template: form.send_status_change_notification_template,
        send_status_change_notification_times: form.send_status_change_notification_times
      }
    }
    
    // 如果积分功能关闭，重置积分相关设置
    let pointData = {}
    if (form.point_enabled === 0) {
      pointData = {
        point_channel: 1
      }
    } else {
      pointData = {
        point_channel: form.point_channel
      }
    }
    
    // 准备提交的数据 - 包含所有字段
    const submitData = {
      register_enabled: form.register_enabled,
      register_audit_enabled: form.register_audit_enabled,
      // 使用处理后的注册相关设置
      ...registerData,
      subscription_enabled: form.subscription_enabled,
      notification_enabled: form.notification_enabled,
      // 通知相关字段使用上面处理的数据
      ...notificationData,
      // 积分相关字段
      point_enabled: form.point_enabled,
      ...pointData
    }
    
    const { data } = await memberService.saveConfig(submitData as any)
    
    if (data.code === 200) {
      ElMessage.success(t('Members.MemberSystem.saveSuccess'))
      fetchConfig()
    } else {
      ElMessage.error(data.message || t('Members.MemberSystem.saveFailed'))
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberSystem.saveFailed'))
  } finally {
    submitLoading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchConfig()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .config-bg {
      background: #f4f7f9;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .config-module {
      width: 100%;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0px 1px 1px #00000029;
      margin-bottom: 20px;
      transition: box-shadow 0.2s;
      position: relative;
      padding: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .module-tit {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h2 {
          color: #000000;
          font-size: 16px;
          line-height: 1.4;
          margin: 0;
          font-weight: 500;
        }
      }

      .module-con {
        .hei-box {
         
        }
      }
    }

    .form-buttons {
      display: flex;
      justify-content: center;
      width: 100%;
    }
  }
}


</style>
