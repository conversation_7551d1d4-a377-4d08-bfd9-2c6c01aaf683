<template>
  <div class="table-page bwms-module">
    <div class="module-header"></div>
    <div class="scroll-bar-custom-transparent">
      <div class="module-con">
        <div class="box">
          <el-tabs v-model="activeName"  @tab-click="handleClick">
            <!-- <el-tab-pane :label="t('Message.push.tabs_name1')" name="1">
                <el-form :model="userForm" label-position="top" class="flex flex-wrap" :rules="userRules" ref="userFormRefs"
                  :hide-required-asterisk="true">
                  <div class="pr-4 w-2/4">
                    <el-form-item prop="userForm" label="用户表">
                      <el-select v-model="userForm.userForm" placeholder="Select" size="large" class="mb-2">
                        <el-option v-for="item in userFormList" :key="item.value" :label="item.label" :value="item.value" />
                      </el-select>
                      <el-alert type="info" :closable="false">
                        <p>更新用户数据时，存档表中的用户相关数据不会更新</p>
                      </el-alert>
                    </el-form-item>
                  </div>
                  <div class="pl-4 w-2/4">
                    <el-form-item prop="userName" label="用户名">
                      <el-input class="mb-2" v-model="userForm.userName" size="large" />
                      <el-alert type="info" :closable="false">
                        <p>可使用通配符" * "，多个用户名用半角逗号" , "隔开</p>
                      </el-alert>
                    </el-form-item>
                  </div>
                  <div class="pr-4 w-2/4">
                    <el-form-item prop="userID" label="用户 UID">
                      <el-input v-model="userForm.userID" size="large" />
                    </el-form-item>
                  </div>
                  <div class="pl-4 w-2/4">
                    <el-form-item prop="userGroup" label="主用户组">
                      <el-select v-model="userForm.userGroup" size="large">
                        <el-option-group v-for="group in userGroupList" :key="group.label" :label="group.label">
                          <el-option v-for="item in group.options" :key="item.value" :label="item.label"
                            :value="item.value" />
                        </el-option-group>
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="flex justify-center items-center mt-4 w-full btn-list">
                    <div class="btn-box blue-btn" @click="nextStep">
                      <span>下一步</span>
                    </div>
                  </div>
                </el-form>
              </el-tab-pane> -->
            <el-tab-pane :label="t('Message.push.tabs_name2')" name="2">
              <el-form v-loading="loading" :model="noticeForm" label-position="top" class="flex flex-wrap" :disabled="id" :rules="noticeRules" ref="noticFormRefs" :hide-required-asterisk="true">
                <!-- <div class="pr-4 w-2/4">
                    <el-form-item prop="userTotal" label="通知会员">
                      <p>共搜索到 {{ noticeForm.userTotal }} 名符合条件的用户 <span class="underline cursor-pointer underline-offset-2" @click="activeName = '1'">重新搜索</span></p>
                    </el-form-item>
                  </div> -->
                <div class="w-full">
                  <el-form-item prop="title" :label="t('Message.push.tabs1_label1')">
                    <el-input v-model="noticeForm.title" size="large" />
                  </el-form-item>
                </div>
                <div class="w-full">
                  <el-form-item prop="content" :label="t('Message.push.tabs1_label2')">
                    <el-input v-model="noticeForm.content" type="textarea" :rows="10" resize="none" size="large" />
                  </el-form-item>
                </div>
                <div class="pr-4 w-2/4">
                  <el-form-item prop="type" :label="t('Message.push.tabs1_label3')">
                    <el-radio-group v-model="noticeForm.type">
                      <el-radio :value="1" size="large">{{ $t('Message.push.label3_radio1') }}</el-radio>
                      <el-radio :value="2" size="large">{{ $t('Message.push.label3_radio2') }}</el-radio>
                      <el-radio :value="3" size="large">{{ $t('Message.push.label3_radio3') }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div class="pr-4 w-2/4">
                  <el-form-item prop="priority" :label="t('Message.push.tabs1_label4')">
                    <el-radio-group v-model="noticeForm.priority">
                      <el-radio :value="0" size="large">{{ $t('Message.push.label4_radio1') }}</el-radio>
                      <el-radio :value="1" size="large">{{ $t('Message.push.label4_radio2') }}</el-radio>
                      <el-radio :value="2" size="large">{{ $t('Message.push.label4_radio3') }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div class="pr-4 w-2/4">
                  <el-form-item prop="sendNum" :label="t('Message.push.tabs1_label5')">
                    <el-input-number v-model="noticeForm.sendNum" size="large" :step="10" />
                  </el-form-item>
                </div>
                
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="flex justify-center mt-5" v-if="!id">
        <el-button @click="submitHandle">
          <span>{{ $t('Message.push.btn1_text') }}</span>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { useAppStore } from '/admin/stores/modules/app'
import { useUserStore } from '/admin/stores/modules/user'
import http from '/admin/support/http'

import MessageRepositoryImpl from '../infrastructure/MessageRepositoryImpl'
import MessageServices from '../application/MessageServices'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const api = 'message'
const appStore = useAppStore()
const userStore = useUserStore()
const pageName = ref(appStore.getPageName)
const route = useRoute()
const router = useRouter()
const id = Number(route.query.id) || undefined

// Tab
const activeName = ref('2')
const handleClick = (tab: any) => {
  console.log(tab)
}

const loading = ref(true)
const MessageInstance = new MessageServices(new MessageRepositoryImpl(api), t)

const userFormRefs = ref()
const userFormList = ref([
  {
    label: '表格1',
    value: 1,
  },
  {
    label: '表格2',
    value: 2,
  },
  {
    label: '表格3',
    value: 3,
  },
  {
    label: '表格4',
    value: 4,
  },
])
const userGroupList = ref([
  {
    label: '会员用户组1',
    value: 1,
    options: [
      {
        label: '会员1-1',
        value: 2,
      },
      {
        label: '会员1-2',
        value: 3,
      },
      {
        label: '会员1-3',
        value: 4,
      },
      {
        label: '会员1-4',
        value: 5,
      },
    ],
  },
  {
    label: '会员用户组2',
    value: 1,
    options: [
      {
        label: '会员2-1',
        value: 2,
      },
      {
        label: '会员2-2',
        value: 3,
      },
      {
        label: '会员2-3',
        value: 4,
      },
      {
        label: '会员2-4',
        value: 5,
      },
    ],
  },
])
const userForm = reactive({
  userForm: '',
  userName: '',
  userID: '',
  userGroup: '',
})
const userRules = {
  userForm: [{ required: true, message: '请选择用户表', trigger: 'change' }],
  userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  userID: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
  userGroup: [{ required: true, message: '请选择主用户组', trigger: 'change' }],
}
const nextStep = () => {
  userFormRefs.value.validate((valid: boolean, fields: any) => {
    if (valid) {
      activeName.value = '2'
    } else {
      console.log('error submit!', fields)
    }
  })
}

const noticFormRefs = ref()
const noticeForm = reactive({
  // userTotal: 1000,
  receiver_id: 0,
  title: '',
  content: '',
  type: 1,
  priority: 0,
  sendNum: 100,
})
const noticeRules = {
  title: [{ required: true, message: t('Message.push.label1_msg'), trigger: 'blur' }],
  content: [{ required: true, message: t('Message.push.label2_msg'), trigger: 'blur' }],
  type: [{ required: true, message: t('Message.push.label3_msg'), trigger: 'blur' }],
  priority: [{ required: true, message: t('Message.push.label4_msg'), trigger: 'blur' }],
  sendNum: [{ required: true, message: t('Message.push.label5_msg'), trigger: 'blur' }],
}
const submitHandle = () => {
  loading.value = true
  noticFormRefs.value.validate((valid: boolean, fields: any) => {
    if (valid) {
      MessageInstance.create(noticeForm)
        .then(() => {
          router.push({
            path: `/message/messagePushList`,
            query: {
              type: noticeForm.type,
            },
          })
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      console.log('error submit!', fields)
      loading.value = false
    }
  })
}

function getDetai() {
  
  if (!id) {
    loading.value = false
    return
  }
  MessageInstance.getById(id)
    .then(res => {
      if (!res) return
      const { receiver_id, title, content, type, priority } = res
      noticeForm.receiver_id = receiver_id
      noticeForm.title = title
      noticeForm.content = content
      noticeForm.type = type
      noticeForm.priority = priority
    })
    .finally(() => {
      loading.value = false
    })
}

onMounted(() => {
  getDetai();
  // http
  //   .get(`/user/id?email=${userStore.getEmail}`)
  //   .then(res => {
  //     noticeForm.receiver_id = res.data.data.user_id
  //   })
  //   .finally(() => {

  //   })
})
</script>

<style lang="scss" scoped>
.bwms-module{
  .module-con {
    .box {
      padding-top: 0;
    }
  }
}
</style>