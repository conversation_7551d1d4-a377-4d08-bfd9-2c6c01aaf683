{"PayConfig": {"title": "支付设置列表", "button": {"add": "新增", "edit": "编辑配置"}, "label": {"if_code": "接口代码：", "way_code": "支付渠道：", "rate": "费率：", "status": "状态："}, "empty": "暂无支付配置数据", "message": {"status_update_success": "状态更新成功", "status_update_fail": "状态更新失败", "fetch_fail": "获取支付配置列表失败"}}, "PayConfigForm": {"editTitle": "编辑支付配置", "addTitle": "添加支付配置", "sectionBase": "基础配置", "sectionParams": "参数配置", "label": {"if_code": "接口代码", "if_name": "接口名称", "way_code": "支付渠道", "if_rate": "费率", "state": "状态", "lang": "语言标识", "icon": "卡片图标", "bg_color": "卡片背景色", "remark": "备注", "params_json": "配置JSON", "qrcode_url": "支付二维码"}, "placeholder": {"if_code": "请选择接口代码", "if_name": "请输入接口名称", "way_code": "请选择支付渠道", "if_rate": "请输入费率，如0.0038表示0.38%", "lang": "请选择语言标识", "icon": "图片上传", "remark": "请输入备注信息", "params_json": "请输入JSON格式的配置", "qrcode_upload": "上传二维码"}, "button": {"cancel": "取消", "save": "保存"}, "message": {"upload_success": "上传成功", "upload_fail": "上传失败", "qrcode_upload_success": "二维码上传成功", "qrcode_upload_fail": "二维码上传失败", "fetch_fail": "获取配置详情失败", "create_success": "创建成功", "create_fail": "创建失败", "update_success": "更新成功", "update_fail": "更新失败", "json_error": "配置JSON格式错误", "qrcode_required": "请上传支付二维码", "image_format_error": "上传图片只能是图片格式!", "image_size_error": "上传图片大小不能超过 2MB!"}, "validate": {"if_code": "请选择接口代码", "if_name": "请输入接口名称", "way_code": "请选择支付渠道", "if_rate": "请输入费率", "publishable_key": "请输入Publishable Key", "secret_key": "请输入Secret Key", "client_id": "请输入Client ID", "client_secret": "请输入Client Secret", "brand_name": "请输入品牌名称", "mode": "请选择环境模式", "return_url": "请输入返回URL", "qrcode_url": "请上传支付二维码"}, "qrcode_tip": {"alipay": "请上传支付宝收款二维码图片（必填）", "wechat": "请上传微信收款二维码图片（必填）", "payme": "请上传PayMe收款二维码图片（必填）", "fps": "请上传FPS收款二维码图片（必填）", "default": "请上传支付二维码图片（必填）"}}, "PayRefund": {"list": {"title": "退款订单列表", "data_title": "退款订单数据", "refresh": "刷新", "refresh_success": "数据已刷新", "search": "查询", "reset": "重置", "view_detail": "查看详情", "delete": "删除", "delete_confirm": "确定删除该退款订单吗？", "delete_success": "删除成功", "delete_fail": "删除失败"}, "form": {"keyword": "关键词", "keyword_placeholder": "订单号/商户订单号", "state": "状态", "way_code": "支付方式", "apply_date": "申请日期", "date_range": {"start": "开始日期", "end": "结束日期", "separator": "至", "last_week": "最近一周", "last_month": "最近一个月", "last_three_months": "最近三个月"}, "placeholder": {"all": "全部"}}, "table": {"id": "ID", "refund_order_id": "退款订单号", "pay_order_id": "支付订单号", "mch_refund_no": "商户退款单号", "refund_amount": "退款金额", "way_code": "支付方式", "if_code": "支付接口", "state": "状态", "refund_reason": "退款原因", "created_at": "申请时间", "actions": "操作"}, "detail": {"title": "退款订单详情", "base_info": "基本信息", "error_info": "错误信息", "channel_extra": "通道额外参数", "system_info": "系统信息", "label": {"refund_order_id": "退款订单号", "pay_order_id": "支付订单号", "mch_refund_no": "商户退款单号", "channel_pay_order_no": "渠道支付单号", "channel_order_no": "渠道订单号", "if_code": "支付接口", "way_code": "支付方式", "pay_amount": "支付金额", "refund_amount": "退款金额", "state": "退款状态", "client_ip": "申请IP", "refund_reason": "退款原因", "success_time": "成功时间", "expired_time": "过期时间", "notify_url": "通知地址", "err_code": "错误码", "err_msg": "错误描述", "created_at": "创建时间", "updated_at": "更新时间", "creator_id": "创建者ID", "ext_param": "扩展参数"}, "unknown": "未知", "none": "无"}, "state": {"0": "订单生成", "1": "退款中", "2": "退款成功", "3": "退款失败", "4": "退款任务关闭", "unknown": "未知"}, "payment_methods": {"alipay": "支付宝", "wechat": "微信支付", "stripe": "Stripe", "paypal": "PayPal", "payme": "PayMe", "fps": "FPS"}}, "PayPassage": {"list": {"title": "支付通道管理", "addButton": "新增支付通道", "search": "查询", "reset": "重置", "refresh": "刷新", "tableTitle": "通道列表", "dialogEdit": "编辑支付通道", "dialogAdd": "新增支付通道", "dialogDetail": "支付通道详情", "message": {"deleteConfirm": "确定删除该支付通道吗？", "deleteSuccess": "删除成功", "deleteFail": "删除失败", "updateSuccess": "更新成功", "updateFail": "更新失败", "createSuccess": "创建成功", "createFail": "创建失败", "loadOptionsFail": "加载支付选项失败，使用默认选项"}}, "form": {"label": {"if_code": "支付接口", "way_code": "支付方式", "rate": "支付方式费率", "state": "状态"}, "placeholder": {"if_code": "请选择支付接口", "way_code": "请选择支付方式", "rate": "请输入费率，例如：0.0038", "all": "全部"}, "tip": {"rate": "费率范围：0-1，例如：0.0038表示0.38%"}, "button": {"save": "保存修改", "create": "立即创建", "cancel": "取消", "edit": "编辑", "view": "查看", "delete": "删除"}, "state": {"enabled": "启用", "disabled": "停用"}, "validation": {"if_code": "请选择支付接口", "way_code": "请选择支付方式", "rate": "请输入费率", "rate_range": "费率必须在0到1之间", "state": "请选择状态"}}, "table": {"id": "ID", "if_code": "支付接口", "way_code": "支付方式", "rate": "费率", "state": "状态", "created_at": "创建时间", "actions": "操作"}, "detail": {"id": "ID", "if_code": "支付接口", "way_code": "支付方式", "rate": "费率", "state": "状态", "created_at": "创建时间", "updated_at": "更新时间"}}, "PayOrder": {"list": {"title": "支付订单列表", "refresh": "刷新", "refresh_success": "数据已刷新", "back_to_list": "返回订单列表"}, "filter": {"pay_order_id": "订单号", "mch_order_no": "商户订单号", "if_code": "支付接口", "way_code": "支付方式", "state": "订单状态", "created_time": "创建时间", "search": "查询", "reset": "重置", "date_range": {"start": "开始日期", "end": "结束日期", "separator": "至"}, "placeholder": {"pay_order_id": "支付订单号", "mch_order_no": "商户订单号", "if_code": "支付接口代码", "way_code": "支付方式", "state": "订单状态"}}, "table": {"id": "ID", "pay_order_id": "订单号", "mch_order_no": "商户订单号", "if_code": "接口代码", "way_code": "支付方式", "amount": "金额", "state": "状态", "subject": "商品名称", "created_at": "创建时间", "actions": "操作", "view": "查看", "refund": "退款"}, "detail": {"title": "订单详情", "base_info": "基本信息", "payment_info": "支付信息", "callback_info": "回调信息", "labels": {"pay_order_id": "支付订单号", "state": "订单状态", "mch_order_no": "商户订单号", "if_code": "支付接口", "way_code": "支付方式", "amount": "订单金额", "fee": "手续费", "subject": "商品名称", "body": "商品描述", "created_at": "创建时间", "expired_time": "过期时间", "success_time": "支付完成时间", "refund_state": "退款状态", "refund_times": "退款次数", "refund_amount": "退款金额", "channel_order_no": "支付渠道订单号", "channel_user": "渠道用户标识", "client_ip": "用户IP", "err_code": "错误码", "err_msg": "错误信息", "notify_state": "回调状态", "notify_url": "通知地址", "return_url": "跳转地址", "ext_param": "扩展参数", "channel_extra": "渠道扩展数据"}, "state": {"notified": "已通知", "not_notified": "未通知", "refunded": "已退款", "not_refunded": "未退款"}, "button": {"apply_refund": "申请退款"}, "empty": "找不到订单信息"}, "refund": {"confirm_title": "退款确认", "confirm_button": "确认退款", "cancel_button": "取消", "confirm_message": "确认对订单 {0} 申请退款吗？金额：{1} {2}", "success": "退款申请成功", "fail": "退款申请失败: {0}", "error": "退款操作失败"}, "state": {"0": "订单生成", "1": "支付中", "2": "支付成功", "3": "支付失败", "4": "已撤销", "5": "已退款", "6": "订单关闭", "unknown": "未知状态"}, "payment_methods": {"paypal": "PayPal", "stripe": "Stripe", "alipay": "支付宝", "wechatpay": "微信支付", "payme": "PayMe", "fps": "FPS"}, "statistics": {"total_count": "总订单数", "success_count": "成功订单", "total_amount": "总交易额", "refund_amount": "退款金额"}}}