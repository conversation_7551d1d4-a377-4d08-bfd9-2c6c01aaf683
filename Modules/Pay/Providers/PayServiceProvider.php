<?php

namespace Modules\Pay\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use Mo<PERSON>les\Iam\Enums\MenuType;
use Modules\Pay\Services\PaymentLogger;

class PayServiceProvider extends BingoModuleServiceProvider
{
    public function boot(): void
    {
        $path = dirname(__DIR__, 2).DIRECTORY_SEPARATOR.'Pay'.DIRECTORY_SEPARATOR.'Lang';
        $this->loadTranslationsFrom($path, 'Pay');
        $this->registerNavigation();
        $this->registerModulePermissions();
    }

    /**
     * route path
     *
     * @return string
     */
    public function moduleName(): string
    {
        return 'Pay';
    }

    protected function navigation(): array
    {
        return [
            [
                "key" => "payment_management",
                "parent" => "application",
                "nav_name" => T("Pay::nav.payment_management"),
                "path" => "/pay",
                "icon" => "Nav/Asset/menu_icon/payment_management.png",
                "order" => 7,
                "children" => [
                    [
                        "key" => "payment_order_list",
                        "parent" => "payment_management",
                        "nav_name" => T("Pay::nav.payment_order_list"),
                        "path" => "/pay/order",
                        "icon" => "List",
                        "order" => 1
                    ],
                    [
                        "key" => "payment_settings",
                        "parent" => "payment_management",
                        "nav_name" => T("Pay::nav.payment_settings"),
                        "path" => "/pay/list",
                        "icon" => "Postcard",
                        "order" => 2
                    ],
                    [
                        "key" => "payment_channel_management",
                        "parent" => "payment_management",
                        "nav_name" => T("Pay::nav.payment_channel_management"),
                        "path" => "/pay/passage",
                        "icon" => "Finished",
                        "order" => 3
                    ],
                    [
                        "key" => "refund_order_list",
                        "parent" => "payment_management",
                        "nav_name" => T("Pay::nav.refund_order_list"),
                        "path" => "/pay/refund",
                        "icon" => "Compass",
                        "order" => 4
                    ]
                ]
            ]
        ];
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [

        ];
    }

    public function registerPermissions(): array
    {
        $admin = [
            'pay' => [
                'permission_name' => T("Pay::permission.payment_management"),
                'route' => '/pay',
                'parent_id' => 0,
                'permission_mark' => 'pay',
                'component' => '/admin/layout/index.vue',
                'type' => MenuType::Top->value(),
                'sort' => 1,
                'children' => [
                    [
                        'permission_name' => T("Pay::permission.payment_list"),
                        'route' => 'list',
                        'parent_id' => "pay",
                        'permission_mark' => 'payment_list',
                        'component' => '/admin/views/AMisPage.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 1,
                        'actions' => [
                            [
                                'permission_name' => T("List"),
                                'route' => '',
                                'permission_mark' => 'pay@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 1,
                                'parent_id' => 'pay'
                            ],
                            [
                                'permission_name' => T("Read"),
                                'route' => '',
                                'permission_mark' => 'pay@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 2,
                                'parent_id' => 'pay'
                            ],
                        ],
                    ],
                    [
                        'permission_name' => T("Pay::permission.payment_config"),
                        'route' => 'config',
                        'parent_id' => "pay",
                        'permission_mark' => 'payment_config',
                        'component' => '/admin/views/AMisPage.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 2,
                        'actions' => [
                            [
                                'permission_name' => T("Update"),
                                'route' => '',
                                'permission_mark' => 'pay@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 1,
                                'parent_id' => 'pay'
                            ],
                        ],
                    ],
                    [
                        'permission_name' => T("Pay::permission.payment_passage"),
                        'route' => 'passage',
                        'parent_id' => "pay",
                        'permission_mark' => 'payPassage',
                        'component' => '/admin/views/AMisPage.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 3,
                        'actions' => [
                            [
                                'permission_name' => T("List"),
                                'route' => '',
                                'permission_mark' => 'payPassage@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 1,
                                'parent_id' => 'payPassage'
                            ],
                            [
                                'permission_name' => T("Create"),
                                'route' => '',
                                'permission_mark' => 'payPassage@store',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 2,
                                'parent_id' => 'payPassage'
                            ],
                            [
                                'permission_name' => T("Update"),
                                'route' => '',
                                'permission_mark' => 'payPassage@update',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 3,
                                'parent_id' => 'payPassage'
                            ],
                            [
                                'permission_name' => T("Delete"),
                                'route' => '',
                                'permission_mark' => 'payPassage@destroy',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 4,
                                'parent_id' => 'payPassage'
                            ],
                        ],
                    ],
                    [
                        'permission_name' => T("Pay::permission.payment_order"),
                        'route' => 'order',
                        'parent_id' => "pay",
                        'permission_mark' => 'payOrder',
                        'component' => '/admin/views/AMisPage.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 4,
                        'actions' => [
                            [
                                'permission_name' => T("List"),
                                'route' => '',
                                'permission_mark' => 'payOrder@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 1,
                                'parent_id' => 'payOrder'
                            ],
                            [
                                'permission_name' => T("Read"),
                                'route' => '',
                                'permission_mark' => 'payOrder@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 2,
                                'parent_id' => 'payOrder'
                            ],
                        ],
                    ],
                    [
                        'permission_name' => T("Pay::permission.refund_order"),
                        'route' => 'refund',
                        'parent_id' => "pay",
                        'permission_mark' => 'payRefund',
                        'component' => '/admin/views/AMisPage.vue',
                        'type' => MenuType::Menu->value(),
                        'sort' => 5,
                        'actions' => [
                            [
                                'permission_name' => T("List"),
                                'route' => '',
                                'permission_mark' => 'payRefund@index',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 1,
                                'parent_id' => 'payRefund'
                            ],
                            [
                                'permission_name' => T("Read"),
                                'route' => '',
                                'permission_mark' => 'payRefund@show',
                                'component' => '',
                                'type' => MenuType::Action->value(),
                                'sort' => 2,
                                'parent_id' => 'payRefund'
                            ],
                        ],
                    ],
                ],
            ],
        ];
        $frontend = [];
        return array_merge(["admin" => $admin], ["frontend" => $frontend]);
    }

    public function register(): void
    {
        parent::register();
        
        // 注册PaymentLogger为单例
        $this->app->singleton(PaymentLogger::class, function ($app) {
            return new PaymentLogger();
        });
    }

}
