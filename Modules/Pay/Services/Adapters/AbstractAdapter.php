<?php
// Services/Adapters/AbstractAdapter.php
namespace Modules\Pay\Services\Adapters;

use Illuminate\Support\Facades\Log;
use Modules\Pay\Domain\Contracts\PaymentInterface;
use Modules\Pay\Models\PayConfig;
use Modules\Pay\Enums\PayErrorCode;
use Modules\Pay\Services\PaymentLogger;

abstract class AbstractAdapter implements PaymentInterface
{
    protected PayConfig $config;
    protected array $configParams;
    protected PaymentLogger $logger;
    
    public function __construct(PayConfig $config)
    {
        $this->config = $config;
        // 检查并确保configParams是数组类型
        if (isset($config->if_params)) {
            if (is_string($config->if_params)) {
                // 尝试将JSON字符串解析为数组
                $decodedParams = json_decode($config->if_params, true);
                $this->configParams = is_array($decodedParams) ? $decodedParams : [];
            } else {
                $this->configParams = is_array($config->if_params) ? $config->if_params : [];
            }
        } else {
            $this->configParams = [];
        }
        
        if (empty($this->configParams)) {
            throw new \Exception("Payment channel [{$config->if_code}] config is empty", PayErrorCode::PAY_CONFIG_NOT_FOUND->value);
        }
        
        // 获取PaymentLogger实例
        $this->logger = app(PaymentLogger::class);
    }
    
    /**
     * 获取支付配置
     */
    public function getConfig(): array
    {
        return $this->configParams;
    }
    
    /**
     * 获取渠道代码
     */
    public function getIfCode(): string
    {
        return $this->config->if_code;
    }
    
    /**
     * 签名生成
     * @param array $params 待签名参数
     * @return string 签名结果
     */
    abstract protected function generateSign(array $params): string;
    
    /**
     * 签名验证
     * @param array $params 待验证参数
     * @param string $sign 签名
     * @return bool 验证结果
     */
    abstract protected function verifySign(array $params, string $sign): bool;
    
    /**
     * 记录日志
     */
    protected function log(string $message, array $context = []): void
    {
        $context['channel'] = $this->getIfCode();
        Log::info($message, $context);
    }
    
    /**
     * 记录错误日志
     */
    protected function logError(string $message, array $context = []): void
    {
        $context['channel'] = $this->getIfCode();
        Log::error($message, $context);
    }
    
    /**
     * 记录API请求日志
     */
    protected function logApiRequest(string $apiName, array $requestData): void
    {
        $this->logger->logApiRequest($this->getIfCode(), $apiName, $requestData);
    }
    
    /**
     * 记录API响应日志
     */
    protected function logApiResponse(string $apiName, array $responseData, bool $isSuccess = true): void
    {
        $this->logger->logApiResponse($this->getIfCode(), $apiName, $responseData, $isSuccess);
    }
    
    /**
     * 记录支付通知
     */
    protected function logPaymentNotify(string $orderId, array $data): void
    {
        $this->logger->logPaymentNotify($orderId, $data);
    }

    /**
     * 捕获支付订单（对于需要二次确认的支付方式如PayPal）
     * 
     * @param string $token 支付令牌
     * @param string $payerId 支付者ID
     * @return array 捕获结果
     */
    public function captureOrder(string $token, string $payerId): array
    {
        // Implementation of captureOrder method
        return [
            'success' => false,
            'error_msg' => '该支付方式不支持捕获操作'
        ];
    }
    
    /**
     * 获取固定收款二维码（用于需要展示二维码的支付方式，如PayMe、FPS等）
     * 
     * @param array $orderData 订单数据
     * @return array 二维码数据，包含base64编码的图片、URL或其他必要信息
     */
    public function generateQrCode(array $orderData): array
    {
        // 获取二维码类型
        $qrCodeType = $orderData['qr_type'] ?? '';
        
        // 从配置中获取对应类型的二维码数据
        $qrCodeKey = "{$qrCodeType}_qrcode";
        $qrCodeData = $this->configParams[$qrCodeKey] ?? null;
        $qrCodeUrl = $this->configParams["{$qrCodeType}_qrcode_url"] ?? null;
        
        // 如果配置中有二维码数据或URL，返回成功
        if (!empty($qrCodeData) || !empty($qrCodeUrl)) {
            return [
                'success' => true,
                'qrcode_base64' => $qrCodeData,
                'qrcode_url' => $qrCodeUrl,
                'qr_type' => $qrCodeType,
                'instructions' => $this->configParams['payment_instructions'] ?? '请使用手机扫描二维码进行支付，支付完成后上传付款截图'
            ];
        }
        
        // 默认实现，不支持生成二维码
        return [
            'success' => false,
            'error_msg' => '该支付方式不支持二维码支付或未配置二维码'
        ];
    }
    
    /**
     * 通过收据确认支付（用于用户上传付款截图的情况）
     * 
     * @param string $orderNo 订单号
     * @param string $receiptFile 收据文件（可以是文件路径或上传的文件对象）
     * @param array $extraData 额外数据（付款金额、时间等）
     * @return array 确认结果
     */
    public function confirmByReceipt(string $orderNo, $receiptFile, array $extraData = []): array
    {
        // 默认实现，不支持通过收据确认
        return [
            'success' => false,
            'error_msg' => '该支付方式不支持通过收据确认'
        ];
    }
}
