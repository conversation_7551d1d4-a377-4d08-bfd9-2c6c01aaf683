<!-- Modules/Pay/views/ui/configs/ConfigForm.vue -->
<template>
    <div class="config-form-container">
      <!-- <div class="page-header">
        <h2>{{ isEdit ? $t('Pay.PayConfigForm.editTitle') : $t('Pay.PayConfigForm.addTitle') }}</h2>
      </div> -->
      <div class="form-wrapper">
        <el-form 
          ref="formRef" 
          :model="formData" 
          :rules="rules" 
          label-width="120px"
          v-loading="loading"
        >
          <!-- 基础配置区域 -->
          <div class="form-section">
            <h3 class="section-title">{{ $t('Pay.PayConfigForm.sectionBase') }}</h3>
            
            <el-row :gutter="24">
              <el-col :xs="24" :sm="24" :md="12">
                <el-form-item :label="$t('Pay.PayConfigForm.label.if_code')" prop="if_code">
                  <el-select 
                    v-model="formData.if_code" 
                    :placeholder="$t('Pay.PayConfigForm.placeholder.if_code')"
                    class="form-control"
                    :disabled="isEdit"
                  >
                    <el-option 
                      v-for="item in ifCodes" 
                      :key="item.value || item" 
                      :label="item.label || item" 
                      :value="item.value || item" 
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :xs="24" :sm="24" :md="12">
                <el-form-item :label="$t('Pay.PayConfigForm.label.if_name')" prop="if_name">
                  <el-input v-model="formData.if_name" :placeholder="$t('Pay.PayConfigForm.placeholder.if_name')" class="form-control" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :xs="24" :sm="24" :md="12">
                <el-form-item :label="$t('Pay.PayConfigForm.label.way_code')" prop="way_code">
                  <el-select 
                    v-model="formData.way_code" 
                    :placeholder="$t('Pay.PayConfigForm.placeholder.way_code')" 
                    class="form-control"
                    @change="handleChannelChange"
                  >
                    <el-option 
                      v-for="(label, value) in configStore.payChannels" 
                      :key="value" 
                      :label="label" 
                      :value="value" 
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :xs="24" :sm="24" :md="12">
                <el-form-item :label="$t('Pay.PayConfigForm.label.if_rate')" prop="if_rate">
                  <el-input-number 
                    v-model="formData.if_rate" 
                    :precision="4" 
                    :step="0.0001" 
                    :min="0" 
                    :max="1"
                    class="form-control"
                    :placeholder="$t('Pay.PayConfigForm.placeholder.if_rate')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :xs="24" :sm="24" :md="12">
                <el-form-item :label="$t('Pay.PayConfigForm.label.state')">
                  <el-switch v-model="formData.state" :active-value="1" :inactive-value="0" />
                </el-form-item>
              </el-col>
              
              <el-col :xs="24" :sm="24" :md="12">
                <el-form-item :label="$t('Pay.PayConfigForm.label.lang')" prop="lang">
                  <el-select v-model="formData.lang" :placeholder="$t('Pay.PayConfigForm.placeholder.lang')" class="form-control">
                    <el-option label="简体中文" value="zh_CN" />
                    <el-option label="繁体中文" value="zh_HK" />
                    <el-option label="英文" value="en" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :xs="24" :sm="24" :md="12">
                <el-form-item :label="$t('Pay.PayConfigForm.label.icon')" class="upload-item">
                  <el-upload
                    class="avatar-uploader"
                    :auto-upload="true"
                    :show-file-list="false"
                    :before-upload="beforeAvatarUpload"
                    name="file"
                  >
                    <img v-if="formData.icon" :src="formData.icon" class="avatar" />
                    <div v-else class="avatar-uploader-placeholder">
                      <el-icon><Plus /></el-icon>
                      <div>{{ $t('Pay.PayConfigForm.placeholder.icon') }}</div>
                    </div>
                  </el-upload>
                </el-form-item>
              </el-col>
              
              <el-col :xs="24" :sm="24" :md="12">
                <el-form-item :label="$t('Pay.PayConfigForm.label.bg_color')">
                  <el-color-picker v-model="formData.bg_color" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item :label="$t('Pay.PayConfigForm.label.remark')">
              <el-input 
                v-model="formData.remark" 
                type="textarea" 
                :rows="3" 
                :placeholder="$t('Pay.PayConfigForm.placeholder.remark')"
                class="form-control"
              />
            </el-form-item>
          </div>
          
          <!-- 参数配置区域 -->
          <div class="form-section">
            <h3 class="section-title">{{ $t('Pay.PayConfigForm.sectionParams') }}</h3>
            
            <template v-if="formData.if_code === 'stripe'">
              <el-row :gutter="24">
                <el-col :xs="24" :sm="24" :md="12">
                  <el-form-item label="Publishable Key" prop="if_params.publishable_key">
                    <el-input v-model="paramsObj.publishable_key" placeholder="请输入Stripe Publishable Key" class="form-control" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12">
                  <el-form-item label="Secret Key" prop="if_params.secret_key">
                    <el-input v-model="paramsObj.secret_key" placeholder="请输入Stripe Secret Key" class="form-control" />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            
            <template v-else-if="formData.if_code === 'paypal'">
              <el-row :gutter="24">
                <el-col :xs="24" :sm="24" :md="12">
                  <el-form-item label="Client ID" prop="if_params.client_id">
                    <el-input v-model="paramsObj.client_id" placeholder="请输入PayPal Client ID" class="form-control" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12">
                  <el-form-item label="Client Secret" prop="if_params.client_secret">
                    <el-input v-model="paramsObj.client_secret" placeholder="请输入PayPal Client Secret" class="form-control" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="24">
                <el-col :xs="24" :sm="24" :md="12">
                  <el-form-item label="品牌名称" prop="if_params.brand_name">
                    <el-input v-model="paramsObj.brand_name" placeholder="请输入品牌名称" class="form-control" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12">
                  <el-form-item label="环境模式" prop="if_params.mode">
                    <el-select v-model="paramsObj.mode" placeholder="请选择环境模式" class="form-control">
                      <el-option label="沙箱环境" value="sandbox" />
                      <el-option label="生产环境" value="live" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="返回URL" prop="if_params.return_url">
                <el-input v-model="paramsObj.return_url" placeholder="请输入支付完成后的返回URL" class="form-control" />
              </el-form-item>
            </template>
            
            <template v-else-if="formData.if_code === 'alipay'">
              <el-form-item :label="$t('Pay.PayConfigForm.label.qrcode_url')" prop="if_params.qrcode_url">
                <el-upload
                  class="qrcode-uploader"
                  :auto-upload="true"
                  :show-file-list="false"
                  :before-upload="(file) => beforeQrcodeUpload(file, 'alipay')"
                >
                  <img v-if="paramsObj.qrcode_url" :src="paramsObj.qrcode_url" class="qrcode-image" />
                  <div v-else class="qrcode-uploader-placeholder">
                    <el-icon><Plus /></el-icon>
                    <div>{{ $t('Pay.PayConfigForm.placeholder.qrcode_upload') }}</div>
                  </div>
                </el-upload>
                <div class="qrcode-tip">{{ getQrcodeUploadTip('alipay') }}</div>
              </el-form-item>
            </template>
            
            <template v-else-if="formData.if_code === 'wechat'">
              <el-form-item :label="$t('Pay.PayConfigForm.label.qrcode_url')" prop="if_params.qrcode_url">
                <el-upload
                  class="qrcode-uploader"
                  :auto-upload="true"
                  :show-file-list="false"
                  :before-upload="(file) => beforeQrcodeUpload(file, 'wechat')"
                >
                  <img v-if="paramsObj.qrcode_url" :src="paramsObj.qrcode_url" class="qrcode-image" />
                  <div v-else class="qrcode-uploader-placeholder">
                    <el-icon><Plus /></el-icon>
                    <div>{{ $t('Pay.PayConfigForm.placeholder.qrcode_upload') }}</div>
                  </div>
                </el-upload>
                <div class="qrcode-tip">{{ getQrcodeUploadTip('wechat') }}</div>
              </el-form-item>
            </template>
            
            <template v-else-if="formData.if_code === 'payme'">
              <el-form-item :label="$t('Pay.PayConfigForm.label.qrcode_url')" prop="if_params.qrcode_url">
                <el-upload
                  class="qrcode-uploader"
                  :auto-upload="true"
                  :show-file-list="false"
                  :before-upload="(file) => beforeQrcodeUpload(file, 'payme')"
                >
                  <img v-if="paramsObj.qrcode_url" :src="paramsObj.qrcode_url" class="qrcode-image" />
                  <div v-else class="qrcode-uploader-placeholder">
                    <el-icon><Plus /></el-icon>
                    <div>{{ $t('Pay.PayConfigForm.placeholder.qrcode_upload') }}</div>
                  </div>
                </el-upload>
                <div class="qrcode-tip">{{ getQrcodeUploadTip('payme') }}</div>
              </el-form-item>
            </template>
            
            <template v-else-if="formData.if_code === 'fps'">
              <el-form-item :label="$t('Pay.PayConfigForm.label.qrcode_url')" prop="if_params.qrcode_url">
                <el-upload
                  class="qrcode-uploader"
                  :auto-upload="true"
                  :show-file-list="false"
                  :before-upload="(file) => beforeQrcodeUpload(file, 'fps')"
                >
                  <img v-if="paramsObj.qrcode_url" :src="paramsObj.qrcode_url" class="qrcode-image" />
                  <div v-else class="qrcode-uploader-placeholder">
                    <el-icon><Plus /></el-icon>
                    <div>{{ $t('Pay.PayConfigForm.placeholder.qrcode_upload') }}</div>
                  </div>
                </el-upload>
                <div class="qrcode-tip">{{ getQrcodeUploadTip('fps') }}</div>
              </el-form-item>
            </template>
            
            <template v-else>
              <el-form-item :label="$t('Pay.PayConfigForm.label.params_json')" prop="paramsJson">
                <el-input 
                  v-model="paramsJson" 
                  type="textarea" 
                  :rows="5" 
                  :placeholder="$t('Pay.PayConfigForm.placeholder.params_json')"
                  class="form-control"
                />
              </el-form-item>
            </template>
          </div>
          
          <div class="form-actions">
            <el-button @click="goBack">{{ $t('Pay.PayConfigForm.button.cancel') }}</el-button>
            <el-button type="primary" @click="submitForm">{{ $t('Pay.PayConfigForm.button.save') }}</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted, computed, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage, FormInstance } from 'element-plus';
  import { Plus } from '@element-plus/icons-vue';
  import { useConfigStore } from '../../stores/configStore';
  import type { ConfigFormData } from '../../types/config';
  import http from '/admin/support/http';
  import { useI18n } from 'vue-i18n';
  
  const { t } = useI18n();
  const route = useRoute();
  const router = useRouter();
  const configStore = useConfigStore();
  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const isEdit = computed(() => route.params.id !== 'create' && route.params.id !== undefined);
  const ifCodes = ref<any[]>([]);
  
  // 上传相关配置
  const uploadHeaders = {
    Authorization: `Bearer ${localStorage.getItem('token')}`
  };
  
  // 选中的支付方式（用于复选框）
  const selectedWayCodes = ref<string[]>([]);
  
  // 监听支付方式选择变化，转为JSON字符串保存到表单数据
  watch(selectedWayCodes, (val) => {
    formData.way_codes = val;
  });
  
  // 参数对象（用于不同支付方式的特定参数）
  const paramsObj = reactive<Record<string, any>>({});
  
  // 参数JSON字符串
  const paramsJson = ref('');
  
  // 监听JSON文本变化，尝试解析
  watch(paramsJson, (val) => {
    try {
      if (val) {
        Object.assign(paramsObj, JSON.parse(val));
      }
    } catch (e) {
      // JSON解析错误，不做任何操作
    }
  });
  
  // 监听paramsObj的变化，同步到formData.if_params
  watch(paramsObj, (val) => {
    formData.if_params = val;
  }, { deep: true });
  
  // 表单数据
  const formData = reactive<ConfigFormData>({
    if_code: '',
    if_name: '',
    if_params: {},
    way_codes: [],
    way_code: '',
    if_rate: 0,
    state: 1,
    icon: '',
    bg_color: '',
    remark: '',
    lang: 'zh_CN'
  });
  
  // 表单验证规则
  const rules = {
    if_code: [{ required: true, message: t('Pay.PayConfigForm.validate.if_code'), trigger: 'change' }],
    if_name: [{ required: true, message: t('Pay.PayConfigForm.validate.if_name'), trigger: 'blur' }],
    way_code: [{ required: true, message: t('Pay.PayConfigForm.validate.way_code'), trigger: 'change' }],
    if_rate: [{ required: true, message: t('Pay.PayConfigForm.validate.if_rate'), trigger: 'blur' }],
    'if_params.publishable_key': [{ required: true, message: t('Pay.PayConfigForm.validate.publishable_key'), trigger: 'blur' }],
    'if_params.secret_key': [{ required: true, message: t('Pay.PayConfigForm.validate.secret_key'), trigger: 'blur' }],
    'if_params.client_id': [{ required: true, message: t('Pay.PayConfigForm.validate.client_id'), trigger: 'blur' }],
    'if_params.client_secret': [{ required: true, message: t('Pay.PayConfigForm.validate.client_secret'), trigger: 'blur' }],
    'if_params.brand_name': [{ required: true, message: t('Pay.PayConfigForm.validate.brand_name'), trigger: 'blur' }],
    'if_params.mode': [{ required: true, message: t('Pay.PayConfigForm.validate.mode'), trigger: 'change' }],
    'if_params.return_url': [{ required: true, message: t('Pay.PayConfigForm.validate.return_url'), trigger: 'blur' }],
    'if_params.qrcode_url': [{ required: true, message: t('Pay.PayConfigForm.validate.qrcode_url'), trigger: 'change' }]
  };
  
  // 图片上传前的校验
  const beforeAvatarUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    const isLt2M = file.size / 1024 / 1024 < 2;
    
    if (!isImage) {
      ElMessage.error(t('Pay.PayConfigForm.message.image_format_error'));
      return false;
    }
    if (!isLt2M) {
      ElMessage.error(t('Pay.PayConfigForm.message.image_size_error'));
      return false;
    }
    
    // 手动处理文件上传
    uploadFile(file);
    
    // 返回false阻止默认上传行为
    return false;
  };
  
  // 手动上传文件
  const uploadFile = async (file: File) => {
    loading.value = true;
    try {
      // 创建FormData
      const fileFormData = new FormData();
      fileFormData.append('file', file);
      fileFormData.append('dir', 'pay/images');
      fileFormData.append('mode', 'OVERWRITE');
      
      // 使用http服务发送请求
      const response = await http.post('/media/upload', fileFormData);
      
      if (response.data.code === 0 || response.data.code === 200) {
        // 更新组件表单数据中的icon
        formData.icon = response.data.data.file.url;
        ElMessage.success(t('Pay.PayConfigForm.message.upload_success'));
      } else {
        ElMessage.error(response.data.message || t('Pay.PayConfigForm.message.upload_fail'));
      }
    } catch (error) {
      console.error('上传失败:', error);
      ElMessage.error(t('Pay.PayConfigForm.message.upload_fail'));
    } finally {
      loading.value = false;
    }
  };
  
  // 二维码图片上传前的校验
  const beforeQrcodeUpload = (file: File, payType: string) => {
    const isImage = file.type.startsWith('image/');
    const isLt2M = file.size / 1024 / 1024 < 2;
    
    if (!isImage) {
      ElMessage.error(t('Pay.PayConfigForm.message.image_format_error'));
      return false;
    }
    if (!isLt2M) {
      ElMessage.error(t('Pay.PayConfigForm.message.image_size_error'));
      return false;
    }
    
    // 手动处理文件上传
    uploadQrcode(file, payType);
    
    // 返回false阻止默认上传行为
    return false;
  };
  
  // 上传二维码图片
  const uploadQrcode = async (file: File, payType: string) => {
    loading.value = true;
    try {
      // 创建FormData
      const fileFormData = new FormData();
      fileFormData.append('file', file);
      fileFormData.append('dir', `pay/qrcode/${payType}`);
      fileFormData.append('mode', 'OVERWRITE');
      
      // 使用http服务发送请求
      const response = await http.post('/media/upload', fileFormData);
      
      if (response.data.code === 0 || response.data.code === 200) {
        // 更新参数对象中的二维码URL
        paramsObj.qrcode_url = response.data.data.file.url;
        ElMessage.success(t('Pay.PayConfigForm.message.qrcode_upload_success'));
      } else {
        ElMessage.error(response.data.message || t('Pay.PayConfigForm.message.qrcode_upload_fail'));
      }
    } catch (error) {
      console.error('二维码上传失败:', error);
      ElMessage.error(t('Pay.PayConfigForm.message.qrcode_upload_fail'));
    } finally {
      loading.value = false;
    }
  };
  
  // 加载支付接口代码
  const loadIfCodes = async () => {
    const result = await configStore.fetchIfCodes();
    ifCodes.value = result;
  };
  
  // 加载配置详情
  const loadConfigDetail = async (id: number) => {
    loading.value = true;
    try {
      const config = await configStore.getConfigDetail(id);
      
      if (config) {
        // 基础字段填充
        formData.if_code = config.if_code;
        formData.if_name = config.if_name;
        formData.way_code = config.way_code;
        formData.if_rate = config.if_rate;
        formData.state = config.state;
        formData.icon = config.icon;
        formData.bg_color = config.bg_color;
        formData.remark = config.remark;
        formData.lang = config.lang || 'zh_CN';
        
        // 处理way_codes（从JSON字符串转为数组）
        try {
          if (config.way_codes) {
            const parsedWayCodes = JSON.parse(config.way_codes);
            if (Array.isArray(parsedWayCodes)) {
              selectedWayCodes.value = parsedWayCodes;
              formData.way_codes = parsedWayCodes;
            }
          }
        } catch (e) {
          console.error('解析way_codes失败:', e);
        }
        
        // 处理if_params（从JSON字符串转为对象）
        try {
          if (config.if_params) {
            const parsedParams = JSON.parse(config.if_params);
            Object.assign(paramsObj, parsedParams);
            paramsJson.value = JSON.stringify(parsedParams, null, 2);
          }
        } catch (e) {
          console.error('解析if_params失败:', e);
        }
      } else {
        ElMessage.error(t('Pay.PayConfigForm.message.fetch_fail'));
        goBack();
      }
    } catch (error) {
      ElMessage.error(t('Pay.PayConfigForm.message.fetch_fail'));
      goBack();
    } finally {
      loading.value = false;
    }
  };
  
  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return;
    console.log(formData);
    await formRef.value.validate(async (valid) => {
      if (valid) {
        loading.value = true;
        try {
          // 处理表单数据
          const submitData: any = { ...formData };
          
          // 检查二维码上传
          if (['alipay', 'wechat', 'payme', 'fps'].includes(formData.if_code) && !paramsObj.qrcode_url) {
            ElMessage.error(t('Pay.PayConfigForm.message.qrcode_required'));
            loading.value = false;
            return;
          }
          
          // 处理way_codes（从数组转为JSON字符串）
          if (Array.isArray(submitData.way_codes)) {
            submitData.way_codes = JSON.stringify(submitData.way_codes);
          }
          
          // 处理if_params（使用paramsObj或paramsJson）
          if (paramsJson.value && !['alipay', 'wechat', 'payme', 'fps'].includes(formData.if_code)) {
            try {
              submitData.if_params = paramsJson.value;
            } catch (e) {
              ElMessage.error(t('Pay.PayConfigForm.message.json_error'));
              loading.value = false;
              return;
            }
          } else {
            // 使用对象中的数据
            submitData.if_params = JSON.stringify(paramsObj);
          }
          
          let result;
          if (isEdit.value) {
            result = await configStore.updateConfig(Number(route.params.id), submitData);
          } else {
            result = await configStore.createConfig(submitData);
          }
          
          if (result.code === 0 || result.code === 200) {
            ElMessage.success(isEdit.value ? t('Pay.PayConfigForm.message.update_success') : t('Pay.PayConfigForm.message.create_success'));
            goBack();
          } else {
            ElMessage.error(result.msg || (isEdit.value ? t('Pay.PayConfigForm.message.update_fail') : t('Pay.PayConfigForm.message.create_fail')));
          }
        } catch (error) {
          ElMessage.error(isEdit.value ? t('Pay.PayConfigForm.message.update_fail') : t('Pay.PayConfigForm.message.create_fail'));
        } finally {
          loading.value = false;
        }
      }
    });
  };
  
  // 返回列表页
  const goBack = () => {
    router.back(); // 返回上一个浏览记录
  };
  
  // 当前支付渠道可用的支付方式
  const availablePayWays = ref<Record<string, string>>({});
  
  // 处理支付渠道变更
  const handleChannelChange = async (channel: string) => {
    if (!channel) return;
    
    // 获取该渠道支持的支付方式
    const result = await configStore.fetchPayWays(channel);
    console.log("hahahaha===>",result);
    if (result && result.data) {
      availablePayWays.value = result.data;
    } else {
      availablePayWays.value = {};
    }
    
    // 重置选中的支付方式
    selectedWayCodes.value = [];
  };
  
  // 渲染支付二维码上传的提示文本
  const getQrcodeUploadTip = (payType: string) => {
    return t(`Pay.PayConfigForm.qrcode_tip.${payType}`) || t('Pay.PayConfigForm.qrcode_tip.default');
  };
  
  // 初始化
  onMounted(async () => {
    await loadIfCodes();
    await configStore.fetchPayWays(); // 加载所有支付方式
    if (isEdit.value) {
      await loadConfigDetail(Number(route.params.id));
      // 如果编辑模式且已有支付渠道，加载该渠道的支付方式
      if (formData.way_code) {
        await handleChannelChange(formData.way_code);
      }
    }
  });
  </script>
  
  <style scoped>
  .config-form-container {
    padding: 20px;
  }
  
  .form-wrapper {
    max-width: 900px;
    margin: 0 auto;
  }
  
  .page-header {
    margin-bottom: 20px;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #409eff;
  }
  
  .page-header h2 {
    margin: 0;
    font-size: 18px;
    color: #303133;
  }
  
  .form-section {
    margin-bottom: 25px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .form-control {
    width: 100%;
  }
  
  /* 修复select下拉框高度问题 */
  :deep(.el-select .el-select__wrapper) {
    line-height: 36px !important;
    min-height: 36px !important;
  }
  
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 15px;
  }
  
  .avatar-uploader {
    display: flex;
    justify-content: flex-start;
  }
  
  .avatar {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
  }
  
  .avatar-uploader-placeholder {
    width: 80px;
    height: 80px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #8c939d;
    cursor: pointer;
  }
  
  .avatar-uploader-placeholder:hover {
    border-color: #409eff;
  }
  
  .qrcode-uploader {
    display: flex;
    justify-content: flex-start;
  }
  
  .qrcode-image {
    width: 120px;
    height: 120px;
    object-fit: contain;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
  }
  
  .qrcode-uploader-placeholder {
    width: 120px;
    height: 120px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #8c939d;
    cursor: pointer;
  }
  
  .qrcode-uploader-placeholder:hover {
    border-color: #409eff;
  }
  
  .qrcode-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    text-align: left;
  }
  
  /* 上传控件样式调整 */
  .upload-item :deep(.el-form-item__content) {
    display: flex;
    align-items: flex-start;
  }
  
  /* 响应式调整 */
  @media (max-width: 768px) {
    .form-wrapper {
      max-width: 100%;
    }
    
    .form-actions {
      flex-direction: column;
      align-items: center;
    }
    
    .form-actions .el-button {
      width: 100%;
      margin-bottom: 10px;
    }
  }
  </style>