<template>
    <div class="order-detail-container">
      <el-page-header @back="goBack" :title="$t('Pay.PayOrder.list.back_to_list')">
        <template #content>
          <span class="page-title">{{ $t('Pay.PayOrder.detail.title') }}</span>
        </template>
      </el-page-header>
      
      <div class="detail-content" v-loading="loading">
        <el-card v-if="order" class="detail-card">
          <template #header>
            <div class="card-header">
              <span>{{ $t('Pay.PayOrder.detail.base_info') }}</span>
              <div>
                <el-tag :type="getStatusTagType(order.state)" size="large">
                  {{ getStatusText(order.state) }}
                </el-tag>
              </div>
            </div>
          </template>
          
          <el-descriptions :column="3" border>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.pay_order_id')" :span="2">{{ order.pay_order_id }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.state')">
              <el-tag :type="getStatusTagType(order.state)">{{ getStatusText(order.state) }}</el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.mch_order_no')">{{ order.mch_order_no }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.if_code')">{{ order.if_code }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.way_code')">
              <el-tag>{{ order.way_code }}</el-tag>
            </el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.amount')" :span="2">
              <span class="amount">{{ formatAmount(order.amount) }}</span> {{ order.currency }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.fee')">
              {{ formatAmount(order.mch_fee_amount) }} ({{ order.mch_fee_rate }}%)
            </el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.subject')">{{ order.subject }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.body')" :span="2">{{ order.body || '-' }}</el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.created_at')">{{ formatDateTime(order.created_at) }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.expired_time')">{{ formatDateTime(order.expired_time) }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.success_time')">{{ formatDateTime(order.success_time) }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <el-card v-if="order" class="detail-card">
          <template #header>
            <div class="card-header">
              <span>{{ $t('Pay.PayOrder.detail.payment_info') }}</span>
            </div>
          </template>
          
          <el-descriptions :column="3" border>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.refund_state')">
              <el-tag :type="order.refund_state === 1 ? 'warning' : 'info'">
                {{ order.refund_state === 1 ? $t('Pay.PayOrder.detail.state.refunded') : $t('Pay.PayOrder.detail.state.not_refunded') }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.refund_times')">{{ order.refund_times || 0 }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.refund_amount')">{{ formatAmount(order.refund_amount || 0) }} {{ order.currency }}</el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.channel_order_no')" :span="2">{{ order.channel_order_no || '-' }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.channel_user')">{{ order.channel_user || '-' }}</el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.client_ip')">{{ order.client_ip }}</el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.err_code')" :span="2">{{ order.err_code || '-' }}</el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.err_msg')" :span="3">{{ order.err_msg || '-' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <el-card v-if="order" class="detail-card">
          <template #header>
            <div class="card-header">
              <span>{{ $t('Pay.PayOrder.detail.callback_info') }}</span>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.notify_state')">
              <el-tag :type="order.notify_state === 1 ? 'success' : 'info'">
                {{ order.notify_state === 1 ? $t('Pay.PayOrder.detail.state.notified') : $t('Pay.PayOrder.detail.state.not_notified') }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.notify_url')">{{ order.notify_url || '-' }}</el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.return_url')" :span="2">{{ order.return_url || '-' }}</el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.ext_param')" :span="2">
              <pre v-if="order.ext_param" class="json-code">{{ formatJson(order.ext_param) }}</pre>
              <span v-else>-</span>
            </el-descriptions-item>
            
            <el-descriptions-item :label="$t('Pay.PayOrder.detail.labels.channel_extra')" :span="2">
              <pre v-if="order.channel_extra" class="json-code">{{ formatJson(order.channel_extra) }}</pre>
              <span v-else>-</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <div v-if="order && order.state === 2 && order.refund_state === 0" class="action-container">
          <el-button type="danger" @click="handleRefund" :loading="refundLoading">
            {{ $t('Pay.PayOrder.detail.button.apply_refund') }}
          </el-button>
        </div>
        
        <el-empty v-if="!order && !loading" :description="$t('Pay.PayOrder.detail.empty')"></el-empty>
      </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useOrderStore } from '../../stores/orderStore'
import { OrderStatus, type PayOrder } from '../../types/order'
import { useI18n } from 'vue-i18n'

const route = useRoute()
const router = useRouter()
const orderStore = useOrderStore()
const { t } = useI18n()

const loading = ref(false)
const refundLoading = ref(false)
const orderId = ref(parseInt(route.params.id as string))
const order = ref<PayOrder | null>(null)

// 获取订单详情
onMounted(async () => {
  if (orderId.value) {
    loading.value = true
    try {
      await orderStore.fetchOrder(orderId.value)
      order.value = orderStore.currentOrder
      if (!order.value) {
        ElMessage.error(t('Pay.PayOrder.detail.empty'))
      }
    } catch (error) {
      console.error('获取订单详情失败', error)
      ElMessage.error(t('Pay.PayOrder.refund.error'))
    } finally {
      loading.value = false
    }
  }
})

// 获取状态文本
function getStatusText(status: OrderStatus): string {
  return t(`Pay.PayOrder.state.${status}`) || t('Pay.PayOrder.state.unknown')
}

// 获取状态标签类型
function getStatusTagType(status: OrderStatus): string {
  const typeMap = {
    [OrderStatus.CREATED]: 'info',
    [OrderStatus.PROCESSING]: 'warning',
    [OrderStatus.SUCCESS]: 'success',
    [OrderStatus.FAILED]: 'danger',
    [OrderStatus.CLOSED]: 'info',
    [OrderStatus.REFUNDED]: 'warning',
    [OrderStatus.EXPIRED]: 'info'
  }
  return typeMap[status] || 'info'
}

// 格式化金额 - 从分转换为元
function formatAmount(amount: number): string {
  if (amount === undefined || amount === null) return '0.00'
  // 将分转换为元，并格式化为2位小数
  return (amount / 100).toFixed(2)
}

// 格式化日期时间
function formatDateTime(dateStr: string): string {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}

// 格式化JSON
function formatJson(jsonData: any): string {
  if (typeof jsonData === 'string') {
    try {
      jsonData = JSON.parse(jsonData)
    } catch (e) {
      return jsonData
    }
  }
  return JSON.stringify(jsonData, null, 2)
}

// 返回列表
function goBack() {
  router.push('/pay/order')
}

// 处理退款
async function handleRefund() {
  if (!order.value) return
  
  try {
    const confirmMessage = t('Pay.PayOrder.refund.confirm_message', [
      order.value.pay_order_id,
      formatAmount(order.value.amount),
      order.value.currency
    ])
    
    await ElMessageBox.confirm(
      confirmMessage,
      t('Pay.PayOrder.refund.confirm_title'),
      {
        type: 'warning',
        confirmButtonText: t('Pay.PayOrder.refund.confirm_button'),
        cancelButtonText: t('Pay.PayOrder.refund.cancel_button')
      }
    )
    
    refundLoading.value = true
    const result = await orderStore.refundOrder(order.value.id)
    
    if (result.code === 0) {
      ElMessage.success(t('Pay.PayOrder.refund.success'))
      // 重新加载订单详情
      await orderStore.fetchOrder(orderId.value)
      order.value = orderStore.currentOrder
    } else {
      ElMessage.error(t('Pay.PayOrder.refund.fail', [result.msg]))
    }
  } catch (error) {
    // 用户取消或其他错误
    if (error !== 'cancel') {
      console.error('退款操作失败', error)
      ElMessage.error(t('Pay.PayOrder.refund.error'))
    }
  } finally {
    refundLoading.value = false
  }
}
</script>

<style scoped>
.order-detail-container {
  padding: 20px;
}
.page-title {
  font-size: 18px;
  font-weight: bold;
}
.detail-card {
  margin-top: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.amount {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
}
.json-code {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 150px;
  overflow: auto;
}
.action-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>