<template>
    <div class="order-list-container">
      <!-- 统计数据卡片 -->
      <!-- <order-statistics :statistics="statistics" /> -->
      
      <!-- 过滤器 -->
      <order-filter @filter="handleFilter" />
      
      <!-- 订单表格 -->
      <el-card class="order-table-card">
        <template #header>
          <div class="card-header">
            <span>{{ $t('Pay.PayOrder.list.title') }}</span>
            <div class="actions">
              <el-button type="primary" @click="refreshData" :loading="loading" size="small">
                <el-icon><Refresh /></el-icon> {{ $t('Pay.PayOrder.list.refresh') }}
              </el-button>
            </div>
          </div>
        </template>
        
        <order-table 
          :orders="orders" 
          :loading="loading"
          :total="totalCount"
          :current-page="page"
          :page-size="limit"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @view-detail="viewOrderDetail"
          @refund="handleRefund"
        />
      </el-card>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Refresh } from '@element-plus/icons-vue'
  import { useOrderStore } from '../../stores/orderStore'
  import OrderStatistics from './components/OrderStatistics.vue'
  import OrderFilter from './components/OrderFilter.vue'
  import OrderTable from './components/OrderTable.vue'
  import type { OrderQueryParams } from '../../types/order'
  import { useI18n } from 'vue-i18n'
  
  const router = useRouter()
  const orderStore = useOrderStore()
  const { t } = useI18n()
  
  const orders = computed(() => orderStore.orders)
  const loading = computed(() => orderStore.loading)
  const totalCount = computed(() => orderStore.totalCount)
  const page = computed(() => orderStore.page)
  const limit = computed(() => orderStore.limit)
  const statistics = computed(() => orderStore.statistics)
  
  // 查询参数
  const queryParams = ref<OrderQueryParams>({})
  
  // 初始化加载
  onMounted(async () => {
    await loadData()
  })
  
  // 加载数据
  async function loadData() {
    await Promise.all([
      orderStore.fetchOrders(queryParams.value),
      // orderStore.fetchStatistics(queryParams.value)
    ])
  }
  
  // 刷新数据
  async function refreshData() {
    await loadData()
    ElMessage.success(t('Pay.PayOrder.list.refresh_success'))
  }
  
  // 处理过滤器
  function handleFilter(filters: OrderQueryParams) {
    queryParams.value = filters
    orderStore.page = 1 // 重置到第一页
    loadData()
  }
  
  // 处理分页
  function handlePageChange(newPage: number) {
    orderStore.page = newPage
    loadData()
  }
  
  // 处理每页数量变化
  function handleSizeChange(newLimit: number) {
    orderStore.limit = newLimit
    loadData()
  }
  
  // 查看订单详情
  function viewOrderDetail(orderId: number) {
    router.push(`/pay/order/${orderId}`)
  }
  
  // 处理退款申请
  async function handleRefund(order: any) {
    try {
      const confirmMessage = t('Pay.PayOrder.refund.confirm_message', [
        order.pay_order_id,
        (order.amount / 100).toFixed(2),
        order.currency
      ])
      
      await ElMessageBox.confirm(
        confirmMessage,
        t('Pay.PayOrder.refund.confirm_title'),
        {
          type: 'warning',
          confirmButtonText: t('Pay.PayOrder.refund.confirm_button'),
          cancelButtonText: t('Pay.PayOrder.refund.cancel_button')
        }
      )
      
      const result = await orderStore.refundOrder(order.pay_order_id)
      if (result.code === 0) {
        ElMessage.success(t('Pay.PayOrder.refund.success'))
        await loadData() // 刷新数据
      } else {
        ElMessage.error(t('Pay.PayOrder.refund.fail', [result.msg]))
      }
    } catch (error) {
      // 用户取消或其他错误
      if (error !== 'cancel') {
        console.error('退款操作失败', error)
        ElMessage.error(t('Pay.PayOrder.refund.error'))
      }
    }
  }
  </script>
  
  <style scoped>
  .order-list-container {
    padding: 20px;
  }
  .order-table-card {
    margin-top: 20px;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  </style>