<template>
  <el-card class="filter-card" shadow="hover">
    <el-form :model="filterForm" label-position="top" @submit.prevent>
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item :label="$t('Pay.PayOrder.filter.pay_order_id')">
            <el-input 
              v-model="filterForm.pay_order_id" 
              :placeholder="$t('Pay.PayOrder.filter.placeholder.pay_order_id')" 
              clearable 
              class="filter-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item :label="$t('Pay.PayOrder.filter.mch_order_no')">
            <el-input 
              v-model="filterForm.mch_order_no" 
              :placeholder="$t('Pay.PayOrder.filter.placeholder.mch_order_no')" 
              clearable 
              class="filter-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item :label="$t('Pay.PayOrder.filter.if_code')">
            <el-input 
              v-model="filterForm.if_code" 
              :placeholder="$t('Pay.PayOrder.filter.placeholder.if_code')" 
              clearable 
              class="filter-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item :label="$t('Pay.PayOrder.filter.way_code')">
            <el-select 
              v-model="filterForm.way_code" 
              :placeholder="$t('Pay.PayOrder.filter.placeholder.way_code')" 
              clearable 
              class="filter-input"
            >
              <el-option 
                v-for="item in wayCodeOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item :label="$t('Pay.PayOrder.filter.state')">
            <el-select 
              v-model="filterForm.state" 
              :placeholder="$t('Pay.PayOrder.filter.placeholder.state')" 
              clearable 
              class="filter-input"
            >
              <el-option 
                v-for="item in statusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" :lg="12">
          <el-form-item :label="$t('Pay.PayOrder.filter.created_time')">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              :range-separator="$t('Pay.PayOrder.filter.date_range.separator')"
              :start-placeholder="$t('Pay.PayOrder.filter.date_range.start')"
              :end-placeholder="$t('Pay.PayOrder.filter.date_range.end')"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="date-range-picker"
            />
          </el-form-item>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="8" :lg="6">
          <el-form-item class="action-buttons">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              {{ $t('Pay.PayOrder.filter.search') }}
            </el-button>
            <el-button @click="resetFilter">
              <el-icon><Refresh /></el-icon>
              {{ $t('Pay.PayOrder.filter.reset') }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { OrderStatus } from '../../../types/order'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// 定义过滤表单
const filterForm = ref({
  pay_order_id: '',
  mch_order_no: '',
  if_code: '',
  way_code: '',
  state: undefined as OrderStatus | undefined,
  start_time: '',
  end_time: ''
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 监听日期范围变化
watch(dateRange, (val) => {
  if (val) {
    filterForm.value.start_time = val[0]
    filterForm.value.end_time = val[1]
  } else {
    filterForm.value.start_time = ''
    filterForm.value.end_time = ''
  }
})

// 状态选项
const statusOptions = computed(() => [
  { value: OrderStatus.CREATED, label: t('Pay.PayOrder.state.0') },
  { value: OrderStatus.PROCESSING, label: t('Pay.PayOrder.state.1') },
  { value: OrderStatus.SUCCESS, label: t('Pay.PayOrder.state.2') },
  { value: OrderStatus.FAILED, label: t('Pay.PayOrder.state.3') },
  { value: OrderStatus.CLOSED, label: t('Pay.PayOrder.state.4') },
  { value: OrderStatus.REFUNDED, label: t('Pay.PayOrder.state.5') },
  { value: OrderStatus.EXPIRED, label: t('Pay.PayOrder.state.6') }
])

// 支付方式选项
const wayCodeOptions = computed(() => [
  { value: 'paypal', label: t('Pay.PayOrder.payment_methods.paypal') },
  { value: 'stripe', label: t('Pay.PayOrder.payment_methods.stripe') },
  { value: 'alipay', label: t('Pay.PayOrder.payment_methods.alipay') },
  { value: 'wechatpay', label: t('Pay.PayOrder.payment_methods.wechatpay') },
  { value: 'payme', label: t('Pay.PayOrder.payment_methods.payme') }
])

const emit = defineEmits(['filter'])

// 处理查询
function handleSearch() {
  emit('filter', { ...filterForm.value })
}

// 重置过滤条件
function resetFilter() {
  filterForm.value = {
    pay_order_id: '',
    mch_order_no: '',
    if_code: '',
    way_code: '',
    state: undefined,
    start_time: '',
    end_time: ''
  }
  dateRange.value = null
  emit('filter', { ...filterForm.value })
}
</script>

<style scoped>
.filter-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

/* 设置表单项标签样式 */
:deep(.el-form-item__label) {
  padding-bottom: 4px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

/* 统一所有输入类组件样式 */
.filter-input {
  width: 100%;
  height: 36px;
}

/* 统一日期选择器样式 */
.date-range-picker {
  width: 100%;
  height: 36px;
}

/* 确保所有输入框内部样式一致 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper),
:deep(.el-date-editor .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  transition: box-shadow 0.2s;
  background-color: #fff;
}

:deep(.el-input__wrapper:hover),
:deep(.el-select .el-input__wrapper:hover),
:deep(.el-date-editor .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus),
:deep(.el-date-editor .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 按钮样式优化 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  height: 100%;
  padding-top: 28px; /* 对齐其他表单项的底部 */
}

.action-buttons :deep(.el-form-item__content) {
  display: flex;
  justify-content: flex-end;
}

.action-buttons .el-button {
  margin-left: 10px;
  height: 36px;
  padding: 8px 16px;
  border-radius: 4px;
}

.action-buttons .el-button .el-icon {
  margin-right: 4px;
}


:deep(.el-select .el-select__wrapper) {
    line-height: 36px !important;
    min-height: 36px !important;
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .action-buttons {
    padding-top: 0;
    justify-content: flex-start;
    margin-top: 12px;
  }
  
  .action-buttons :deep(.el-form-item__content) {
    justify-content: flex-start;
  }
  
  .action-buttons .el-button:first-child {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }
  
  .date-range-picker {
    width: 100%;
  }
}
</style>