<template>
    <div class="statistics-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total-icon">
                <el-icon><Tickets /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">{{ $t('Pay.PayOrder.statistics.total_count') }}</div>
                <div class="stat-value">{{ statistics?.total_count || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success-icon">
                <el-icon><Select /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">{{ $t('Pay.PayOrder.statistics.success_count') }}</div>
                <div class="stat-value">{{ statistics?.success_count || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon amount-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">{{ $t('Pay.PayOrder.statistics.total_amount') }}</div>
                <div class="stat-value">¥ {{ formatAmount(statistics?.total_amount || 0) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon refund-icon">
                <el-icon><TurnOff /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">{{ $t('Pay.PayOrder.statistics.refund_amount') }}</div>
                <div class="stat-value">¥ {{ formatAmount(statistics?.refund_amount || 0) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { Tickets, Select, Money, TurnOff } from '@element-plus/icons-vue'
  import type { OrderStatistics } from '../../../types/order'
  import { useI18n } from 'vue-i18n'
  
  defineProps<{
    statistics: OrderStatistics | null
  }>()
  
  const { t } = useI18n()
  
  // 格式化金额
  function formatAmount(amount: number): string {
    return amount.toFixed(2)
  }
  </script>
  
  <style scoped>
  .statistics-container {
    margin-bottom: 20px;
  }
  .stat-card {
    height: 100px;
    transition: all 0.3s;
  }
  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  .stat-content {
    display: flex;
    align-items: center;
    height: 100%;
  }
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
  }
  .stat-icon .el-icon {
    font-size: 30px;
    color: white;
  }
  .total-icon {
    background-color: #409eff;
  }
  .success-icon {
    background-color: #67c23a;
  }
  .amount-icon {
    background-color: #e6a23c;
  }
  .refund-icon {
    background-color: #f56c6c;
  }
  .stat-info {
    flex: 1;
  }
  .stat-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }
  .stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #303133;
  }
  .stat-rate {
    font-size: 12px;
    color: #67c23a;
    margin-top: 5px;
  }
  </style>