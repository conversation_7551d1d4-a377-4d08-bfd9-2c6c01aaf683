<template>
    <div class="order-table">
      <el-table 
        :data="orders" 
        border 
        style="width: 100%"
        v-loading="loading"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="id" :label="$t('Pay.PayOrder.table.id')" width="80" />
        <el-table-column prop="pay_order_id" :label="$t('Pay.PayOrder.table.pay_order_id')" width="180" show-overflow-tooltip />
        <el-table-column prop="mch_order_no" :label="$t('Pay.PayOrder.table.mch_order_no')" width="180" show-overflow-tooltip />
        
        <el-table-column prop="if_code" :label="$t('Pay.PayOrder.table.if_code')" width="120" />
        <el-table-column prop="way_code" :label="$t('Pay.PayOrder.table.way_code')" width="120">
          <template #default="scope">
            <el-tag :type="getWayCodeTagType(scope.row.way_code)">
              {{ scope.row.way_code }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="amount" :label="$t('Pay.PayOrder.table.amount')" width="120">
          <template #default="scope">
            <span>{{ formatAmount(scope.row.amount) }} {{ scope.row.currency }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="state" :label="$t('Pay.PayOrder.table.state')" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.state)">
              {{ getStatusText(scope.row.state) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="subject" :label="$t('Pay.PayOrder.table.subject')" min-width="150" show-overflow-tooltip />
        
        <el-table-column prop="created_at" :label="$t('Pay.PayOrder.table.created_at')" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column fixed="right" :label="$t('Pay.PayOrder.table.actions')" width="180">
          <template #default="scope">
            <el-button 
              link 
              type="primary" 
              size="small" 
              @click="$emit('view-detail', scope.row.id)"
            >
              {{ $t('Pay.PayOrder.table.view') }}
            </el-button>
            
            <el-button 
              v-if="scope.row.state === 2 && scope.row.refund_state === 0" 
              link 
              type="danger" 
              size="small" 
              @click="$emit('refund', scope.row)"
            >
              {{ $t('Pay.PayOrder.table.refund') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 15, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @update:current-page="emit('update:currentPage', $event)"
          @update:page-size="emit('update:pageSize', $event)"
        />
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, computed } from 'vue'
  import { OrderStatus, type PayOrder } from '../../../types/order'
  import { useI18n } from 'vue-i18n'
  
  const props = defineProps({
    orders: {
      type: Array as () => PayOrder[],
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    total: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 15
    }
  })
  
  const emit = defineEmits(['view-detail', 'refund', 'page-change', 'size-change', 'update:currentPage', 'update:pageSize'])
  const { t } = useI18n()
  
  // 格式化金额 - 从分转换为元
  function formatAmount(amount: number): string {
    if (amount === undefined || amount === null) return '0.00'
    // 将分转换为元，并格式化为2位小数
    return (amount / 100).toFixed(2)
  }
  
  // 格式化日期时间
  function formatDateTime(dateStr: string): string {
    if (!dateStr) return '-'
    const date = new Date(dateStr)
    return date.toLocaleString()
  }
  
  // 获取状态文本
  function getStatusText(status: OrderStatus): string {
    return t(`Pay.PayOrder.state.${status}`) || t('Pay.PayOrder.state.unknown')
  }
  
  // 获取状态标签类型
  function getStatusTagType(status: OrderStatus): string {
    const typeMap = {
      [OrderStatus.CREATED]: 'info',
      [OrderStatus.PROCESSING]: 'warning',
      [OrderStatus.SUCCESS]: 'success',
      [OrderStatus.FAILED]: 'danger',
      [OrderStatus.CLOSED]: 'info',
      [OrderStatus.REFUNDED]: 'warning',
      [OrderStatus.EXPIRED]: 'info'
    }
    return typeMap[status] || 'info'
  }
  
  // 获取支付方式标签类型
  function getWayCodeTagType(wayCode: string): string {
    const typeMap: Record<string, string> = {
      'paypal': 'primary',
      'stripe': 'success',
      'alipay': 'info',
      'wechatpay': 'success',
      'payme': 'warning'
    }
    return typeMap[wayCode] || 'info'
  }
  
  // 处理页码变化
  function handleCurrentChange(val: number) {
    emit('page-change', val)
    emit('update:currentPage', val)
  }
  
  // 处理每页数量变化
  function handleSizeChange(val: number) {
    emit('size-change', val)
    emit('update:pageSize', val)
  }
  </script>
  
  <style scoped>
  .order-table {
    width: 100%;
  }
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  </style>