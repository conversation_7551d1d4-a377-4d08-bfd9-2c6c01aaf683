<!-- Modules/Pay/views/ui/passages/PassageList.vue -->
<template>
  <div class="passage-list-container">
    <!-- 搜索过滤区域 -->
    <el-card class="filter-card">
      <template #header>
        <div class="card-header">
          <span>{{ $t('Pay.PayPassage.list.title') }}</span>
          <el-button type="primary" @click="handleCreateClick" size="small">
            <el-icon><Plus /></el-icon> {{ $t('Pay.PayPassage.list.addButton') }}
          </el-button>
        </div>
      </template>
      
      <el-form :model="searchForm" inline>
        <!-- <el-form-item label="支付接口">
          <el-select v-model="searchForm.if_code" placeholder="全部" clearable>
            <el-option v-for="item in ifCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select v-model="searchForm.way_code" placeholder="全部" clearable>
            <el-option v-for="item in wayCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->
        <el-form-item :label="$t('Pay.PayPassage.form.label.state')">
          <el-select v-model="searchForm.state" :placeholder="$t('Pay.PayPassage.form.placeholder.all')" clearable style="width: 250px;">
            <el-option :label="$t('Pay.PayPassage.form.state.enabled')" :value="1" />
            <el-option :label="$t('Pay.PayPassage.form.state.disabled')" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('Pay.PayPassage.list.search') }}</el-button>
          <el-button @click="resetSearch">{{ $t('Pay.PayPassage.list.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 通道表格区域 -->
    <el-card class="passage-table-card">
      <template #header>
        <div class="card-header">
          <span>{{ $t('Pay.PayPassage.list.tableTitle') }}</span>
          <div class="actions">
            <el-button type="primary" @click="loadData" :loading="passageStore.loading" size="small">
              <el-icon><Refresh /></el-icon> {{ $t('Pay.PayPassage.list.refresh') }}
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="passageStore.loading"
        :data="passageStore.passages"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" :label="$t('Pay.PayPassage.table.id')" width="80" />
        <el-table-column prop="if_code" :label="$t('Pay.PayPassage.table.if_code')" min-width="120" />
        <el-table-column prop="way_code" :label="$t('Pay.PayPassage.table.way_code')" min-width="120" />
        <el-table-column :label="$t('Pay.PayPassage.table.rate')" min-width="120">
          <template #default="scope">
            {{ formatRate(scope.row.rate) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('Pay.PayPassage.table.state')" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.state === 1 ? 'success' : 'danger'">
              {{ scope.row.state === 1 ? $t('Pay.PayPassage.form.state.enabled') : $t('Pay.PayPassage.form.state.disabled') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" :label="$t('Pay.PayPassage.table.created_at')" width="180" />
        <el-table-column :label="$t('Pay.PayPassage.table.actions')" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">{{ $t('Pay.PayPassage.form.button.edit') }}</el-button>
            <el-button type="primary" link @click="handleView(scope.row)">{{ $t('Pay.PayPassage.form.button.view') }}</el-button>
            <el-popconfirm :title="$t('Pay.PayPassage.list.message.deleteConfirm')" @confirm="handleDelete(scope.row.id)">
              <template #reference>
                <el-button type="danger" link>{{ $t('Pay.PayPassage.form.button.delete') }}</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页控件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 15, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="passageStore.totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 编辑/创建弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? $t('Pay.PayPassage.list.dialogEdit') : $t('Pay.PayPassage.list.dialogAdd')"
      width="650px"
      destroy-on-close
    >
      <passage-form
        :passage="currentPassage"
        :is-edit="isEdit"
        @submit="handleFormSubmit"
        @cancel="dialogVisible = false"
      />
    </el-dialog>
    
    <!-- 查看详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="$t('Pay.PayPassage.list.dialogDetail')"
      width="650px"
    >
      <passage-detail :passage="currentPassage" />
    </el-dialog>
  </div>
</template>
  
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Plus } from '@element-plus/icons-vue'
import { usePassageStore } from '../../stores/passageStore'
import { useConfigStore } from '../../stores/configStore'
import PassageForm from './components/PassageForm.vue'
import PassageDetail from './components/PassageDetail.vue'
import type { PayPassage } from '../../../views/types/passage'
import type { CodeOption } from '../../../views/types/config'
import { useI18n } from 'vue-i18n'

const passageStore = usePassageStore()
const configStore = useConfigStore()
const { t } = useI18n()

// 分页相关
const currentPage = ref(1)
const pageSize = ref(15)

// 搜索表单
const searchForm = reactive({
  if_code: '',
  way_code: '',
  state: null as number | null
})

// 接口选项和支付方式选项
const ifCodeOptions = ref<CodeOption[]>([])
const wayCodeOptions = ref<CodeOption[]>([])

// 弹窗控制
const dialogVisible = ref(false)
const detailVisible = ref(false)
const isEdit = ref(false)
const currentPassage = ref<PayPassage | null>(null)

// 格式化费率显示
const formatRate = (rate: number): string => {
  if (rate === undefined || rate === null) return '0%'
  return (rate * 100).toFixed(4) + '%'
}

// 初始化
onMounted(async () => {
  // 加载支付方式和渠道数据
  await loadPayOptions()
  loadData()
})

// 加载支付选项
const loadPayOptions = async () => {
  try {
    // 加载支付方式和渠道选项
    await configStore.fetchPayWays()
    
    // 将支付渠道转换为选项格式
    ifCodeOptions.value = Object.entries(configStore.payChannels).map(([value, label]) => ({
      value,
      label: label as string
    }))
    
    // 提取所有支付方式，去重
    const allWays = new Set<string>()
    Object.values(configStore.payWaysByChannel).forEach(ways => {
      if (ways && typeof ways === 'object') {
        Object.keys(ways).forEach(key => allWays.add(key))
      }
    })
    
    // 将支付方式转换为选项格式
    wayCodeOptions.value = Array.from(allWays).map(code => ({
      value: code,
      label: configStore.payWays[code] || code
    }))
  } catch (error) {
    console.error('加载支付选项失败:', error)
    ElMessage.error(t('Pay.PayPassage.list.message.loadOptionsFail'))
  }
}

// 加载列表数据
const loadData = () => {
  passageStore.page = currentPage.value
  passageStore.limit = pageSize.value
  
  const params = {
    if_code: searchForm.if_code,
    way_code: searchForm.way_code,
    state: searchForm.state as number | undefined
  }
  
  passageStore.fetchPassages(params)
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'state') {
      searchForm[key as keyof typeof searchForm] = null
    } else {
      // @ts-ignore - 类型错误可以使用ts-ignore跳过
      searchForm[key] = ''
    }
  })
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

// 创建按钮点击
const handleCreateClick = () => {
  isEdit.value = false
  currentPassage.value = null
  dialogVisible.value = true
}

// 编辑按钮点击
const handleEdit = (row: PayPassage) => {
  isEdit.value = true
  currentPassage.value = { ...row }
  dialogVisible.value = true
}

// 查看按钮点击
const handleView = (row: PayPassage) => {
  currentPassage.value = { ...row }
  detailVisible.value = true
}

// 删除按钮点击
const handleDelete = async (id: number) => {
  try {
    const result = await passageStore.deletePassage(id)
    ElMessage.success(t('Pay.PayPassage.list.message.deleteSuccess'))
    loadData()
  } catch (error) {
    ElMessage.error(t('Pay.PayPassage.list.message.deleteFail'))
  }
}

// 表单提交
const handleFormSubmit = async (formData: Partial<PayPassage>) => {
  try {
    let result
    if (isEdit.value && currentPassage.value) {
      result = await passageStore.updatePassage(currentPassage.value.id, formData)
    } else {
      result = await passageStore.createPassage(formData)
    }
    
    ElMessage.success(isEdit.value ? t('Pay.PayPassage.list.message.updateSuccess') : t('Pay.PayPassage.list.message.createSuccess'))
    dialogVisible.value = false
    loadData()
  } catch (error) {
    ElMessage.error(isEdit.value ? t('Pay.PayPassage.list.message.updateFail') : t('Pay.PayPassage.list.message.createFail'))
  }
}
</script>
  
<style scoped>
.passage-list-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.passage-table-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}


:deep(.el-select .el-select__wrapper) {
    line-height: 36px !important;
    min-height: 36px !important;
}
</style>