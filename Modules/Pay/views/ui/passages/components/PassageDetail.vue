<!-- Modules/Pay/views/ui/passages/components/PassageDetail.vue -->
<template>
    <div class="passage-detail" v-if="passage">
      <el-descriptions border :column="1" direction="vertical">
        <el-descriptions-item :label="$t('Pay.PayPassage.detail.id')">{{ passage.id }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayPassage.detail.if_code')">{{ passage.if_code }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayPassage.detail.way_code')">{{ passage.way_code }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayPassage.detail.rate')">{{ formatRate(passage.rate) }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayPassage.detail.state')">
          <el-tag :type="passage.state === 1 ? 'success' : 'danger'">
            {{ passage.state === 1 ? $t('Pay.PayPassage.form.state.enabled') : $t('Pay.PayPassage.form.state.disabled') }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayPassage.detail.created_at')">{{ passage.created_at || '-' }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayPassage.detail.updated_at')">{{ passage.updated_at || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  import type { PayPassage } from '../../../../views/types/passage'
  import { useI18n } from 'vue-i18n'
  
  const props = defineProps<{
    passage: PayPassage | null
  }>()
  
  const { t } = useI18n()
  
  // 格式化费率显示
  const formatRate = (rate: number): string => {
    if (rate === undefined || rate === null) return '0%'
    return (rate * 100).toFixed(4) + '%'
  }
  </script>
  
  <style scoped>
  .passage-detail {
    padding: 10px;
  }
  </style>