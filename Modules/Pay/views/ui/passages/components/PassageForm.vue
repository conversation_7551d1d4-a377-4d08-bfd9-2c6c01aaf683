<!-- Modules/Pay/views/ui/passages/components/PassageForm.vue -->
<template>
    <div class="passage-form">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="right"
      >
        <el-form-item :label="$t('Pay.PayPassage.form.label.if_code')" prop="if_code">
          <el-select v-model="form.if_code" :placeholder="$t('Pay.PayPassage.form.placeholder.if_code')" style="width: 100%">
            <el-option v-for="item in ifCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item :label="$t('Pay.PayPassage.form.label.way_code')" prop="way_code">
          <el-select v-model="form.way_code" :placeholder="$t('Pay.PayPassage.form.placeholder.way_code')" style="width: 100%">
            <el-option v-for="item in wayCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        
        <el-form-item :label="$t('Pay.PayPassage.form.label.rate')" prop="rate">
          <el-input-number 
            v-model="form.rate" 
            :precision="4" 
            :step="0.0001" 
            :min="0" 
            :max="1"
            style="width: 100%"
            :controls="false"
            :placeholder="$t('Pay.PayPassage.form.placeholder.rate')"
          />
          <div class="form-tip">{{ $t('Pay.PayPassage.form.tip.rate') }}</div>
        </el-form-item>
        
        <el-form-item :label="$t('Pay.PayPassage.form.label.state')" prop="state">
          <el-radio-group v-model="form.state">
            <el-radio :label="1">{{ $t('Pay.PayPassage.form.state.enabled') }}</el-radio>
            <el-radio :label="0">{{ $t('Pay.PayPassage.form.state.disabled') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">{{ isEdit ? $t('Pay.PayPassage.form.button.save') : $t('Pay.PayPassage.form.button.create') }}</el-button>
          <el-button @click="cancel">{{ $t('Pay.PayPassage.form.button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, watch, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import type { PayPassage } from '../../../../views/types/passage'
  import { useI18n } from 'vue-i18n'
  
  const props = defineProps<{
    passage: PayPassage | null
    isEdit: boolean
  }>()
  
  const emit = defineEmits<{
    (e: 'submit', data: Partial<PayPassage>): void
    (e: 'cancel'): void
  }>()
  
  const { t } = useI18n()
  
  // 表单实例和数据
  const formRef = ref<FormInstance>()
  const form = reactive<Partial<PayPassage>>({
    if_code: '',
    way_code: '',
    rate: 0,
    state: 1
  })
  
  // 接口选项
  const ifCodeOptions = ref([
    { label: 'WxPay', value: 'wxpay' },
    { label: 'AliPay', value: 'alipay' },
    { label: 'UnionPay', value: 'unionpay' },
    { label: 'Stripe', value: 'stripe' },
    { label: 'PayPal', value: 'paypal' }
  ])
  
  // 支付方式选项
  const wayCodeOptions = ref([
    { label: '支付宝', value: 'alipay' },
    { label: '微信支付', value: 'wechat' },
    { label: 'Stripe', value: 'stripe' },
    { label: 'PayPal', value: 'paypal' },
    { label: 'PayMe', value: 'payme' },
    { label: 'FPS', value: 'fps' }
  ])
  
  // 表单验证规则
  const rules = reactive<FormRules>({
    if_code: [
      { required: true, message: t('Pay.PayPassage.form.validation.if_code'), trigger: 'change' }
    ],
    way_code: [
      { required: true, message: t('Pay.PayPassage.form.validation.way_code'), trigger: 'change' }
    ],
    rate: [
      { required: true, message: t('Pay.PayPassage.form.validation.rate'), trigger: 'blur' },
      { type: 'number', min: 0, max: 1, message: t('Pay.PayPassage.form.validation.rate_range'), trigger: 'blur' }
    ],
    state: [
      { required: true, message: t('Pay.PayPassage.form.validation.state'), trigger: 'change' }
    ]
  })
  
  // 初始化表单数据
  const initFormData = () => {
    if (props.passage) {
      Object.keys(form).forEach(key => {
        const k = key as keyof typeof form
        form[k] = props.passage?.[k]
      })
    }
  }
  
  // 监听prop变化
  watch(() => props.passage, () => {
    initFormData()
  }, { immediate: true })
  
  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return
    
    await formRef.value.validate(async (valid) => {
      if (valid) {
        emit('submit', form)
      }
    })
  }
  
  // 取消
  const cancel = () => {
    emit('cancel')
  }
  
  // 初始化
  onMounted(() => {
    initFormData()
  })
  </script>
  
  <style scoped>
  .passage-form {
    padding: 20px 0;
  }
  
  .form-tip {
    color: #909399;
    font-size: 12px;
    margin-top: 4px;
  }
  </style>