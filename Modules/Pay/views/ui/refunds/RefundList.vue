<!-- Modules/Pay/views/ui/refunds/RefundList.vue -->
<template>
    <div class="refund-list-container">
      <!-- 搜索过滤区域 -->
      <el-card class="filter-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>{{ $t('Pay.PayRefund.list.title') }}</span>
            <div class="actions">
              <el-button type="primary" @click="refreshData" :loading="refundStore.loading" size="small">
                <el-icon><Refresh /></el-icon> {{ $t('Pay.PayRefund.list.refresh') }}
              </el-button>
            </div>
          </div>
        </template>
        
        <el-form :model="searchForm" label-position="top" @submit.prevent>
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item :label="$t('Pay.PayRefund.form.keyword')">
                <el-input 
                  v-model="searchForm.keyword" 
                  :placeholder="$t('Pay.PayRefund.form.keyword_placeholder')" 
                  clearable
                  @keyup.enter="handleSearch"
                  class="filter-input"
                />
              </el-form-item>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item :label="$t('Pay.PayRefund.form.state')">
                <el-select 
                  v-model="searchForm.state" 
                  :placeholder="$t('Pay.PayRefund.form.placeholder.all')" 
                  clearable 
                  class="filter-input"
                >
                  <el-option :label="$t('Pay.PayRefund.state.0')" :value="0" />
                  <el-option :label="$t('Pay.PayRefund.state.1')" :value="1" />
                  <el-option :label="$t('Pay.PayRefund.state.2')" :value="2" />
                  <el-option :label="$t('Pay.PayRefund.state.3')" :value="3" />
                  <el-option :label="$t('Pay.PayRefund.state.4')" :value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item :label="$t('Pay.PayRefund.form.way_code')">
                <el-select 
                  v-model="searchForm.way_code" 
                  :placeholder="$t('Pay.PayRefund.form.placeholder.all')" 
                  clearable 
                  class="filter-input"
                >
                  <el-option v-for="item in wayCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <el-form-item :label="$t('Pay.PayRefund.form.apply_date')">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :range-separator="$t('Pay.PayRefund.form.date_range.separator')"
                  :start-placeholder="$t('Pay.PayRefund.form.date_range.start')"
                  :end-placeholder="$t('Pay.PayRefund.form.date_range.end')"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  class="date-range-picker"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row>
            <el-col :span="24">
              <el-form-item class="action-buttons">
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon> {{ $t('Pay.PayRefund.list.search') }}
                </el-button>
                <el-button @click="resetSearch">
                  <el-icon><Refresh /></el-icon> {{ $t('Pay.PayRefund.list.reset') }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      
      <!-- 订单表格区域 -->
      <el-card class="refund-table-card">
        <template #header>
          <div class="card-header">
            <span>{{ $t('Pay.PayRefund.list.data_title') }}</span>
          </div>
        </template>
        
        <el-table
          v-loading="refundStore.loading"
          :data="refundStore.refunds"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" :label="$t('Pay.PayRefund.table.id')" width="80" />
          <el-table-column prop="refund_order_id" :label="$t('Pay.PayRefund.table.refund_order_id')" min-width="180" show-overflow-tooltip />
          <el-table-column prop="pay_order_id" :label="$t('Pay.PayRefund.table.pay_order_id')" min-width="180" show-overflow-tooltip />
          <el-table-column prop="mch_refund_no" :label="$t('Pay.PayRefund.table.mch_refund_no')" min-width="180" show-overflow-tooltip />
          <el-table-column :label="$t('Pay.PayRefund.table.refund_amount')" width="120">
            <template #default="scope">
              {{ formatAmount(scope.row.refund_amount) }} {{ scope.row.currency }}
            </template>
          </el-table-column>
          <el-table-column prop="way_code" :label="$t('Pay.PayRefund.table.way_code')" width="120" />
          <el-table-column prop="if_code" :label="$t('Pay.PayRefund.table.if_code')" width="120" />
          <el-table-column :label="$t('Pay.PayRefund.table.state')" width="120">
            <template #default="scope">
              <el-tag 
                :type="getStatusType(scope.row.state)" 
                :effect="scope.row.state === 2 ? 'light' : 'plain'"
              >
                {{ getStatusLabel(scope.row.state) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="refund_reason" :label="$t('Pay.PayRefund.table.refund_reason')" min-width="150" show-overflow-tooltip />
          <el-table-column prop="created_at" :label="$t('Pay.PayRefund.table.created_at')" width="180" />
          <el-table-column :label="$t('Pay.PayRefund.table.actions')" width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="handleView(scope.row)">{{ $t('Pay.PayRefund.list.view_detail') }}</el-button>
              <el-popconfirm 
                v-if="scope.row.state === 0"
                :title="$t('Pay.PayRefund.list.delete_confirm')" 
                @confirm="handleDelete(scope.row.id)"
              >
                <template #reference>
                  <el-button type="danger" link>{{ $t('Pay.PayRefund.list.delete') }}</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页控件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 15, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="refundStore.totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
      
      <!-- 查看详情弹窗 -->
      <el-dialog
        v-model="detailVisible"
        :title="$t('Pay.PayRefund.detail.title')"
        width="700px"
      >
        <refund-detail :refund="currentRefund" />
      </el-dialog>
    </div>
  </template>
    
  <script setup lang="ts">
  import { ref, reactive, onMounted, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Refresh, Search } from '@element-plus/icons-vue'
  import { useRefundStore } from '../../stores/refundStore'
  import RefundDetail from './components/RefundDetail.vue'
  import type { PayRefund, RefundQueryParams } from '../../types/refund'
  import { useI18n } from 'vue-i18n'
  
  const { t } = useI18n()
  const refundStore = useRefundStore()
  
  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(15)
  
  // 搜索表单
  const searchForm = reactive<RefundQueryParams>({
    keyword: '',
    state: undefined,
    way_code: undefined,
    if_code: undefined,
    start_date: '',
    end_date: ''
  })
  
  // 日期范围
  const dateRange = ref<[string, string] | null>(null)
  
  // 日期快捷选项
  const dateShortcuts = [
    {
      text: t('Pay.PayRefund.form.date_range.last_week'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      },
    },
    {
      text: t('Pay.PayRefund.form.date_range.last_month'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      },
    },
    {
      text: t('Pay.PayRefund.form.date_range.last_three_months'),
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      },
    },
  ]
  
  // 支付方式选项
  const wayCodeOptions = ref([
    { label: t('Pay.PayRefund.payment_methods.alipay'), value: 'alipay' },
    { label: t('Pay.PayRefund.payment_methods.wechat'), value: 'wechat' },
    { label: t('Pay.PayRefund.payment_methods.stripe'), value: 'stripe' },
    { label: t('Pay.PayRefund.payment_methods.paypal'), value: 'paypal' },
    { label: t('Pay.PayRefund.payment_methods.payme'), value: 'payme' },
    { label: t('Pay.PayRefund.payment_methods.fps'), value: 'fps' }
  ])
  
  // 弹窗控制
  const detailVisible = ref(false)
  const currentRefund = ref<PayRefund | null>(null)
  
  // 监听日期变化
  watch(dateRange, (newValue) => {
    if (newValue) {
      searchForm.start_date = newValue[0]
      searchForm.end_date = newValue[1]
    } else {
      searchForm.start_date = ''
      searchForm.end_date = ''
    }
  })
  
  // 初始化
  onMounted(() => {
    loadData()
  })
  
  // 加载列表数据
  const loadData = () => {
    refundStore.page = currentPage.value
    refundStore.limit = pageSize.value
    
    refundStore.fetchRefunds(searchForm)
  }
  
  // 刷新数据
  const refreshData = () => {
    loadData()
    ElMessage.success(t('Pay.PayRefund.list.refresh_success'))
  }
  
  // 搜索
  const handleSearch = () => {
    currentPage.value = 1
    loadData()
  }
  
  // 重置搜索
  const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
      // @ts-ignore - 类型错误可以使用ts-ignore跳过
      searchForm[key] = undefined
    })
    dateRange.value = null
    handleSearch()
  }
  
  // 分页大小变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    loadData()
  }
  
  // 页码变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    loadData()
  }
  
  // 查看按钮点击
  const handleView = (row: PayRefund) => {
    currentRefund.value = { ...row }
    detailVisible.value = true
  }
  
  // 删除按钮点击
  const handleDelete = async (id: number) => {
    try {
      const result = await refundStore.deleteRefund(id)
      if (result.code === 0 || result.code === 200) {
        ElMessage.success(t('Pay.PayRefund.list.delete_success'))
        loadData()
      } else {
        ElMessage.error(result.message || t('Pay.PayRefund.list.delete_fail'))
      }
    } catch (error) {
      ElMessage.error(t('Pay.PayRefund.list.delete_fail'))
    }
  }
  
  // 获取状态标签
  const getStatusLabel = (state: number): string => {
    return t(`Pay.PayRefund.state.${state}`) || t('Pay.PayRefund.state.unknown')
  }
  
  // 获取状态标签类型
  const getStatusType = (state: number): string => {
    switch (state) {
      case 0: return 'info'
      case 1: return 'warning'
      case 2: return 'success'
      case 3: return 'danger'
      case 4: return 'info'
      default: return 'info'
    }
  }
  
  // 格式化金额（转换分为元）
  const formatAmount = (amount: number): string => {
    if (amount === undefined || amount === null) return '0.00'
    return (amount / 100).toFixed(2)
  }
  </script>
    
  <style scoped>
  .refund-list-container {
    padding: 20px;
  }
  
  .filter-card {
    margin-bottom: 20px;
    border-radius: 8px;
  }
  
  .refund-table-card {
    margin-top: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  /* 设置表单项标签样式 */
  :deep(.el-form-item__label) {
    padding-bottom: 4px;
    font-weight: 500;
    color: #606266;
    font-size: 14px;
    line-height: 1.4;
  }
  
  /* 统一所有输入类组件样式 */
  .filter-input {
    width: 100%;
    height: 36px;
  }
  
  /* 统一日期选择器样式 */
  .date-range-picker {
    width: 100%;
    height: 36px;
  }
  
  /* 确保所有输入框内部样式一致 */
  :deep(.el-input__wrapper),
  :deep(.el-select .el-input__wrapper),
  :deep(.el-date-editor .el-input__wrapper) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    height: 36px;
    line-height: 36px;
    padding: 0 12px;
    transition: box-shadow 0.2s;
    background-color: #fff;
  }
  
  :deep(.el-input__wrapper:hover),
  :deep(.el-select .el-input__wrapper:hover),
  :deep(.el-date-editor .el-input__wrapper:hover) {
    box-shadow: 0 0 0 1px #c0c4cc inset;
  }
  
  :deep(.el-input__wrapper.is-focus),
  :deep(.el-select .el-input__wrapper.is-focus),
  :deep(.el-date-editor .el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
  }
  
  /* 修复select下拉框高度问题 */
  :deep(.el-select .el-select__wrapper) {
    line-height: 36px !important;
    min-height: 36px !important;
  }
  
  /* 按钮样式优化 */
  .action-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
  
  .action-buttons .el-button {
    margin-left: 10px;
    height: 36px;
    padding: 8px 16px;
    border-radius: 4px;
  }
  
  .action-buttons .el-button .el-icon {
    margin-right: 4px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  /* 响应式调整 */
  @media (max-width: 1400px) {
    .action-buttons {
      justify-content: flex-start;
    }
    
    .action-buttons .el-button:first-child {
      margin-left: 0;
    }
  }
  
  @media (max-width: 768px) {
    .action-buttons {
      flex-direction: column;
    }
    
    .date-range-picker {
      width: 100%;
    }
  }
  </style>