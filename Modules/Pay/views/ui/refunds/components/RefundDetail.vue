<!-- Modules/Pay/views/ui/refunds/components/RefundDetail.vue -->
<template>
    <div class="refund-detail" v-if="refund">
      <el-descriptions :title="$t('Pay.PayRefund.detail.base_info')" :column="2" border>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.refund_order_id')" span="2">{{ refund.refund_order_id }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.pay_order_id')">{{ refund.pay_order_id }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.mch_refund_no')">{{ refund.mch_refund_no }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.channel_pay_order_no')" v-if="refund.channel_pay_order_no">
          {{ refund.channel_pay_order_no }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.channel_order_no')" v-if="refund.channel_order_no">
          {{ refund.channel_order_no }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.if_code')">{{ refund.if_code }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.way_code')">{{ refund.way_code }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.pay_amount')">{{ formatAmount(refund.pay_amount) }} {{ refund.currency }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.refund_amount')">{{ formatAmount(refund.refund_amount) }} {{ refund.currency }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.state')">
          <el-tag :type="getStatusType(refund.state)">{{ getStatusLabel(refund.state) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.client_ip')">{{ refund.client_ip || $t('Pay.PayRefund.detail.unknown') }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.refund_reason')" span="2">{{ refund.refund_reason || $t('Pay.PayRefund.detail.none') }}</el-descriptions-item>
        <!-- <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.success_time')" v-if="refund.success_time">
          {{ formatTime(refund.success_time) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.expired_time')" v-if="refund.expired_time">
          {{ formatTime(refund.expired_time) }}
        </el-descriptions-item> -->
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.notify_url')" v-if="refund.notify_url" span="2">
          {{ refund.notify_url }}
        </el-descriptions-item>
      </el-descriptions>
  
      <el-descriptions :title="$t('Pay.PayRefund.detail.error_info')" :column="1" border v-if="refund.err_code || refund.err_msg" class="mt-4">
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.err_code')" v-if="refund.err_code">{{ refund.err_code }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.err_msg')" v-if="refund.err_msg">{{ refund.err_msg }}</el-descriptions-item>
      </el-descriptions>
  
      <div class="channel-extra mt-4" v-if="refund.channel_extra">
        <h3>{{ $t('Pay.PayRefund.detail.channel_extra') }}</h3>
        <pre class="json-content">{{ formatJson(refund.channel_extra) }}</pre>
      </div>
  
      <el-descriptions :title="$t('Pay.PayRefund.detail.system_info')" :column="2" border class="mt-4">
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.created_at')">{{ refund.created_at || $t('Pay.PayRefund.detail.unknown') }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.updated_at')">{{ refund.updated_at || $t('Pay.PayRefund.detail.unknown') }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.creator_id')" v-if="refund.creator_id">{{ refund.creator_id }}</el-descriptions-item>
        <el-descriptions-item :label="$t('Pay.PayRefund.detail.label.ext_param')" v-if="refund.ext_param">{{ refund.ext_param }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </template>
  
  <script setup lang="ts">
  import { useI18n } from 'vue-i18n'
  import type { PayRefund } from '../../../../types/refund'
  
  const { t } = useI18n()
  
  const props = defineProps<{
    refund: PayRefund | null
  }>()
  
  // 获取状态标签
  const getStatusLabel = (state: number): string => {
    return t(`Pay.PayRefund.state.${state}`) || t('Pay.PayRefund.state.unknown')
  }
  
  // 获取状态标签类型
  const getStatusType = (state: number): string => {
    switch (state) {
      case 0: return 'info'
      case 1: return 'warning'
      case 2: return 'success'
      case 3: return 'danger'
      case 4: return 'info'
      default: return 'info'
    }
  }
  
  // 格式化JSON
  const formatJson = (json: any): string => {
    if (typeof json === 'string') {
      try {
        return JSON.stringify(JSON.parse(json), null, 2)
      } catch (e) {
        return json
      }
    }
    try {
      return JSON.stringify(json, null, 2)
    } catch (e) {
      return '{}'
    }
  }
  
  // 格式化金额（转换分为元）
  const formatAmount = (amount: number): string => {
    if (amount === undefined || amount === null) return '0.00'
    return (amount / 100).toFixed(2)
  }
  
  // 格式化时间戳
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return t('Pay.PayRefund.detail.unknown')
    const date = new Date(timestamp * 1000)
    return date.toLocaleString()
  }
  </script>
  
  <style scoped>
  .refund-detail {
    padding: 10px;
  }
  
  .channel-extra {
    margin-top: 20px;
  }
  
  .json-content {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: monospace;
    max-height: 300px;
    overflow-y: auto;
  }
  
  h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #606266;
  }
  
  .mt-4 {
    margin-top: 16px;
  }
  </style>

<script lang="ts">
export default {
  name: 'RefundDetail'
}
</script>