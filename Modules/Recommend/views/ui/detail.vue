<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <el-button @click="cancelHandle" class="button-no-border back-btn">
        <el-icon>
          <Back />
        </el-icon>
        <span>{{ $t('Recommend.deatil.box_btn2') }}</span>
      </el-button>
    </div>
    <div class="scroll-bar-custom-transparent">
      <div class="module-con">
        <div class="box">
          <el-form :model="recommendInfo" label-width="auto" label-position="top" v-loading="loading" :rules="rules" ref="recommendForm" :hide-required-asterisk="false">
            <!-- 名称和编码一行两列 -->
            <div class="form-row">
              <el-form-item :label="t('Recommend.deatil.label1')" prop="name" class="form-item">
                <el-input v-model="recommendInfo.name" :placeholder="t('Recommend.list.input_tips')+t('Recommend.deatil.label1')" />
              </el-form-item>
              <el-form-item :label="t('Recommend.deatil.label2')" prop="code" class="form-item">
                <el-input v-model="recommendInfo.code" :placeholder="t('Recommend.list.input_tips')+t('Recommend.deatil.label2')" />
              </el-form-item>
            </div>
            
            <!-- 位置和规则类型一行两列 -->
            <div class="form-row">
              <el-form-item :label="t('Recommend.deatil.label4')" prop="placement" class="form-item">
                <el-input v-model="recommendInfo.placement" :placeholder="t('Recommend.list.input_tips')+t('Recommend.deatil.label4')" />
              </el-form-item>
              
              <el-form-item :label="t('Recommend.deatil.label3')" prop="algorithm_type" class="form-item">
                <el-radio-group v-model="recommendInfo.algorithm_type" @change="algorithmTypeChange" class="simple-radio-group">
                  <el-radio :value="type.val" v-for="(type, index) in algorithmTypeList" :key="index">{{ type.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            
            <!-- 规则模块 -->
            <div class="rule-section">
              <div class="rule-header">
                <div class="flex justify-between align-items-center">
                  <div class="rule-title">{{ $t('Recommend.deatil.label5') }}</div>
                  <!-- 没有内容时，新增按钮放在右边 -->
                  <el-button class="el-button-default" v-if="recommendInfo.contents.length < 5" @click="addRecommendContent">
                    <el-icon size="16"><Plus /></el-icon>
                    <span>{{ $t('Recommend.deatil.label5_btn') }}</span>
                  </el-button>
                </div>
              </div>

              <div class="rule-content">
                <template v-if="recommendInfo.contents">
                  <div class="rule-item" v-for="(recommendContent, index) in recommendInfo.contents" :key="recommendContent.id">
                    <!-- 规则标题和删除按钮 -->
                    <div class="rule-item-header">
                      <div class="rule-item-number">{{ index + 1 }}</div>
                      <el-icon size="16" @click="delContentHandle(index)"><Delete /></el-icon>
                    </div>
                    
                    <div class="rule-item-body">
                      <!-- 排序和内容类型一行两列 -->
                      <div class="form-row">
                        <el-form-item :label="t('Recommend.deatil.label6')" :prop="`contents[${index}].sort`" :rules="rules.contents.sort" class="form-item">
                          <el-input-number v-model="recommendContent.sort" :min="1" :max="5" controls-position="right" @change="sortHandle" style="width: 100%;" />
                        </el-form-item>
                        
                        <el-form-item :label="t('Recommend.deatil.label8')" class="form-item" :prop="`contents[${index}].content_type`" :rules="rules.contents.content_type">
                          <el-select v-model="recommendContent.content_type" :placeholder="t('Recommend.deatil.label8_tips')" >
                            <el-option v-for="item in contentTypeList" :key="item.id" :label="item.name" :value="item.name" />
                          </el-select>
                        </el-form-item>
                      </div>
                      
                      <!-- 混合模式的计数类型和分类ID一行两列 -->
                      <div class="form-row" v-if="recommendInfo.algorithm_type === '4'">
                        <el-form-item :label="t('Recommend.deatil.label7')" class="form-item" :prop="`contents[${index}].count_type`" :rules="rules.contents.count_type">
                          <el-select v-model="recommendContent.count_type" :placeholder="t('Recommend.deatil.label7_tips')">
                            <el-option :label="t('Recommend.deatil.label7_option1')" :value="1" />
                            <el-option :label="t('Recommend.deatil.label7_option2')" :value="2" />
                          </el-select>
                        </el-form-item>

                        <el-form-item :label="t('Recommend.deatil.label9')" class="form-item" :prop="`contents[${index}].category_id`" :rules="rules.contents.category_id">
                          <el-select v-model="recommendContent.category_id" :placeholder="t('Recommend.deatil.label9_tips')" @change="categoryChange(recommendContent)">
                            <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
                          </el-select>
                        </el-form-item>
                      </div>
                      
                      <!-- 非混合模式的分类ID单行 -->
                      <div class="form-row" v-if="recommendInfo.algorithm_type !== '4'">
                        <el-form-item :label="t('Recommend.deatil.label9')" class="form-item-full" :prop="`contents[${index}].category_id`" :rules="rules.contents.category_id">
                          <el-select v-model="recommendContent.category_id" :placeholder="t('Recommend.deatil.label9_tips')" @change="categoryChange(recommendContent)">
                            <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
                          </el-select>
                        </el-form-item>
                      </div>
                      
                      <!-- 固定 -->
                      <div class="form-row" v-if="categoryList.length && (recommendInfo.algorithm_type === '1' || recommendContent.count_type === 1)">
                        <el-form-item
                          :label="t('Recommend.deatil.label10')"
                          :prop="`contents[${index}].content_id`"
                          :rules="rules.contents.content_id"
                          class="form-item-full"
                        >
                          <el-select v-model="recommendContent.content_id" :placeholder="t('Recommend.list.select_tips')">
                            <el-option v-for="item in recommendContent.content_list" :key="item.id" :label="item.title" :value="item.id" />
                          </el-select>
                        </el-form-item>
                      </div>
                      
                      <!-- 随机 + 个性 -->
                      <div class="form-row" v-if="categoryList.length && (recommendInfo.algorithm_type === '2' || recommendContent.count_type === 2 || recommendInfo.algorithm_type === '3')">
                        <el-form-item
                          :label="t('Recommend.deatil.label11')"
                          class="form-item-full"
                        >
                          <el-select v-model="recommendContent.exclude" :placeholder="t('Recommend.list.select_tips')+t('Recommend.deatil.label11')" multiple>
                            <el-option v-for="item in excludeList" :key="item.id" :label="item.code" :value="item.id" />
                            <template #empty>
                              <div style="text-align: center; padding: 8px 0;">
                                {{ t('Cms.list.no_data') }}
                              </div>
                            </template>
                          </el-select>
                        </el-form-item>
                      </div>
                      
                      <!-- 随机 + 个性的内容数量和随机方法一行两列 -->
                      <div class="form-row" v-if="recommendInfo.algorithm_type === '2' || recommendInfo.algorithm_type === '3' || recommendContent.count_type === 2">
                        <el-form-item
                          :label="t('Recommend.deatil.label12')"
                          :prop="`contents[${index}].content_num`"
                          :rules="rules.contents.content_num"
                          class="form-item"
                          v-if="recommendInfo.algorithm_type === '2' || recommendInfo.algorithm_type === '3'"
                        >
                          <el-input-number v-model="recommendContent.content_num" :min="1" :max="5" controls-position="right" size="default" />
                        </el-form-item>
                        
                        <el-form-item
                          :label="t('Recommend.deatil.label13')"
                          :prop="`contents[${index}].random_method`"
                          :rules="rules.contents.random_method"
                          class="form-item"
                          v-if="recommendInfo.algorithm_type === '2' || recommendContent.count_type === 2 || recommendInfo.algorithm_type === '3'"
                        >
                          <el-radio-group v-model="recommendContent.random_method" size="default">
                            <el-radio :value="type.val" v-for="(type, index) in randonMethods" :key="index">{{ type.label }}</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </template>
                
              </div>
            </div>
          </el-form>
        </div>
      </div>
      
      <!-- 提交按钮模块放在容器外面的底部 -->
      <div class="flex justify-center">
        <el-button @click="cancelHandle" class="el-button-default button-cancel">
          <span>{{ $t('Recommend.deatil.box_btn2') }}</span>
        </el-button>
        <el-button type="primary" @click="submitHandle" class="button-no-border">{{ boxBtn }}</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance } from 'element-plus'
import { Plus, Delete, Back } from '@element-plus/icons-vue'

import { Recommend } from '../domain/Recommend'
import { RecommendContent } from '../domain/RecommendContent'
import { ContentType } from '../domain/ContentType'
import { Category } from '../domain/Category'
import { Content } from '../domain/Content'
import { Excludes } from '../domain/Excludes'
import RecommendService from '../application/RecommendService'
import RecommendRepositoryImpl from '../infrastructure/RecommendRepositoryImpl'
import ContentTypeService from '../application/ContentTypeService'
import ContentTypeRepositoryImpl from '../infrastructure/ContentTypeRepositoryImpl'
import CategoryService from '../application/CategoryService'
import CategoryRepositoryImpl from '../infrastructure/CategoryRepositoryImpl'
import ContentService from '../application/ContentService'
import ContentRepositoryImpl from '../infrastructure/ContentRepositoryImpl'
import ExcludesService from '../application/ExcludesService'
import ExcludesRepositoryImpl from '../infrastructure/ExcludesRepositoryImpl'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const api = 'recommend'
const route = useRoute()
const router = useRouter()
const id = route.query.id
const boxBtn = ref(t('Recommend.deatil.box_btn3'))
const lang = localStorage.getItem('bwms_language')

const recommendForm = ref<FormInstance>()
const rules = computed(() => ({
  name: [{ required: true, message: t('Recommend.deatil.label1_tips'), trigger: 'blur' }],
  code: [{ required: true, message: t('Recommend.deatil.label2_tips'), trigger: 'blur' }],
  placement: [{ required: true, message: t('Recommend.deatil.label3_tips'), trigger: 'blur' }],
  algorithm_type: [{ required: true, message: t('Recommend.deatil.label4_tips'), trigger: 'change' }],
  contents: {
    sort: [{ required: true, message: t('Recommend.deatil.label6_tips'), trigger: 'change' }],
    count_type: [{ required: true, message: t('Recommend.deatil.label7_tips'), trigger: 'change' }],
    content_type: [{ required: true, message: t('Recommend.deatil.label8_tips'), trigger: 'change' }],
    category_id: [{ required: true, message: t('Recommend.deatil.label9_tips'), trigger: 'change' }],
    content_id: [{ required: true, message: t('Recommend.deatil.label10_tips'), trigger: 'change' }],
    content_num: [{ required: true, message: t('Recommend.deatil.label12_tips'), trigger: 'change' }],
    random_method: [{ required: true, message: t('Recommend.deatil.label13_tips'), trigger: 'change' }],
  },
}))

// 推荐列表
const RecommendInstance = new RecommendService(new RecommendRepositoryImpl(api))
const loading = ref(true)
const recommendInfo = ref<Recommend>({
  id: 0,
  name: '',
  code: '',
  placement: '',
  algorithm_type: '1',
  creator: '',
  lang: '',
  creator_id: 0,
  created_at: '',
  updated_at: '',
  contents: [],
})
const algorithmTypeList = reactive([
  {
    val: '1',
    label: t('Recommend.deatil.label3_radio1'),
  },
  {
    val: '2',
    label: t('Recommend.deatil.label3_radio2'),
  },
  {
    val: '3',
    label: t('Recommend.deatil.label3_radio3'),
  },
  {
    val: '4',
    label: t('Recommend.deatil.label3_radio4'),
  },
])
const algorithmTypeChange = (val: string) => {
  if (val === '1' || val === '2') recommendInfo.value.contents.forEach(item => (item.count_type = Number(val)))
}
function getInfo() {
  if (!id) return
  loading.value = true

  RecommendInstance.getById(Number(id)).then(res => {
    if (res) {
      res.contents = res.contents.sort((a, b) => a.sort - b.sort)
      recommendInfo.value = res
    }
    loading.value = false
  })
}

// 推荐内容
function infoHandle() {
  recommendInfo.value.contents.forEach(content => {
    categoryHandle(content.category_id).then(list => {
      content.content_list = list
    })
  })
}
const addRecommendContent = async () => {
  const { id, contents } = recommendInfo.value
  const category_id = categoryList.value[0].id
  let content_list: Content[] = []
  await categoryHandle(category_id).then(list => {
    content_list = list
  })

  const count_type = Number(recommendInfo.value.algorithm_type === '1' || recommendInfo.value.algorithm_type === '2' ? recommendInfo.value.algorithm_type : '')
  recommendInfo.value.contents.push({
    id: 0,
    setting_id: id,
    count_type,
    sort: contents.length + 1,
    category_id,
    content_type: '',
    content_id: undefined,
    random_method: '',
    exclude: [],
    lang: Number(lang) || 1,
    creator_id: 0,
    created_at: '',
    updated_at: '',
    content_list,
    content_num: 1,
  })
}
const sortHandle = () => {
  recommendInfo.value.contents = recommendInfo.value.contents.sort((a, b) => a.sort - b.sort)
}
const delContentHandle = (index: number) => {
  recommendInfo.value.contents.splice(index, 1)
}

// 算法
const randonMethods = reactive([
  {
    val: 1,
    label: t('Recommend.deatil.label13_radio1'),
  },
  {
    val: 2,
    label: t('Recommend.deatil.label13_radio2'),
  },
  {
    val: 3,
    label: t('Recommend.deatil.label13_radio3'),
  },
  {
    val: 4,
    label: t('Recommend.deatil.label13_radio4'),
  },
  {
    val: 5,
    label: t('Recommend.deatil.label13_radio5'),
  },
  {
    val: 6,
    label: t('Recommend.deatil.label13_radio6'),
  },
])

// 内容类型
const ContentTypeInstance = new ContentTypeService(new ContentTypeRepositoryImpl(api))
const contentTypeList = ref<ContentType[]>([])
function getContentTypeList() {
  ContentTypeInstance.get().then(res => {
    contentTypeList.value = res
  })
}

// 栏目
const CategoryInstance = new CategoryService(new CategoryRepositoryImpl('cms'))
const categoryList = ref<Category[]>([])
function getCategoryList() {
  CategoryInstance.get().then(res => {
    categoryList.value = res
    infoHandle()
  })
}
async function categoryHandle(id: number): Promise<Content[]> {
  let list: Content[] = []
  const checkedCategory = categoryList.value.find(item => item.id === id)
  if (checkedCategory) {
    const { model_id } = checkedCategory
    await getContentList(model_id).then(res => {
      list = res
    })
  }

  return list
}
const categoryChange = (recommendContent: RecommendContent) => {
  categoryHandle(recommendContent.category_id).then(list => {
    recommendContent.content_list = list
    recommendContent.content_id = undefined
  })
}

// 内容
const ContentInstance = new ContentService(new ContentRepositoryImpl('cms'))
async function getContentList(model_id: number): Promise<Content[]> {
  let contentList: Content[] = []
  await ContentInstance.get(model_id).then(res => {
    contentList = res
  })

  return contentList
}

// 提交 | 取消
const submitHandle = () => {
  if (!recommendForm.value) return
  recommendForm.value.validate((valid, message) => {
    if (valid) {
      loading.value = true
      if (id) {
        RecommendInstance.update(recommendInfo.value)
          .then(() => {
            router.push('recommendList')
          })
          .finally(() => {
            loading.value = false
          })
      } else {
        RecommendInstance.create(recommendInfo.value)
          .then(() => {
            router.push('recommendList')
          })
          .finally(() => {
            loading.value = false
          })
      }
    } else {
      console.error(message)
    }
  })
}
const cancelHandle = () => {
  router.push('recommendList')
}

// 排除
const ExcludesInstance = new ExcludesService(new ExcludesRepositoryImpl(api))
const excludeList = ref<Excludes[]>([])
function getExcludeList() {
  ExcludesInstance.get(id ? Number(id) : undefined).then(res => {
    excludeList.value = res
  })
}

onMounted(async () => {
  if (id) {
    boxBtn.value = t('Recommend.deatil.box_btn4')
    await getInfo()
  } else {
    loading.value = false
    boxBtn.value = t('Recommend.deatil.box_btn3')
  }

  getContentTypeList()
  getCategoryList()
  getExcludeList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-header {
    display: flex;
    justify-content: end;
    margin-bottom: 16px;
    gap: 10px;
  }

  .module-con {
    background: #fff;
    border-radius: 10px;
    padding-right: 8px;
    padding-top: 8px;
    margin-bottom: 16px;
    .box {
      padding-top: 12px;
      padding-right: 12px;
    }
  }
}

/* 表单行布局样式 */
.form-row {
  display: flex;
  gap: 26px;
  
  .form-item {
    flex: 1;
    min-width: 0;
  }
  
  .form-item-full {
    flex: 1;
    width: 100%;
  }
}


.rule-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 40px;
}

.rule-header {
  margin-bottom: 16px;
}

.rule-title {
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.rule-content {
  margin-top: 10px;
}

.rule-item {
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
  border-radius: 3px;
  background-color: #ffffff;
  transition: box-shadow 0.2s;
  
  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }
}

.rule-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.rule-item-number {
  font-size: 13px;
  font-weight: 500;
  color: #606266;
  
  &:before {
    content: '#';
    margin-right: 3px;
    color: #909399;
  }
}

.rule-item-body {
  padding: 15px;
}



.el-form {
  width: 98%;
}
</style>
