<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <FilterPopover 
        v-model="filterDialog"
      >
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="filterDialog = true">
            <el-icon>
              <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
            </el-icon>
            <span>{{ $t('Recommend.list.filter') }}</span>
            </el-button>
        </template>
        <el-form :model="search" label-position="top">
          <el-form-item label="ID">
            <el-input v-model="search.id" :placeholder="t('Recommend.list.input_tips')" size="large" />
          </el-form-item>
          <el-form-item :label="$t('Recommend.list.table_th1')">
            <el-input v-model="search.name" :placeholder="t('Recommend.list.input_tips') + t('Recommend.list.table_th1')" size="large" />
          </el-form-item>
          <el-form-item :label="t('Recommend.list.lang')">
            <el-select v-model="lang" size="large" :placeholder="t('Recommend.list.select_tips') + t('Recommend.list.lang')">
              <el-option :label="l.text" :value="l.code" v-for="(l, i) in langList" :key="i" />
            </el-select>
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="refreshContent">
              <el-icon size="16">
                <Refresh />
              </el-icon>
              <span>{{ $t('Recommend.list.search_btn2') }}</span>
            </el-button>
            <el-button class="button-no-border" @click="searchContent" type="primary">
              <el-icon size="16">
                <Filter />
              </el-icon>
              <span>{{ $t('Recommend.list.search_btn1') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>
      <el-button class="button-no-border" @click="createHandle" type="primary">
        <el-icon>
          <img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" />
        </el-icon>
        <span>{{ $t('Recommend.list.table_btn1') }}</span>
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane name="list">
            <template #label>
              <span class="tabs-tit">{{ t('Recommend.list.table_tit') }}</span>
              <span class="tabs-num">{{ total }}</span>
            </template>
            <el-table :data="list" style="width: 100%; height: 100%" @selection-change="handleSelectionChange" v-loading="listLoading" @row-click="clickHandle">
              <template #empty>
                  <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template>
              <el-table-column type="selection" width="55" />
              <el-table-column prop="id" label="ID" width="90" />
              <el-table-column prop="name" :label="t('Recommend.list.table_th1')" />
              <el-table-column prop="placement" :label="t('Recommend.list.table_th2')" width="200" />
              <el-table-column prop="code" :label="t('Recommend.list.table_th3')" width="200" />
              <el-table-column prop="creator_id" :label="t('Recommend.list.table_th4')" width="100" />
              <el-table-column prop="created_at" :label="t('Recommend.list.table_th5')" width="180" />
              <el-table-column prop="updated_at" :label="t('Recommend.list.table_th6')" width="180" />
              <el-table-column fixed="right" :label="$t('Recommend.list.operate')" width="150">
                <template #default="scope">
                  <div class="bwms-operate-btn-box">
                    <el-button class="bwms-operate-btn" @click.stop="editHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                      </el-icon>
                    </el-button>
                    <el-button class="bwms-operate-btn" @click.stop="singleDelete(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                      </el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 分页器 -->
      <div class="box-footer">
        <div class="table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="limit"
              class="page-size-select"
              @change="changePage"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0; font-size: 12px;">
                  {{ t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="page"
              background
              layout="prev, pager, next"
              :page-size="limit"
              :total="total"
              @current-change="changePage"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

import { Recommend } from '../domain/Recommend'
import RecommendService from '../application/RecommendService'
import RecommendRepositoryImpl from '../infrastructure/RecommendRepositoryImpl'
import FilterPopover from '/resources/admin/components/popover/index.vue'

import http from '/admin/support/http'
import { useI18n } from 'vue-i18n'
import { Filter, Download, Refresh } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'


const { t } = useI18n()

const api = 'recommend'
const router = useRouter()
const route = useRoute()

// 多语言
const lang = ref(localStorage.getItem('bwms_language') || 'zh_HK')
const langList = ref([
  {
    text: t('Recommend.list.lang_cn'),
    code: 'zh_CN',
  },
  {
    text: t('Recommend.list.lang_en'),
    code: 'en',
  },
  {
    text: t('Recommend.list.lang_hk'),
    code: 'zh_HK',
  },
])
const changeLang = (_lang: string) => {
  lang.value = _lang
  localStorage.setItem('bwms_language', _lang)
  http.get(`users/setLang?lang=${_lang}`).then(() => {
    location.reload()
  })
}

// 标签页
const activeName = ref('list')

// 搜索
const filterDialog = ref(false)
const search = reactive({
  id: '',
  name: '',
})
const searchContent = () => {
  getList()
  filterDialog.value = false
}
const refreshContent = () => {
  search.id = ''
  search.name = ''
  getList()
  filterDialog.value = false
}

// 分页
let page = ref(1)
let limit = ref(15)
let total = ref(0)
const changePage = () => {
  getList()
}

// 删除处理
const checkedList = ref<Recommend[]>([])
// 单条删除
const singleDelete = (item: Recommend) => {
  ElMessageBox.confirm(
    t('Recommend.list.delete_message'),
    t('Recommend.list.delete_confirm'),
    {
      confirmButtonText: t('Recommend.list.confirm'),
      cancelButtonText: t('Recommend.list.cancel'),
      type: 'warning',
    }
  ).then(() => {
    listLoading.value = true
    RecommendInstance.delete([item.id]).then(() => {
      ElMessage.success(t('Recommend.list.delete_success'))
      getList()
    }).catch(() => {
      ElMessage.error(t('Recommend.list.delete_error'))
    }).finally(() => {
      listLoading.value = false
    })
  }).catch(() => {
    // 用户取消删除，不执行任何操作
  })
}

// 内容列表
const RecommendInstance = new RecommendService(new RecommendRepositoryImpl(api))
const list = ref<Recommend[]>([])
const listLoading = ref(true)
const getList = () => {
  listLoading.value = true
  const params = {
    page: page.value,
    limit: limit.value,
    ...search
  }
  
  RecommendInstance.get(params)
    .then((res: any) => {
      if (res.items && res.total) {
        list.value = res.items
        total.value = res.total
      } else {
        list.value = res as Recommend[]
        total.value = res.length
      }
    })
    .catch((error) => {
      ElMessage.error(t('Recommend.list.fetch_error'))
      list.value = []
      total.value = 0
    })
    .finally(() => {
      listLoading.value = false
    })
}

const createHandle = () => {
  router.push({ name: 'recommendCreate' })
}
const editHandle = (item: Recommend) => {
  router.push({ name: 'recommendDetail', query: { id: item.id } })
}
const handleSelectionChange = (val: Recommend[]) => {
  checkedList.value = val
}

const clickHandle = (val: Recommend) => {
  router.push({ name: 'recommendDetail', query: { id: val.id } })
}

watch(() => route, (to, from) => {
  if(from && from.path) {
    getList()
  }
}, { immediate: true })

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 0;
    }
  }
}

.tabs-tit {
  margin-right: 5px;
}

.tabs-num {
  display: inline-block;
  background-color: #f2f3f5;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 11px;
  font-size: 12px;
  color: #909399;
}


</style>
