<?php

namespace Modules\SEO\Domain\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Bingo\Exceptions\BizException;
use Modules\SEO\Enums\SEOErrorCode;
use Modules\Ai\Services\LLM\LLMService;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;

class AIService
{
    protected string $apiKey;
    protected string $baseUri;
    protected int $timeout = 180; // 增加默认超时时间到180秒
    protected int $retries = 2; // 添加重试次数

    public function __construct()
    {
        $this->apiKey = config('services.yunwu.api_key');
        // 从配置中获取超时设置，如果没有则使用默认值
        $this->timeout = config('services.yunwu.timeout', $this->timeout);
        $this->baseUri = config('services.yunwu.base_uri', 'https://yunwu.ai');
    }

    public function chatCompletion(array $messages, array $params = []): array
    {
        try {
            $defaultParams = [
                // 'model' => 'claude-3-7-sonnet-20250219',
                // 'model' => 'claude-3-haiku-20240307',
                'model' => 'claude-sonnet-4-20250514',
                'temperature' => 0.7,
                'stream' => false,
                'max_tokens' => 3000
            ];
            
            $payload = array_merge($defaultParams, $params, ['messages' => $messages]);
            // 创建HTTP客户端并设置重试逻辑
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ])
                ->timeout($this->timeout)
                ->withoutVerifying()
                ->post($this->baseUri . '/v1/chat/completions', $payload);

            app('log')->info('AIService 请求参数', [
                'payload' => $payload,
                'response' => $response->body(),
                'code' => $response->status()
            ]);

            // 记录响应信息
            if ($response->successful()) {
                return $response->json();
            }

            // 根据状态码提供更具体的错误信息
            $errorMessage = match ($response->status()) {
                408 => 'AI服务请求超时，请稍后重试',
                429 => '上游负载已饱和，请稍后再试',
                500, 502, 503, 504 => 'AI服务暂时不可用，请稍后重试',
                default => 'AI服务请求失败: ' . $response->body()
            };
            BizException::throws(SEOErrorCode::AI_SERVICE_FAILED, $errorMessage);
        } catch (ConnectionException $e) {
            // 处理连接异常
            app('log')->error('AIService 连接异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            BizException::throws(SEOErrorCode::AI_SERVICE_FAILED, '无法连接到AI服务，请检查网络连接或稍后重试');
        } catch (RequestException $e) {
            // 处理请求异常
            app('log')->error('AIService 请求异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            BizException::throws(SEOErrorCode::AI_SERVICE_FAILED, '请求AI服务时发生错误: ' . $e->getMessage());
        } catch (\Exception $e) {
            // 处理其他异常
            app('log')->error('AIService 未知异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            BizException::throws(SEOErrorCode::AI_SERVICE_FAILED, '处理AI请求时发生未知错误: ' . $e->getMessage());
        }
    }
}
