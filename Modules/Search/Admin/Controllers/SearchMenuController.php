<?php

namespace Modules\Search\Admin\Controllers;

use Bingo\Base\BingoController as Controller;
use Illuminate\Http\Request;
use Modules\Search\Services\SearchMenuService;

class SearchMenuController extends Controller
{
    protected SearchMenuService $searchMenuService;

    public function __construct(SearchMenuService $searchMenuService)
    {
        $this->searchMenuService = $searchMenuService;
    }

    /**
     * 获取最近访问的页面
     * @return array
     */
    public function getRecentPages(): array
    {
        return $this->searchMenuService->getRecentPages();
    }

    /**
     * 获取快捷工具
     * @return array
     */
    public function getQuickTools(): array
    {
        return $this->searchMenuService->getQuickTools();
    }
}
