<?php

namespace Modules\Search\Services;

class SearchMenuService
{
    /**
     * 获取最近访问的页面
     * @return array
     */
    public function getRecentPages(): array
    {
        // 返回静态数据
        return [
            [
                'id' => 1,
                'title' => '页面管理',
                'url' => '/cms/cmsList?model_id=5',
                'time' => '昨天 10:36'
            ],
            // [
            //     'id' => 2,
            //     'title' => '关于我们',
            //     'url' => '/cms/cmsDetail?id=2',
            //     'time' => '昨天 10:36'
            // ],
            // [
            //     'id' => 3,
            //     'title' => '活动管理',
            //     'url' => '/cms/activities',
            //     'time' => '昨天 10:36'
            // ],
            [
                'id' => 4,
                'title' => '新闻管理',
                'url' => '/cms/cmsList?model_id=1',
                'time' => '昨天 10:36'
            ]
        ];
    }

    /**
     * 获取快捷工具
     * @return array
     */
    public function getQuickTools(): array
    {
        // 使用T函数支持多语言
        return [
            [
                'id' => 1,
                'title' => T('Search::base.menuSearch.fileManagement'),
                'desc' => T('Search::base.menuSearch.fileManagementDesc'),
                'icon' => 'Document',
                'url' => '/media/mediaManager'
            ],
            [
                'id' => 2,
                'title' => T('Search::base.menuSearch.googleAnalytics'),
                'desc' => T('Search::base.menuSearch.googleAnalyticsDesc'),
                'icon' => 'DataAnalysis',
                'url' => '/analytics/dashboard'
            ],
            [
                'id' => 3,
                'title' => T('Search::base.menuSearch.seoTools'),
                'desc' => T('Search::base.menuSearch.seoToolsDesc'),
                'icon' => 'Search',
                'url' => '/seo'
            ]
        ];
    }
}
