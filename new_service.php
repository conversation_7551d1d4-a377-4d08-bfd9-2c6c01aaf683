<?php

namespace Modules\SEO\Domain\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Bingo\Exceptions\BizException;
use Modules\SEO\Enums\SEOErrorCode;
use Modules\SEO\Models\SeoLayoutTemplate;
use Modules\SEO\Domain\Repositories\SeoLayoutTemplateRepository;
use Modules\SEO\Models\SeoContentLayoutLog;
use Modules\SEO\Enums\SEOPrompt;
use Modules\SEO\Enums\ArticleType;
use Modules\SEO\Enums\ArticleStyle;

class ContentLayoutService
{
    protected AIService $aiService;
    protected SeoLayoutTemplateRepository  $seoLayoutTemplateRepository;
    protected int $cacheMinutes = 60;

    public function __construct(AIService $aiService, SeoLayoutTemplateRepository $seoLayoutTemplateRepository)
    {
        $this->aiService = $aiService;
        $this->seoLayoutTemplateRepository = $seoLayoutTemplateRepository;
    }

    /**
     * 内容块排版
     *
     * @param array $data
     * @return string
     */
    public function applyBlock(array $data)
    {
        $content = $data['content'] ?? '';
        $style = $data['style'];
        $lang = $data['lang'];
        return $this->blockFormat($content, $style, $lang);
    }

    public function blockFormat(string $content, array $style = [], string $lang = 'zh_CN'): string
    {
        try {
            $cacheKey = 'seo_content_layout_block_' . md5($content . json_encode($style));
            Cache::forget($cacheKey);
            if ($cachedResult = Cache::get($cacheKey)) {
                return $cachedResult;
            }

            // 示例输出结构：
            // <div class="content-block marketing-style mood-happy"
            //      style="background: linear-gradient(...); color: {...};">
            //   {...内容...}
            // </div>

            // 提取所有图片标签并替换为占位符
            $images = [];
            $imageCount = 0;

            $cleanContent = preg_replace_callback('/<img[^>]*>/', function ($matches) use (&$images, &$imageCount) {
                $img = $matches[0];
                $placeholder = "IMG_PLACEHOLDER_{$imageCount}";
                $images[$placeholder] = $img;
                $imageCount++;
                return $placeholder;
            }, $content);

            // 去除图片标签以外的其他HTML标签，但保留图片占位符
            $cleanContent = preg_replace('/<(?!IMG_PLACEHOLDER)[^>]*>/', '', $cleanContent);

            // 去除多余的换行符，将连续的多个换行符替换为单个换行符
            $cleanContent = preg_replace('/\n{2,}/', "\n", $cleanContent);

            // 使用处理后的内容（带有占位符，其他HTML标签已去除，多余换行已清理）
            $content = $cleanContent;

            $action = $style['action'] ?? 'default';
            $define_content = $style['prompt'] ?? '';
            $articleType = ArticleType::from($style['type'] ?? 'default');
            $mood = $style['mood'] ?? 'default';
            $articleStyle = ArticleStyle::getStyle($articleType->value);

            $prompt =  "作为一名专业的内容编辑与排版专家，请按照以下指引对所提供的文本进行优化改写：\n\n";
            // 直接通过枚举值获取对应的枚举实例
            $seo_prompt = SEOPrompt::tryFrom($action) ?? SEOPrompt::EXPAND;

            $prompt .= $seo_prompt->getPrompt($define_content);
            $prompt .= "需要改写的内容：\n\n{$content}\n\n";
            $prompt .= "排版样式要求：\n";
            $prompt .= "- 根据{$articleType->value}特征使用{$articleStyle['primary_color']}-{$articleStyle['secondary_color']}渐变方案\n";
            $prompt .= "- 当情绪参数为欢快时，采用圆角+阴影组合（border-radius:8px + box-shadow: 0 4px 6px rgba(0,0,0,0.1)）\n\n";

            // 添加内容分析指令
            $prompt .= "内容分析与排版指导：\n";
            $prompt .= "- 请先分析内容的风格和类型（如学术专业、新闻报道、营销推广、教程指南、故事叙述等）\n";
            $prompt .= "- 根据识别出的内容风格，应用最适合的排版策略\n";
            $prompt .= "- 对于学术内容，使用正式结构，规范的引用和标注\n";
            $prompt .= "- 对于新闻内容，使用倒金字塔结构，简洁标题，短段落\n";
            $prompt .= "- 对于营销内容，突出关键信息，使用列表展示优势，添加行动召唤\n";
            $prompt .= "- 对于教程内容，清晰标示步骤，突出注意事项，使用图文结合\n";
            $prompt .= "- 对于叙述内容，注重段落流畅过渡，适当格式化对话和情感描写\n";
            $prompt .= "- 分析内容的主题和关键点，确保排版突出这些要素\n";
            $prompt .= "- 评估内容的复杂度，为复杂内容提供更清晰的结构和导航\n";
            $prompt .= "- 识别内容中的重点和强调部分，使用适当的HTML元素突出显示\n\n";

            // 添加Bootstrap排版要求
            $prompt .= "Bootstrap排版與美觀要求 - 打造活潑生動的網頁體驗：\n";
            $prompt .= "- 想像你正在設計一個充滿活力的網頁！使用Bootstrap框架創造視覺層次分明、充滿活力的HTML結構\n";
            $prompt .= "- 將內容變身為多個有趣的獨立區塊(block)，每個區塊就像一個小舞台，展示不同的精彩內容\n";
            $prompt .= "- 保持原文文字精華不變，但讓它們穿上全新的「排版外衣」，煥然一新\n";
            $prompt .= "- 像尋寶一樣發掘所有內容要點，無論相關或不相關，重新組織成吸引眼球的信息流\n";
            $prompt .= "- 使用Bootstrap卡片組件(card)打造精美內容盒子，就像精心包裝的禮物盒，增強視覺吸引力\n";
            $prompt .= "- 讓重要標題閃閃發光！使用帶有淺色背景的醒目樣式，像舞台聚光燈一樣吸引注意\n";
            $prompt .= "- 次要內容可以玩「躲貓貓」，使用折疊面板(collapse)或標籤頁(tab)，點擊才顯示，增加互動趣味\n";
            $prompt .= "- 關鍵點就是內容中的「超級明星」，用Bootstrap的alert組件或徽章(badge)讓它們脫穎而出\n";
            $prompt .= "- 相關內容像好朋友一樣組隊出現，使用卡片組(card-group)或網格系統(grid)創造和諧統一感\n";
            $prompt .= "- 圖片也要「responsive」！確保使用img-fluid類實現響應式顯示，在各種設備上都完美呈現\n";
            $prompt .= "- 列表不再單調！使用Bootstrap的列表組(list-group)，讓清單項目穿上時尚外衣\n";
            $prompt .= "- 引用內容像是特別嘉賓，用帶左側彩色邊框的blockquote樣式，讓它們獨具魅力\n";
            $prompt .= "- 元素之間要保持「社交距離」，使用Bootstrap的間距工具類(spacing utilities)創造舒適閱讀節奏\n";
            $prompt .= "- 色彩搭配要像專業設計師，主要使用柔和的藍色和灰色調，點綴亮色，活潑但不花哨\n";
            $prompt .= "- 最終成品應該像精心設計的雜誌頁面，結構清晰，現代感十足，讓人愛不釋手！\n\n";

            // 最终提示
            $prompt .= "请严格按以下要求生成内容：\n";

            // 输出格式要求
            $prompt .= "【输出格式要求】(必须严格遵守)\n";
            $prompt .= "- 必须且只能返回纯HTML格式代码，不允许任何其他格式；\n";
            $prompt .= "- 严禁在HTML前后添加任何说明、注释、标记或代码块符号；\n";
            $prompt .= "- 严禁使用```html或```等Markdown代码块标记包裹HTML代码；\n";
            $prompt .= "- 严禁包含任何解释、问候、总结或额外说明；\n";
            $prompt .= "- 严禁返回与要求无关的内容或上下文；\n";
            $prompt .= "- 直接输出原始HTML代码，不要添加任何额外的标记或格式；\n";
            $prompt .= "- 绝对不要使用Markdown格式，不要添加```html和```标记；\n";
            $prompt .= "- 在输出前，检查所有HTML标签是否正确闭合，确保没有不完整的标签；\n";
            $prompt .= "- 违反以上任何规则将导致输出无法使用；\n";

            // 内容处理要求
            $prompt .= "【内容处理要求】\n";
            $prompt .= "- 注意需要把上下文中对应块的图片占位符也添加；\n";
            $prompt .= "- 识别并正确处理文章中的小标题、段落、列表、引用等元素；\n";
            $prompt .= "- 识别内容中的图片占位符（如'IMG_PLACEHOLDER_0'），将其放置在最合适的位置；\n";
            $prompt .= "- 严格保持所有HTML标签的完整性，不要删除、修改或破坏任何HTML标签；\n";
            $prompt .= "- 特别重要：不要将图片占位符（如'IMG_PLACEHOLDER_0'）转换为HTML的img标签，保持原始的纯文本占位符格式；\n";
            $prompt .= "- 图片占位符必须保持原样（如'IMG_PLACEHOLDER_0'），只允许调整其在文档中的位置，不得对其做任何修改；\n";
            $prompt .= "- 绝对不要将图片占位符转换为<img>标签，必须保持纯文本格式；\n";
            $prompt .= "- 样式属性必须格式正确，不得出现重复的style属性或格式错误，如style=\" style=\"...\"或style=\"...\"&gt;这类错误；\n";
            $prompt .= "- 绝对不要将HTML标签转义为HTML实体，例如不要将<转义为&lt;或将>转义为&gt;，保持原始的HTML标签格式；\n";
            $prompt .= "- 确保所有HTML标签都正确闭合，每个开始标签必须有对应的结束标签，如<p>必须有</p>，<div>必须有</div>；\n";
            $prompt .= "- 检查并修复任何不完整的标签，确保没有孤立的开始标签或结束标签；\n";
            $prompt .= "- 使用富文本编辑器兼容的HTML标签，如p、div、span、h2-h6、ul、ol、li、blockquote等；\n";
            $prompt .= "- 避免使用复杂的HTML结构或不常见的标签，确保在富文本编辑器中正确显示；\n";
            $prompt .= "- 确保HTML标签正确嵌套，避免嵌套错误；\n";
            $prompt .= "- 语言需要是{$lang}。\n";
            $prompt .= "- 为每个区块添加动态class，例如：class='content-block {$articleType->value}-style mood-{$mood}'";
            $prompt .= "- 文字颜色根据背景亮度自动计算（亮背景用深色文字，暗背景用浅色文字）";

            $messages = [
                [
                    'role' => 'system',
                    'content' => '您是SEO内容专家，负责优化内容结构和可读性。请使用Bootstrap框架进行整体重组排版，将内容分割成多个独立区块，每个区块有明确的主题和视觉边界。保持原文文字内容基本不变，仅调整结构和排版方式。请生成富文本编辑器兼容的HTML代码，确保美观且符合大众审美。严格保持所有HTML标签的完整性，确保不删除、不修改、不破坏任何HTML标签，只调整位置。特别重要：对于图片占位符（如"IMG_PLACEHOLDER_0"），请保持其原始纯文本格式，不要将其转换为HTML的img标签。图片占位符必须保持原样，只允许调整其在文档中的位置。样式属性必须格式正确，不得出现重复的style属性或格式错误。所有HTML标签必须正确闭合和嵌套，每个开始标签必须有对应的结束标签（如<p>必须有</p>，<div>必须有</div>）。检查并修复任何不完整的标签，确保没有孤立的开始标签或结束标签。绝对不要使用Markdown格式，不要在返回的HTML前后添加```html和```标记，直接返回纯HTML代码。'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ];

            $response = $this->aiService->chatCompletion($messages);
            $result = $this->parseResponse($response);

            // 将占位符替换回原始图片标签
            if (!empty($images)) {
                // 记录替换前的图片占位符数量
                $placeholderCount = 0;
                foreach ($images as $placeholder => $imgTag) {
                    $placeholderCount += substr_count($result, $placeholder);
                    $result = str_replace($placeholder, $imgTag, $result);
                }

                // 如果没有找到任何占位符，记录日志并尝试强制插入图片
                if ($placeholderCount === 0) {
                    Log::warning('No image placeholders found in AI response', [
                        'image_count' => count($images),
                        'content_preview' => substr($result, 0, 200)
                    ]);

                    // 尝试在内容末尾添加图片
                    $allImages = '';
                    foreach ($images as $imgTag) {
                        $allImages .= '<div class="image-container">' . $imgTag . '</div>';
                    }

                    // 在内容末尾添加图片区域
                    if (!empty($allImages)) {
                        $result .= '<div class="recovered-images">' . $allImages . '</div>';
                    }
                }
            }

            Cache::put($cacheKey, $result, $this->cacheMinutes * 60);
            return $result;
        } catch (\Bingo\Exceptions\BizException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Content block format failed', [
                'error' => $e->getMessage(),
                'content' => $content,
                'style' => $style
            ]);
            BizException::throws(SEOErrorCode::CONTENT_LAYOUT_FAILED, $e->getMessage());
        }
    }

    /**
     * 生成SEO提示词（第二版）
     *
     * @param string $content 内容参数在此方法中不使用，但保留参数以保持方法签名一致性
     * @return string
     */
    protected function analyzeContentAndGenerateSeoPrompt2(string $content = '')
    {
        $prompt = '生成結構化HTML代碼，使用內聯樣式模擬Bootstrap 5效果，將內容分為多個視覺區塊。

要求：
1. 基本結構
   - 僅生成<body>內HTML，使用內聯style屬性
   - 分為約6個區塊，每區塊模擬card組件
   - 整體使用container布局，最大寬度960px

2. 樣式指南
   - 標題：h2(1.5rem,藍色)、h3(1.25rem,藍色)、h4(1rem,藍色)
   - 段落：引言(1.1rem)、正文(1rem,1.6行高)
   - 列表：無序列表(左邊距20px)、內容大綱(無標記)
   - 圖片：響應式(max-width:100%)、居中、說明文字(灰色,0.875rem)
   - 區塊：白底、淺灰標題欄、圓角(0.25rem)、底部間距(1.5rem)

3. 結構示例
   <div style="background-color:#fff; border:1px solid rgba(0,0,0,0.125); border-radius:0.25rem; margin-bottom:1.5rem;">
     <div style="background-color:rgba(0,0,0,0.03); border-bottom:1px solid rgba(0,0,0,0.125); padding:0.5rem 1rem;">
       <h2 style="font-size:1.5rem; font-weight:bold; color:#007bff; margin:0;">標題</h2>
     </div>
     <div style="padding:1rem;">
       <p style="font-size:1rem; line-height:1.6; margin-bottom:1rem;">內容</p>
       <!-- 其他內容元素 -->
     </div>
     <div style="background-color:rgba(0,0,0,0.03); border-top:1px solid rgba(0,0,0,0.125); padding:0.5rem 1rem; font-size:0.875rem; color:#6c757d;">
       次要信息
     </div>
   </div>

4. 特別注意
   - 保留所有圖片占位符(IMG_PLACEHOLDER_X)
   - 使用等寬字體(Consolas)提高編輯器兼容性
   - 添加區塊註釋便於閱讀
   - 確保HTML標籤正確閉合

請直接輸出HTML代碼，不要添加Markdown標記或額外說明。';

        return $prompt;
    }
