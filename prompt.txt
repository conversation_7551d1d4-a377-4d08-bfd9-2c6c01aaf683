生成結構化HTML代碼，使用內聯樣式模擬Bootstrap 5效果，將內容分為多個視覺區塊。

要求：
1. 基本結構
   - 僅生成<body>內HTML，使用內聯style屬性
   - 分為約6個區塊，每區塊模擬card組件
   - 整體使用container布局，最大寬度960px

2. 樣式指南
   - 標題：h2(1.5rem,藍色)、h3(1.25rem,藍色)、h4(1rem,藍色)
   - 段落：引言(1.1rem)、正文(1rem,1.6行高)
   - 列表：無序列表(左邊距20px)、內容大綱(無標記)
   - 圖片：響應式(max-width:100%)、居中、說明文字(灰色,0.875rem)
   - 區塊：白底、淺灰標題欄、圓角(0.25rem)、底部間距(1.5rem)

3. 結構示例
   <div style="background-color:#fff; border:1px solid rgba(0,0,0,0.125); border-radius:0.25rem; margin-bottom:1.5rem;">
     <div style="background-color:rgba(0,0,0,0.03); border-bottom:1px solid rgba(0,0,0,0.125); padding:0.5rem 1rem;">
       <h2 style="font-size:1.5rem; font-weight:bold; color:#007bff; margin:0;">標題</h2>
     </div>
     <div style="padding:1rem;">
       <p style="font-size:1rem; line-height:1.6; margin-bottom:1rem;">內容</p>
       <!-- 其他內容元素 -->
     </div>
     <div style="background-color:rgba(0,0,0,0.03); border-top:1px solid rgba(0,0,0,0.125); padding:0.5rem 1rem; font-size:0.875rem; color:#6c757d;">
       次要信息
     </div>
   </div>

4. 特別注意
   - 保留所有圖片占位符(IMG_PLACEHOLDER_X)
   - 使用等寬字體(Consolas)提高編輯器兼容性
   - 添加區塊註釋便於閱讀
   - 確保HTML標籤正確閉合

請直接輸出HTML代碼，不要添加Markdown標記或額外說明。
