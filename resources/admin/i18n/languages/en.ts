const en = {
  system: {
    name: 'BWMS Dashboard',
    chinese: 'Chinese',
    english: 'English',
    confirm: 'Confirm',
    cancel: 'Cancel',
    warning: 'Warning',
    next: 'Next',
    prev: 'Prev',
    yes: 'Y',
    no: 'N',
    add: 'Add',
    finish: 'Finish',
    back: 'Back',
    update: 'Update',
    export: 'Export',
    search: 'Search',
    refresh: 'Refresh',
    detail: 'Detail',
    delete: 'Delete',
    prompt: 'Prompt',
    more: 'More',
    logout: 'Log Out',
    fail: 'Fail',
    success: 'Success',
    close: 'Close',
    download: 'Download',
    profile: 'Profile',
    lang: {
      en: 'English',
      zh_HK: 'Traditional Chinese',
      zh_CN: 'Simplified Chinese',
    },
  },

  login: {
    email: 'Email',
    password: 'Password',
    sign_in: 'Sign In',
    welcome: 'Welcome Back👏',
    lost_password: 'lost password?',
    remember: 'Remember me',
    verify: {
      email: {
        required: 'Please input email first',
        invalid: 'Email address is invalid',
      },

      password: {
        required: 'Please input password first',
      },
    },
  },

  register: {
    sign_up: 'Sign Up',
  },

  generate: {
    schema: {
      title: 'Create Schema',
      name: 'Schema Name',
      name_verify: 'please input schema name',
      engine: {
        name: 'Search Engine',
        verify: 'please select schema engine',
        placeholder: 'select schema engine',
      },
      default_field: {
        name: 'Default Field',
        created_at: 'Create time',
        updated_at: 'Update Time',
        creator: 'Creator',
        delete_at: 'SoftDelete',
      },
      comment: {
        name: 'Schema Comment',
        verify: 'please input schema comment',
      },

      structure: {
        title: 'Create Schema Structure',
        field_name: {
          name: 'Field Name',
          verify: 'please input field name',
        },
        length: 'Length',
        type: {
          name: 'Field Type',
          placeholder: 'select field type',
          verify: 'please select field type',
        },
        form_label: 'Form Label',
        form_component: 'Component',
        list: 'List',
        form: 'Form',
        unique: 'Unique',
        search: 'Search',
        search_op: {
          name: 'Search Operate',
          placeholder: 'select search operate',
        },
        nullable: 'Nullable',
        default: 'Default',
        rules: {
          name: 'Verify Rules',
          placeholder: 'select verify rules',
        },
        operate: 'Operate',
        comment: 'Field Comment',
      },
    },
    code: {
      title: 'Code Gen',
      module: {
        name: 'module',
        placeholder: 'please select module',
        verify: 'please select module first',
      },
      controller: {
        name: 'Controller',
        placeholder: 'please input controller name',
        verify: 'please input Controller name  first',
      },
      model: {
        name: 'Model',
        placeholder: 'please input model name',
        verify: 'please input model name  first',
      },
      paginate: 'Paginate',
    },
  },

  module: {
    create: 'Create Module',
    update: 'Update Module',
    form: {
      name: {
        title: 'Module Name',
        required: 'module name required',
      },

      path: {
        title: 'Module Path',
        required: 'module Path required',
      },

      desc: {
        title: 'Description',
      },

      keywords: {
        title: 'Keywords',
      },

      dirs: {
        title: 'Default Dirs',
        Controller: 'Controller',
        Model: 'Model',
        Database: 'Database',
        Request: 'Request',
      },
    },
  },
  widgets: {
    widget_configuration: 'Widget Configuration',
    report_widget: 'Configure Report Widget',
    width: 'Width (1-12)',
    newLine: 'Force New Line',
    title: 'Widget Title',
    manage: 'Manage Widget',
    add: 'Add Widget',
    widgets: 'Widget',
    close: 'Close',
    sure: 'Confirm',
    loading: 'Loading...',
    title_required: 'Please enter the widget title',
    x: 'X',
    y: 'Y',
    h: 'Height',
  },

  header: {
    settings: {
      tit1: 'Account',
      li1: 'Profile & Preferences',
      li2: 'Reset password',
      tit2: 'System Language',
      li3: 'Language Switching',
      langswitch: 'Language Switching',
    },
    contact: {
      title: 'Your After-Sales Specialist',
      label1: 'Service hours',
      label2: 'Email',
      btn_text: 'Send Email',
    },
  },
  home: {
    title: 'DASHBOARD',
    btn_text1: 'Add Widget',
    dialog_tit1: 'Are you sure to delete this module?',
    activityLog: {
      th1: 'Account',
      th2: 'Browser',
      th3: 'Platform',
      th4: 'IP',
      th5: 'State',
      th6: 'Login Time',
    },
    analytics: {
      placeholder1: 'Start Time',
      placeholder2: 'End Time',
      reportTypes: {
        basicUser: 'Basic User Report',
        trafficSource: 'Traffic Source Report',
        pagePerformance: 'Page Performance Report',
        ecommerce: 'E-commerce Report',
        userBehavior: 'User Behavior Report',
        mobileApp: 'Mobile App Report',
        adPerformance: 'Ad Performance Report',
        content: 'Content Analysis Report',
        geographic: 'Geographic Report',
        technical: 'Technical Analysis Report',
        event: 'Event Analysis Report',
        conversionFunnel: 'Conversion Funnel Report',
      },
    },
    news: {
      title: 'News',
    },
    system: {
      title1: 'Website Storage Capacity',
      title2: 'How many days are left until renewal',
      con1: 'Day',
      btn_text1: 'View Storage',
      btn_text2: 'Renewal Now',
      btn_text3: 'Increase Capacity',
    },
  },
  404: {
    tips: 'Sorry, the page you visited does not exist.',
    btn_text: 'Return to Home',
  },
  analytics: {
    dimensions: {
      date: 'Date',
      country: 'Country',
      deviceCategory: 'Device Category',
      source: 'Source',
      medium: 'Medium',
      campaign: 'Campaign',
      pagePath: 'Page Path',
      itemName: 'Item Name',
      itemCategory: 'Item Category',
      sessionSourceMedium: 'Session Source/Medium',
      landingPage: 'Landing Page',
      appVersion: 'App Version',
      operatingSystem: 'Operating System',
      adGroup: 'Ad Group',
      adContent: 'Ad Content',
      pageTitle: 'Page Title',
      region: 'Region',
      city: 'City',
      browser: 'Browser',
      eventName: 'Event Name',
      eventCategory: 'Event Category',
    },
    metrics: {
      totalUsers: 'Total Users',
      newUsers: 'New Users',
      sessions: 'Sessions',
      bounceRate: 'Bounce Rate',
      screenPageViews: 'Screen/Page Views',
      averageSessionDuration: 'Avg. Session Duration',
      conversions: 'Conversions',
      averagePageLoadTime: 'Avg. Page Load Time',
      exitRate: 'Exit Rate',
      itemViews: 'Item Views',
      itemsAddedToCart: 'Items Added to Cart',
      purchases: 'Purchases',
      itemRevenue: 'Item Revenue',
      engagementRate: 'Engagement Rate',
      conversionsPerSession: 'Conversions per Session',
      crashFreeUsersRate: 'Crash-Free Users Rate',
      userEngagementDuration: 'User Engagement Duration',
      adClicks: 'Ad Clicks',
      adImpressions: 'Ad Impressions',
      adCost: 'Ad Cost',
      adConversions: 'Ad Conversions',
      averageTimeOnPage: 'Avg. Time on Page',
      entrances: 'Entrances',
      eventCount: 'Event Count',
      eventValue: 'Event Value',
      addToCarts: 'Add to Carts',
      checkouts: 'Checkouts',
    },
  },
  dashboard: {
    loading: 'Loading...',
    home: 'Home',
    title: 'Dashboard',
    content: {
      title: 'Content Management',
    },
    plugins: {
      title: 'Plugins',
      button: 'Plugins',
    },
    aiCapabilities: {
      title: 'AI Capabilities',
    },
    usefulTools: {
      title: 'Useful Tools',
    },
    analytics: {
      title: 'Analytics',
    },
    websiteStatus: {
      diskSpace: {
        title: 'Disk Space',
      },
      renewal: {
        title: 'Renewal Information',
        days: 'Days',
        increaseCapacity: 'Increase Capacity',
        renewalNow: 'Renewal Now',
        dateRange: {
          to: 'To'
        }
      }
    },
    news: {
      title: 'News',
      learnMore: 'Learn More',
    },
    userInfo: {
      title: 'Have a Question?',
      specialist: 'Your After-Sales Specialist',
      contactButton: 'Click to Contact',
    },
    statisticCard: {
      totalPages: '{total} pages in total',
      cards: {
        activePages: 'Active Pages',
        totalVisits: 'Total Visits',
        uniqueVisitors: 'Unique Visitors',
        systemAlerts: 'System Alerts',
      },
      units: {
        pages: 'pages',
        visits: 'visits',
        users: 'users',
        alerts: 'alerts',
      }
    },
    websiteAnalytics: {
      tabs: {
        overview: 'Data Overview',
        content: 'Content Difference',
        hot: 'Hot Content'
      },
      filters: {
        timeRange: {
          label: 'Time Range',
          options: {
            last30: 'Last 30 Days',
            last60: 'Last 60 Days',
            last90: 'Last 90 Days'
          }
        },
        sortBy: {
          label: 'Sort By',
          options: {
            content: 'Update Content',
            time: 'Time'
          }
        }
      },
      content: {
        updateCount: 'Content Update Count',
        updateTypes: 'Update Types',
        articles: 'Articles: {count}',
        products: 'Product Pages: {count}',
        images: 'Images: {count}',
        charts: {
          trend: 'Update Trend',
          distribution: 'Content Type Distribution',
          comparison: 'Content Difference Comparison'
        }
      },
      competitor: {
        add: 'Add Competitor',
        dialog: {
          title: 'Add Competitor',
          name: 'Website Name',
          namePlaceholder: 'Please enter website name',
          url: 'Website URL',
          urlPlaceholder: 'Please enter website URL',
          confirm: 'Confirm',
          cancel: 'Cancel'
        },
        messages: {
          fillRequired: 'Please fill in all required information',
          addSuccess: 'Added successfully',
          addFailed: 'Add failed'
        }
      }
    },
    ga4Iframe: {
      loading: 'Loading...',
      error: 'Failed to load GA4 data'
    },
    userMenu: {
      title: 'Personal Center',
      backToDashboard: 'Back to Dashboard',
      items: {
        personalInfo: 'Personal Information',
        accountBinding: 'Account Binding',
        mfa: 'Multi-Factor Authentication',
        socialIdentity: 'Social Identity Management',
        enterpriseIdentity: 'Enterprise Identity Management',
        accountSecurity: 'Account Security',
        accessLog: 'Access Log'
      }
    },
    accessLog: {
      filter: {
        button: 'Filter',
        title: 'Filter Conditions',
        ipAddress: 'IP Address',
        ipPlaceholder: 'Enter IP address',
        dateRange: 'Date Range',
        startDate: 'Start Date',
        endDate: 'End Date',
        reset: 'Reset',
        apply: 'Apply'
      },
      table: {
        loginTime: 'Login Time',
        loginUser: 'Login User',
        ipAddress: 'IP Address',
        status: 'Status',
        browser: 'Browser',
        location: 'Location',
        statusSuccess: 'Success',
        statusFailed: 'Failed'
      },
      error: {
        fetchFailed: 'Failed to get login history, please try again later'
      }
    },
    accountBinding: {
      email: {
        title: 'Email',
        unbind: 'Unbind',
        bind: 'Bind',
        modify: 'Modify',
        notBound: 'Not bound',
        current: 'Current Email',
        new: 'New Email',
        verificationCode: 'Verification Code',
        currentVerificationCode: 'Current Email Verification Code',
        newVerificationCode: 'New Email Verification Code',
        unbindTitle: 'Unbind Email',
        bindTitle: 'Bind Email',
        modifyTitle: 'Modify Email',
        placeholder: 'Please enter email',
        codePlaceholder: 'Please enter 6-digit code'
      },
      phone: {
        title: 'Phone',
        unbind: 'Unbind',
        bind: 'Bind',
        modify: 'Modify',
        notBound: 'Not bound',
        current: 'Current Phone',
        new: 'New Phone',
        verificationCode: 'Verification Code',
        currentVerificationCode: 'Current Phone Verification Code',
        newVerificationCode: 'New Phone Verification Code',
        unbindTitle: 'Unbind Phone',
        bindTitle: 'Bind Phone',
        modifyTitle: 'Modify Phone',
        placeholder: 'Please enter phone number',
        codePlaceholder: 'Please enter 6-digit code'
      },
      common: {
        confirm: 'Confirm',
        cancel: 'Cancel',
        sendCode: 'Send Code',
        countdown: '{seconds}s',
        required: 'Please fill in all required information',
        invalidEmail: 'Please enter a valid email format',
        invalidPhone: 'Please enter a valid phone number format',
        success: {
          update: '{type} updated successfully',
          unbind: '{type} unbound successfully'
        },
        error: {
          update: 'Failed to update {type}, please check if the information is correct',
          unbind: 'Failed to unbind {type}, please check if the verification code is correct',
          sendCode: 'Failed to send verification code, please try again later'
        }
      }
    },
    accountSafety: {
      score: {
        title: 'Security Score',
        level: 'Security Level: {level}',
        levels: {
          low: 'Low',
          medium: 'Medium',
          high: 'High'
        }
      },
      password: {
        title: 'Password Settings',
        strength: {
          weak: 'Weak',
          medium: 'Medium',
          strong: 'Strong'
        },
        description: 'It is recommended to use a complex password.',
        modify: 'Modify',
        modifyNow: 'Modify Now',
        dialog: {
          title: 'Modify Password',
          oldPassword: 'Current Password',
          newPassword: 'New Password',
          confirmPassword: 'Confirm Password',
          oldPasswordRequired: 'Please enter current password',
          newPasswordRequired: 'Please enter new password',
          passwordLength: 'Password must be at least 6 characters',
          passwordMismatch: 'Passwords do not match',
          placeholder: {
            oldPassword: 'Enter current password',
            newPassword: 'Enter new password',
            confirmPassword: 'Confirm new password'
          }
        }
      },
      accountDeletion: {
        title: 'Account Deletion',
        description: 'Permanently delete account and all data, please proceed with caution.',
        delete: 'Delete',
        dialog: {
          title: 'Account Deletion',
          warning: 'After deletion, all data of this account will be deleted and cannot be recovered. Please proceed with caution!',
          account: 'Account',
          password: 'Password',
          phone: 'Phone',
          verificationCode: 'Verification Code',
          placeholder: {
            account: 'Please enter current account',
            password: 'Please enter current password',
            phone: 'Please enter phone number',
            code: 'Please enter verification code'
          }
        }
      },
      mfa: {
        title: 'Multi-Factor Authentication (MFA)',
        enabled: 'Enabled',
        disabled: 'Disabled',
        enable: 'Enable',
        disable: 'Disable'
      },
      email: {
        title: 'Email Binding',
        bound: 'Bound',
        unbound: 'Unbound',
        modify: 'Modify',
        bind: 'Bind'
      },
      phone: {
        title: 'Phone Binding',
        bound: 'Bound',
        unbound: 'Unbound',
        modify: 'Modify',
        bind: 'Bind'
      },
      common: {
        confirm: 'Confirm',
        cancel: 'Cancel',
        sendCode: 'Send Code',
        countdown: '{seconds}s',
        success: {
          passwordUpdate: 'Password updated successfully',
          accountDeletion: 'Account deletion request submitted',
          codeSent: 'Verification code sent'
        },
        error: {
          passwordUpdate: 'Failed to update password, please check if the current password is correct',
          accountDeletion: 'Account deletion failed, please check if the information is correct',
          sendCode: 'Failed to send verification code, please try again later',
          verificationCode: 'Please enter verification code',
          phoneNumber: 'Please enter phone number'
        }
      }
    },
    enterpriseIdentity: {
      table: {
        account: 'Account',
        status: 'Status',
        boundAccount: 'Bound Account',
        actions: 'Actions',
        statusTags: {
          normal: 'Normal',
          abnormal: 'Abnormal'
        }
      },
      actions: {
        bind: 'Bind',
        unbind: 'Unbind'
      },
      status: {
        notBound: 'Not bound'
      },
      messages: {
        syncSuccess: 'Synchronizing {name}',
        viewDetails: 'View {name} details'
      }
    },
    mfa: {
      title: 'Multi-Factor Authentication',
      methods: {
        sms: {
          title: 'SMS Verification',
          description: 'Receive verification code via SMS to authenticate login'
        },
        email: {
          title: 'Email Verification',
          description: 'Receive verification code via email to authenticate login'
        },
        otp: {
          title: 'OTP Authentication',
          description: 'Use OTP one-time password to authenticate login'
        }
      },
      dialog: {
        sms: {
          title: 'Bind Phone Number',
          phone: 'Phone Number',
          phonePlaceholder: 'Please enter phone number',
          code: 'Verification Code',
          codePlaceholder: 'Please enter 6-digit code'
        },
        email: {
          title: 'Bind Email Address',
          email: 'Email Address',
          emailPlaceholder: 'Please enter email address',
          code: 'Verification Code',
          codePlaceholder: 'Please enter 6-digit code'
        },
        passkey: {
          title: 'Create Passkey',
          noData: 'No Data',
          description: 'With Passkey, you can use your fingerprint, face, screen settings or physical security key recorded account. Please only set up Passkey on your own devices.',
          create: 'Create Passkey'
        }
      },
      button: {
        bind: 'Bind',
        unbind: 'Unbind',
        sendCode: 'Send Code',
        resendCode: 'Resend in {seconds}s',
        confirm: 'Confirm',
        cancel: 'Cancel'
      },
      message: {
        phoneRequired: 'Please enter phone number',
        emailRequired: 'Please enter email address',
        codeRequired: 'Please enter verification code',
        codeSent: 'Verification code sent',
        bindSuccess: '{type} bound successfully',
        bindFailed: '{type} binding failed',
        unbindSuccess: '{type} unbound successfully',
        unbindFailed: '{type} unbinding failed',
        passkeySuccess: 'Passkey created successfully'
      }
    },
    personalInfo: {
      title: 'Personal Information',
      input_placeholder: "Please enter {label}",
      select_placeholder: "Please select {label}",
      actions: {
        edit: 'Edit',
        save: 'Save',
        cancel: 'Cancel'
      },
      form: {
        username: 'Username',
        userId: 'User ID',
        email: 'Email',
        phone: 'Phone',
        name: 'Name',
        title: 'Title',
        gender: {
          label: 'Gender',
          male: 'Male',
          female: 'Female',
          other: 'Other',
          unknown: 'Unknown'
        },
        birthdate: 'Birthday',
        company: 'Company',
        idNumber: 'ID Number',
        registrationTime: 'Registration Time',
        address: 'Address',
        avatar: {
          upload: 'Upload Avatar',
          tips: 'Please click edit button to enable editing mode',
          success: 'Avatar uploaded successfully',
          error: 'Failed to upload avatar, please try again',
          formatError: 'Avatar image can only be JPG/PNG/GIF/BMP/WebP format!',
          sizeError: 'Avatar image size cannot exceed 10MB!'
        }
      },
      message: {
        updateSuccess: 'Personal information updated successfully',
        updateError: 'Failed to update personal information, please try again'
      }
    },
    socialIdentity: {
      table: {
        account: 'Account',
        status: 'Status',
        boundAccount: 'Bound Account',
        actions: 'Actions',
        statusTags: {
          bound: 'Bound',
          notBound: 'Not Bound'
        }
      },
      actions: {
        bind: 'Bind',
        unbind: 'Unbind'
      },
      sources: {
        wechat: {
          name: 'WeChat',
          desc: 'Login with WeChat account'
        },
        github: {
          name: 'Github',
          desc: 'Login with Github account'
        },
        dingtalk: {
          name: 'DingTalk',
          desc: 'Login with DingTalk account'
        }
      },
      messages: {
        unbindSuccess: 'Successfully unbound {name}',
        bindSuccess: 'Successfully bound {name}',
        bindFailed: 'Failed to bind {name}, please try again',
        unbindFailed: 'Failed to unbind {name}, please try again',
        fetchError: 'Failed to fetch social identity sources'
      }
    },
    cms: {
      parent: 'Content Management',
      create: 'Create',
      news: {
        name: 'News',
        edit: 'Edit News'
      },
      case: {
        name: 'Customer Cases',
        edit: 'Edit Case'
      },
      product: {
        name: 'Products',
        edit: 'Edit Product'
      },
      job: {
        name: 'Job Recruitment',
        edit: 'Edit Job'
      },
      page: {
        name: 'Page Management',
        edit: 'Edit Page'
      },
      messages: {
        name: 'Guestbook',
        edit: 'Edit Message'
      },
      recommend: {
        name: 'Recommendation List',
        detail: 'Recommendation Detail',
        create: 'Create Recommendation',
        edit: 'Edit Recommendation'
      },
      categories: 'Categories',
      contact: {
        records: 'Contact Records'
      }
    },
  },
  iam: {
    menu: {
      application: 'Application',
      identitySource: {
        title: 'Identity Source',
        social: 'Social Identity',
        enterprise: 'Enterprise Identity'
      },
      userManagement: {
        title: 'User Management',
        userList: 'User List',
        userGroup: 'User Groups',
        organization: 'Organization',
        position: 'Position Management'
      },
      permission: {
        title: 'Permission',
        role: 'Role Management'
      },
      security: {
        title: 'Security',
        mfa: 'Multi-Factor Auth'
      },
      branding: {
        title: 'Branding',
        message: 'Message Settings'
      }
    }
  },
  Router: {
    personalInfo: 'Personal Information',
  }
}

export default en
