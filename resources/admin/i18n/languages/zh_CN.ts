const zh_CN = {
  system: {
    name: 'BWMS 管理系统',
    chinese: '中文',
    english: '英文',
    confirm: '确定',
    cancel: '取消',
    warning: '警告',
    next: '下一页',
    prev: '上一页',
    yes: '是',
    no: '否',
    add: '新增',
    edit: '编辑',
    finish: '完成',
    back: '返回',
    update: '更新',
    export: '导出',
    search: '搜索',
    refresh: '重置',
    detail: '详情',
    delete: '删除',
    prompt: '提示',
    more: '更多',
    logout: '登出',
    fail: '失败',
    success: '成功',
    close: '关闭',
    download: '下载',
    profile: '个人资料',
    lang: {
      en: '英文',
      zh_HK: '繁体中文',
      zh_CN: '简体中文',
    },
  },

  login: {
    email: '邮箱',
    password: '密码',
    sign_in: '登录',
    welcome: '👏欢迎回来',
    lost_password: '忘记密码?',
    remember: '记住我',
    verify: {
      email: {
        required: '请先输入邮箱',
        invalid: '邮箱地址无效',
      },

      password: {
        required: '请先输入密码',
      },
    },
  },

  register: {
    sign_up: '注册',
  },
  generate: {
    schema: {
      title: '创建数据表',
      name: '表名称',
      name_verify: '请输入表名称',
      engine: {
        name: '表引擎',
        verify: '请选择表引擎',
        placeholder: '选择表引擎',
      },
      default_field: {
        name: '默认字段',
        created_at: '创建时间',
        updated_at: '更新时间',
        creator: '创建人',
        delete_at: '软删除',
      },
      comment: {
        name: '表注释',
        verify: '请填写表注释/说明',
      },

      structure: {
        title: '创建数据结构',
        field_name: {
          name: '字段名称',
          verify: '请填写字段名称',
        },
        length: '长度',
        type: {
          name: '类型',
          placeholder: '选择字段类型',
          verify: '请先选择字段类型',
        },
        form_label: '表单 Label',
        form_component: '表单组件',
        list: '列表',
        form: '表单',
        unique: '唯一',
        search: '查询',
        search_op: {
          name: '搜索操作符',
          placeholder: '选择搜索操作符',
        },
        nullable: 'nullable',
        default: '默认值',
        rules: {
          name: '验证规则',
          placeholder: '选择验证规则',
        },
        operate: '操作',
        comment: '字段注释',
      },
    },
    code: {
      title: '生成代码',
      module: {
        name: '模块',
        placeholder: '请选择模块',
        verify: '请选择模块',
      },
      controller: {
        name: '控制器',
        placeholder: '请输入控制器名称',
        verify: '请输入控制器名称',
      },
      model: {
        name: '模型',
        placeholder: '请输入模型名称',
        verify: '请输入模型名称',
      },
      paginate: '分页',
      menu: {
        name: '菜单名称',
        placeholder: '请输入菜单名称',
        verify: '请输入菜单名称',
      },
    },
  },

  module: {
    create: '创建模块',
    update: '更新模块',
    form: {
      name: {
        title: '模块名称',
        required: '请输入模块名称',
      },

      path: {
        title: '模块目录',
        required: '请输入模块目录',
      },

      desc: {
        title: '模块描述',
      },

      keywords: {
        title: '模块关键字',
      },

      dirs: {
        title: '默认目录',
        Controller: 'Controller 目录',
        Model: 'Model 目录',
        Database: 'Database 目录',
        Request: 'Request 目录',
      },
    },
  },
  widgets: {
    widget_configuration: '小部件配置',
    report_widget: '配置报告小部件',
    width: '宽度（1-12）',
    newLine: '强制新行',
    title: '小部件标题',
    manage: '管理小部件',
    add: '添加小部件',
    widgets: '小部件',
    close: '关闭',
    sure: '确定',
    loading: '加载中...',
    title_required: '请输入小部件标题',
    x: 'x轴',
    y: 'y轴',
    h: '高度',
  },

  header: {
    settings: {
      tit1: '账号',
      li1: '个人资料和首选项',
      li2: '重置密码',
      tit2: '系统语言',
      li3: '语言切换',
      langswitch: '语言切换',
    },
    contact: {
      title: '您的售后服务专员',
      label1: '服务时间',
      label2: '电子邮件',
      btn_text: '发送邮件',
    },
  },
  home: {
    title: '控制面板',
    btn_text1: '添加小部件',
    dialog_tit1: '确认删除该模块？',
    activityLog: {
      th1: '账户',
      th2: '浏览器',
      th3: '平台',
      th4: 'IP',
      th5: '状态',
      th6: '登录时间',
    },
    analytics: {
      placeholder1: '开始时间',
      placeholder2: '结束时间',
      reportTypes: {
        basicUser: '基本用户报告',
        trafficSource: '流量来源报告',
        pagePerformance: '页面性能报告',
        ecommerce: '电子商务报告',
        userBehavior: '用户行为报告',
        mobileApp: '移动应用报告',
        adPerformance: '广告效果报告',
        content: '内容分析报告',
        geographic: '地理位置报告',
        technical: '技术分析报告',
        event: '事件分析报告',
        conversionFunnel: '转化漏斗报告',
      },
    },
    news: {
      title: '新闻',
    },
    system: {
      title1: '网站存储容量',
      title2: '离续订还有几天',
      con1: '天',
      btn_text1: '查看存储',
      btn_text2: '立即续订',
      btn_text3: '增加容量',
    },
  },
  404: {
    tips: '抱歉，您访问的页面不存在',
    btn_text: '回到首页',
  },
  analytics: {
    dimensions: {
      date: '日期',
      country: '国家',
      deviceCategory: '设备类别',
      source: '来源',
      medium: '媒介',
      campaign: '活动',
      pagePath: '页面路径',
      itemName: '商品名称',
      itemCategory: '商品类别',
      sessionSourceMedium: '会话来源/媒介',
      landingPage: '着陆页',
      appVersion: '应用版本',
      operatingSystem: '操作系统',
      adGroup: '广告组',
      adContent: '广告内容',
      pageTitle: '页面标题',
      region: '地区',
      city: '城市',
      browser: '浏览器',
      eventName: '事件名称',
      eventCategory: '事件类别',
    },
    metrics: {
      totalUsers: '总用户数',
      newUsers: '新用户数',
      sessions: '会话数',
      bounceRate: '跳出率',
      screenPageViews: '页面浏览量',
      averageSessionDuration: '平均会话时长',
      conversions: '转化次数',
      averagePageLoadTime: '平均页面加载时间',
      exitRate: '退出率',
      itemViews: '商品浏览次数',
      itemsAddedToCart: '加入购物车次数',
      purchases: '购买次数',
      itemRevenue: '商品收入',
      engagementRate: '参与率',
      conversionsPerSession: '每次会话转化次数',
      crashFreeUsersRate: '无崩溃用户率',
      userEngagementDuration: '用户参与时长',
      adClicks: '广告点击次数',
      adImpressions: '广告展示次数',
      adCost: '广告成本',
      adConversions: '广告转化次数',
      averageTimeOnPage: '平均页面停留时间',
      entrances: '入口次数',
      eventCount: '事件次数',
      eventValue: '事件价值',
      addToCarts: '加入购物车次数',
      checkouts: '结账次数',
    },
  },
  dashboard: {
    loading: '加载中...',
    home: '首页',
    title: '仪表盘',
    content: {
      title: '内容管理',
    },
    plugins: {
      title: '插件',
      button: '插件',
    },
    aiCapabilities: {
      title: 'AI 功能',
    },
    usefulTools: {
      title: '实用工具',
    },
    analytics: {
      title: '追踪分析',
    },
    websiteStatus: {
      diskSpace: {
        title: '磁盘空间',
      },
      renewal: {
        title: '续费信息',
        days: '天',
        increaseCapacity: '增加容量',
        renewalNow: '立即续订',
        dateRange: {
            to: '至'
        }
      }
    },
    news: {
      title: '新闻',
      learnMore: '了解更多',
    },
    userInfo: {
      title: '有疑问？',
      specialist: '您的售后服务专员',
      contactButton: '点击联系',
    },
    statisticCard: {
      totalPages: '共 {total} 页',
      cards: {
        activePages: '活跃页面',
        totalVisits: '总访问量',
        uniqueVisitors: '独立访客',
        systemAlerts: '系统提醒',
      },
      units: {
        pages: '页',
        visits: '次',
        users: '人',
        alerts: '条',
      }
    },
    websiteAnalytics: {
      tabs: {
        overview: '数据概览',
        content: '内容差异',
        hot: '热点内容'
      },
      filters: {
        timeRange: {
          label: '时间范围',
          options: {
            last30: '最近 30天',
            last60: '最近 60天',
            last90: '最近 90天'
          }
        },
        sortBy: {
          label: '排序方式',
          options: {
            content: '更新内容量',
            time: '时间'
          }
        }
      },
      content: {
        updateCount: '内容更新数量',
        updateTypes: '更新类型',
        articles: '文章：{count}篇',
        products: '产品页：{count}页',
        images: '图片：{count}张',
        charts: {
          trend: '更新趋势',
          distribution: '更新内容类型分布',
          comparison: '更新内容差异比较'
        }
      },
      competitor: {
        add: '添加竞争对手',
        dialog: {
          title: '添加竞争对手',
          name: '网站名称',
          namePlaceholder: '请输入网站名称',
          url: '网站地址',
          urlPlaceholder: '请输入网站地址',
          confirm: '确定',
          cancel: '取消'
        },
        messages: {
          fillRequired: '请填写完整信息',
          addSuccess: '添加成功',
          addFailed: '添加失败'
        }
      }
    },
    ga4Iframe: {
      loading: '加载中...',
      error: '加载 GA4 数据失败'
    },
    userMenu: {
      title: '个人中心',
      backToDashboard: '返回控制台',
      items: {
        personalInfo: '个人信息',
        accountBinding: '账号绑定',
        mfa: '多因素认证',
        socialIdentity: '社会化身份源管理',
        enterpriseIdentity: '企业化身份源管理',
        accountSecurity: '账号安全',
        accessLog: '访问日志'
      }
    },
    accessLog: {
      filter: {
        button: '筛选',
        title: '筛选条件',
        ipAddress: 'IP地址',
        ipPlaceholder: '输入IP地址',
        dateRange: '日期范围',
        startDate: '开始日期',
        endDate: '结束日期',
        reset: '重置',
        apply: '筛选'
      },
      table: {
        loginTime: '登录时间',
        loginUser: '登录用户',
        ipAddress: 'IP 地址',
        status: '状态',
        browser: '浏览器',
        location: '地理位置',
        statusSuccess: '成功',
        statusFailed: '失败'
      },
      error: {
        fetchFailed: '获取登录历史失败，请稍后重试'
      }
    },
    accountBinding: {
      email: {
        title: '邮箱',
        unbind: '解绑',
        bind: '绑定',
        modify: '修改',
        notBound: '未绑定',
        current: '当前邮箱',
        new: '新邮箱',
        verificationCode: '验证码',
        currentVerificationCode: '当前邮箱验证码',
        newVerificationCode: '新邮箱验证码',
        unbindTitle: '解绑邮箱',
        bindTitle: '绑定邮箱',
        modifyTitle: '修改邮箱',
        placeholder: '请输入邮箱',
        codePlaceholder: '请输入6位验证码'
      },
      phone: {
        title: '手机号',
        unbind: '解绑',
        bind: '绑定',
        modify: '修改',
        notBound: '未绑定',
        current: '当前手机号',
        new: '新手机号',
        verificationCode: '验证码',
        currentVerificationCode: '当前手机号验证码',
        newVerificationCode: '新手机号验证码',
        unbindTitle: '解绑手机号',
        bindTitle: '绑定手机号',
        modifyTitle: '修改手机号',
        placeholder: '请输入手机号',
        codePlaceholder: '请输入6位验证码'
      },
      common: {
        confirm: '确认',
        cancel: '取消',
        sendCode: '发送验证码',
        countdown: '{seconds}s',
        required: '请填写所有必要信息',
        invalidEmail: '请输入正确的邮箱格式',
        invalidPhone: '请输入正确的手机号格式',
        success: {
          update: '{type}更新成功',
          unbind: '{type}已解绑'
        },
        error: {
          update: '更新{type}失败，请检查信息是否正确',
          unbind: '解绑{type}失败，请检查验证码是否正确',
          sendCode: '发送验证码失败，请稍后重试'
        }
      }
    },
    accountSafety: {
      score: {
        title: '安全评分',
        level: '安全级别：{level}',
        levels: {
          low: '低',
          medium: '中',
          high: '高'
        }
      },
      password: {
        title: '密码设置',
        strength: {
          weak: '弱',
          medium: '中',
          strong: '强'
        },
        description: '建议使用较为复杂的密码。',
        modify: '修改',
        modifyNow: '立即修改',
        dialog: {
          title: '修改密码',
          oldPassword: '原始密码',
          newPassword: '新密码',
          confirmPassword: '确认密码',
          oldPasswordRequired: '请输入原始密码',
          newPasswordRequired: '请输入新密码',
          passwordLength: '密码长度不能小于6个字符',
          passwordMismatch: '两次输入的密码不一致',
          placeholder: {
            oldPassword: '请输入原始密码',
            newPassword: '请输入新密码',
            confirmPassword: '请确认密码'
          }
        }
      },
      accountDeletion: {
        title: '账号注销',
        description: '永久删除账号和所有数据，请谨慎操作。',
        delete: '注销',
        dialog: {
          title: '账号注销',
          warning: '注销后，此账号的所有数据都将被删除且不可逆，请谨慎操作！',
          account: '账号',
          password: '密码',
          phone: '手机号',
          verificationCode: '验证码',
          placeholder: {
            account: '请输入当前账号',
            password: '请输入当前密码',
            phone: '请输入手机号',
            code: '请输入验证码'
          }
        }
      },
      mfa: {
        title: '多因素认证',
        methods: {
          sms: {
            title: '短信验证码',
            description: '使用短信形式接收验证码认证登录'
          },
          email: {
            title: '电子邮箱验证',
            description: '使用邮件形式接收验证码认证登录'
          },
          otp: {
            title: 'OTP 口令验证',
            description: '使用 OTP 一次性口令认证登录'
          }
        },
        dialog: {
          sms: {
            title: '绑定二次验证手机号',
            phone: '输入手机号',
            phonePlaceholder: '请输入手机号',
            code: '短信验证码',
            codePlaceholder: '请输入6位验证码'
          },
          email: {
            title: '绑定二次验证邮箱',
            email: '输入邮箱账号',
            emailPlaceholder: '请输入邮箱账号',
            code: '邮箱验证码',
            codePlaceholder: '请输入6位验证码'
          },
          passkey: {
            title: '创建 Passkey',
            noData: '暂无数据',
            description: '借助 Passkey，您可以使用自己的指纹、面孔、屏幕设置或实体安全密钥录制的账号，请仅在您自有的设备上设置 Passkey。',
            create: '创建 Passkey'
          }
        },
        button: {
          bind: '绑定',
          unbind: '解绑',
          sendCode: '发送验证码',
          resendCode: '{seconds}s后重新发送',
          confirm: '确认',
          cancel: '取消'
        },
        message: {
          phoneRequired: '请输入手机号',
          emailRequired: '请输入邮箱地址',
          codeRequired: '请输入验证码',
          codeSent: '验证码已发送',
          bindSuccess: '{type}绑定成功',
          bindFailed: '{type}绑定失败',
          unbindSuccess: '{type}解绑成功',
          unbindFailed: '{type}解绑失败',
          passkeySuccess: 'Passkey创建成功'
        }
      },
      email: {
        title: '邮箱绑定',
        bound: '已绑定',
        unbound: '未绑定',
        modify: '修改',
        bind: '绑定'
      },
      phone: {
        title: '手机绑定',
        bound: '已绑定',
        unbound: '未绑定',
        modify: '修改',
        bind: '绑定'
      },
      common: {
        confirm: '确认',
        cancel: '取消',
        sendCode: '发送验证码',
        countdown: '{seconds}s',
        success: {
          passwordUpdate: '密码修改成功',
          accountDeletion: '账号注销请求已提交',
          codeSent: '验证码已发送'
        },
        error: {
          passwordUpdate: '修改密码失败，请检查原密码是否正确',
          accountDeletion: '账号注销失败，请检查输入信息是否正确',
          sendCode: '发送验证码失败，请稍后重试',
          verificationCode: '请输入验证码',
          phoneNumber: '请输入手机号'
        }
      }
    },
    enterpriseIdentity: {
      table: {
        account: '账号',
        status: '状态',
        boundAccount: '绑定账号',
        actions: '操作',
        statusTags: {
          normal: '正常',
          abnormal: '异常'
        }
      },
      actions: {
        bind: '绑定',
        unbind: '解除绑定'
      },
      status: {
        notBound: '未绑定'
      },
      messages: {
        syncSuccess: '正在同步 {name}',
        viewDetails: '查看 {name} 的详细信息'
      }
    },
    mfa: {
      title: '多因素认证',
      methods: {
        sms: {
          title: '短信验证码',
          description: '使用短信形式接收验证码认证登录'
        },
        email: {
          title: '电子邮箱验证',
          description: '使用邮件形式接收验证码认证登录'
        },
        otp: {
          title: 'OTP 口令验证',
          description: '使用 OTP 一次性口令认证登录'
        }
      },
      dialog: {
        sms: {
          title: '绑定二次验证手机号',
          phone: '输入手机号',
          phonePlaceholder: '请输入手机号',
          code: '短信验证码',
          codePlaceholder: '请输入6位验证码'
        },
        email: {
          title: '绑定二次验证邮箱',
          email: '输入邮箱账号',
          emailPlaceholder: '请输入邮箱账号',
          code: '邮箱验证码',
          codePlaceholder: '请输入6位验证码'
        },
        passkey: {
          title: '创建 Passkey',
          noData: '暂无数据',
          description: '借助 Passkey，您可以使用自己的指纹、面孔、屏幕设置或实体安全密钥录制的账号，请仅在您自有的设备上设置 Passkey。',
          create: '创建 Passkey'
        }
      },
      button: {
        bind: '绑定',
        unbind: '解绑',
        sendCode: '发送验证码',
        resendCode: '{seconds}s后重新发送',
        confirm: '确认',
        cancel: '取消'
      },
      message: {
        phoneRequired: '请输入手机号',
        emailRequired: '请输入邮箱地址',
        codeRequired: '请输入验证码',
        codeSent: '验证码已发送',
        bindSuccess: '{type}绑定成功',
        bindFailed: '{type}绑定失败',
        unbindSuccess: '{type}解绑成功',
        unbindFailed: '{type}解绑失败',
        passkeySuccess: 'Passkey创建成功'
      }
    },
    personalInfo: {
      title: '个人信息',
      input_placeholder: "请输入{label}",
      select_placeholder: "请选择{label}",
      actions: {
        edit: '编辑',
        save: '保存',
        cancel: '取消'
      },
      form: {
        username: '用户名',
        userId: '用户 ID',
        email: '邮箱',
        phone: '手机号',
        name: '姓名',
        title: '职称',
        gender: {
          label: '性别',
          male: '男',
          female: '女',
          other: '其他',
          unknown: '未知'
        },
        birthdate: '生日',
        company: '公司',
        idNumber: '身份证号',
        registrationTime: '注册时间',
        address: '住址',
        avatar: {
          upload: '上传头像',
          tips: '请先点击编辑按钮开启编辑模式',
          success: '头像上传成功',
          error: '头像上传失败，请重试',
          formatError: '上传头像图片只能是 JPG/PNG/GIF/BMP/WebP 格式!',
          sizeError: '上传头像图片大小不能超过 10MB!'
        }
      },
      message: {
        updateSuccess: '个人信息更新成功',
        updateError: '个人信息更新失败，请重试'
      }
    },
    socialIdentity: {
      table: {
        account: '账号',
        status: '状态',
        boundAccount: '绑定账号',
        actions: '操作',
        statusTags: {
          bound: '已绑定',
          notBound: '未绑定'
        }
      },
      actions: {
        bind: '绑定',
        unbind: '解除绑定'
      },
      sources: {
        wechat: {
          name: '微信',
          desc: '使用微信账号登录'
        },
        github: {
          name: 'Github',
          desc: '使用Github账号登录'
        },
        dingtalk: {
          name: '钉钉',
          desc: '使用钉钉账号登录'
        }
      },
      messages: {
        unbindSuccess: '已解除绑定 {name}',
        bindSuccess: '已成功绑定 {name}',
        bindFailed: '绑定 {name} 失败，请重试',
        unbindFailed: '解除绑定 {name} 失败，请重试',
        fetchError: '获取社会化身份源列表失败'
      }
    },
    cms: {
      parent: '内容管理',
      create: '新增',
      news: {
        name: '新闻资讯',
        edit: '新闻编辑'
      },
      case: {
        name: '客户案例',
        edit: '案例编辑'
      },
      product: {
        name: '产品中心',
        edit: '产品编辑'
      },
      job: {
        name: '岗位招聘',
        edit: '岗位编辑'
      },
      page: {
        name: '页面管理',
        edit: '页面编辑'
      },
      messages: {
        name: '留言本',
        edit: '留言编辑'
      },
      recommend: {
        name: '推荐列表',
        detail: '推荐详情',
        create: '创建推荐',
        edit: '编辑推荐'
      },
      categories: '分类',
      contact: {
        records: '联系记录'
      }
    }
  },
  iam: {
    menu: {
      application: '应用',
      identitySource: {
        title: '身份源管理',
        social: '社会化身份源',
        enterprise: '企业身份源'
      },
      userManagement: {
        title: '用户管理',
        userList: '用户列表',
        userGroup: '用户组管理',
        organization: '组织机构',
        position: '岗位管理'
      },
      permission: {
        title: '权限管理',
        role: '角色管理'
      },
      security: {
        title: '安全设置',
        mfa: '多因素认证'
      },
      branding: {
        title: '品牌化',
        message: '消息设置'
      }
    }
  },
  Router: {
    personalInfo: '个人资料',
  }
}

export default zh_CN
