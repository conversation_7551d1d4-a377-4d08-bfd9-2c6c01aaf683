const zh_HK = {
  system: {
    name: 'BWMS 管理系統',
    chinese: '中文',
    english: '英文',
    confirm: '確定',
    cancel: '取消',
    warning: '警告',
    next: '下一頁',
    prev: '上一頁',
    yes: '是',
    no: '否',
    add: '新增',
    edit: '編輯',
    finish: '完成',
    back: '返回',
    update: '更新',
    export: '匯出',
    search: '搜尋',
    refresh: '重設',
    detail: '詳情',
    delete: '刪除',
    prompt: '提示',
    more: '更多',
    logout: '登出',
    fail: '失敗',
    success: '成功',
    close: '關閉',
    download: '下載',
    profile: '個人資料',
    lang: {
      en: '英文',
      zh_HK: '繁體中文',
      zh_CN: '簡體中文',
    },
  },

  login: {
    email: '電郵',
    password: '密碼',
    sign_in: '登入',
    welcome: '👏歡迎回來',
    lost_password: '忘記密碼?',
    remember: '記住我',
    verify: {
      email: {
        required: '請先輸入電郵',
        invalid: '電郵地址無效',
      },

      password: {
        required: '請先輸入密碼',
      },
    },
  },

  register: {
    sign_up: '註冊',
  },
  generate: {
    schema: {
      title: '建立資料表',
      name: '表名稱',
      name_verify: '請輸入表名稱',
      engine: {
        name: '表引擎',
        verify: '請選擇表引擎',
        placeholder: '選擇表引擎',
      },
      default_field: {
        name: '預設欄位',
        created_at: '建立時間',
        updated_at: '更新時間',
        creator: '建立人',
        delete_at: '軟刪除',
      },
      comment: {
        name: '表註釋',
        verify: '請填寫表格註記/說明',
      },

      structure: {
        title: '建立資料結構',
        field_name: {
          name: '欄位名稱',
          verify: '請填入欄位名稱',
        },
        length: '長度',
        type: {
          name: '類型',
          placeholder: '選擇欄位類型',
          verify: '請先選擇欄位類型',
        },
        form_label: '表單 Label',
        form_component: '表單元件',
        list: '列表',
        form: '表單',
        unique: '唯一',
        search: '查詢',
        search_op: {
          name: '搜尋運算子',
          placeholder: '選擇搜尋運算子',
        },
        nullable: 'nullable',
        default: '預設值',
        rules: {
          name: '驗證規則',
          placeholder: '選擇驗證規則',
        },
        operate: '操作',
        comment: '欄位註解',
      },
    },
    code: {
      title: '生成程式碼',
      module: {
        name: '模組',
        placeholder: '請選擇模組',
        verify: '請選擇模組',
      },
      controller: {
        name: '控制器',
        placeholder: '請輸入控制器名稱',
        verify: '請輸入控制器名稱',
      },
      model: {
        name: '模型',
        placeholder: '請輸入模型名稱',
        verify: '請輸入模型名稱',
      },
      paginate: '分頁',
      menu: {
        name: '選單名稱',
        placeholder: '請輸入選單名稱',
        verify: '請輸入選單名稱',
      },
    },
  },

  module: {
    create: '建立模組',
    update: '更新模組',
    form: {
      name: {
        title: '模組名稱',
        required: '請輸入模組名稱',
      },

      path: {
        title: '模組目錄',
        required: '請輸入模組目錄',
      },

      desc: {
        title: '模組描述',
      },

      keywords: {
        title: '模組關鍵字',
      },

      dirs: {
        title: '預設目錄',
        Controller: 'Controller 目錄',
        Model: 'Model 目錄',
        Database: 'Database 目錄',
        Request: 'Request 目錄',
      },
    },
  },
  widgets: {
    widget_configuration: '小工具配置',
    report_widget: '配置報告小工具',
    width: '寬度（1-12）',
    newLine: '強制新行',
    title: '小工具標題',
    manage: '管理小工具',
    add: '新增小工具',
    widgets: '小工具',
    close: '關閉',
    sure: '確定',
    loading: '載入中...',
    title_required: '請輸入小工具標題',
    x: 'x軸',
    y: 'y軸',
    h: '高度',
  },

  header: {
    settings: {
      tit1: '帳戶',
      li1: '個人資料和偏好設定',
      li2: '重設密碼',
      tit2: '系統語言',
      li3: '語言切換',
      langswitch: '語言切換',
    },
    contact: {
      title: '您的售後服務專員',
      label1: '服務時間',
      label2: '電子郵件',
      btn_text: '發送郵件',
    },
  },
  home: {
    title: '控制面板',
    btn_text1: '新增小工具',
    dialog_tit1: '確認刪除該模塊？',
    activityLog: {
      th1: '帳戶',
      th2: '瀏覽器',
      th3: '平台',
      th4: 'IP',
      th5: '狀態',
      th6: '登入時間',
    },
    analytics: {
      placeholder1: '開始時間',
      placeholder2: '結束時間',
      reportTypes: {
        basicUser: '基本用戶報告',
        trafficSource: '流量來源報告',
        pagePerformance: '頁面效能報告',
        ecommerce: '電子商務報告',
        userBehavior: '用戶行為報告',
        mobileApp: '流動應用報告',
        adPerformance: '廣告效果報告',
        content: '內容分析報告',
        geographic: '地理位置報告',
        technical: '技術分析報告',
        event: '事件分析報告',
        conversionFunnel: '轉換漏斗報告',
      },
    },
    news: {
      title: '新聞',
    },
    system: {
      title1: '網站儲存容量',
      title2: '離續訂還有幾天',
      con1: '天',
      btn_text1: '查看儲存',
      btn_text2: '立即續訂',
      btn_text3: '增加容量',
    },
  },
  404: {
    tips: '抱歉，您訪問的頁面不存在',
    btn_text: '回到首頁',
  },
  analytics: {
    dimensions: {
      date: '日期',
      country: '國家',
      deviceCategory: '設備類別',
      source: '來源',
      medium: '媒介',
      campaign: '活動',
      pagePath: '頁面路徑',
      itemName: '商品名稱',
      itemCategory: '商品類別',
      sessionSourceMedium: '會話來源/媒介',
      landingPage: '著陸頁',
      appVersion: '應用版本',
      operatingSystem: '作業系統',
      adGroup: '廣告組',
      adContent: '廣告內容',
      pageTitle: '頁面標題',
      region: '地區',
      city: '城市',
      browser: '瀏覽器',
      eventName: '事件名稱',
      eventCategory: '事件類別',
    },
    metrics: {
      totalUsers: '總用戶數',
      newUsers: '新用戶數',
      sessions: '會話數',
      bounceRate: '跳出率',
      screenPageViews: '頁面瀏覽量',
      averageSessionDuration: '平均會話時長',
      conversions: '轉換次數',
      averagePageLoadTime: '平均頁面載入時間',
      exitRate: '退出率',
      itemViews: '商品瀏覽次數',
      itemsAddedToCart: '加入購物車次數',
      purchases: '購買次數',
      itemRevenue: '商品收入',
      engagementRate: '參與率',
      conversionsPerSession: '每次會話轉換次數',
      crashFreeUsersRate: '無當機用戶率',
      userEngagementDuration: '用戶參與時長',
      adClicks: '廣告點擊次數',
      adImpressions: '廣告展示次數',
      adCost: '廣告成本',
      adConversions: '廣告轉換次數',
      averageTimeOnPage: '平均頁面停留時間',
      entrances: '入口次數',
      eventCount: '事件次數',
      eventValue: '事件價值',
      addToCarts: '加入購物車次數',
      checkouts: '結帳次數',
    },
  },
  dashboard: {
    loading: '載入中...',
    home: '首頁',
    title: '儀表板',
    plugins: {
      title: '插件',
      button: '插件',
    },
    aiCapabilities: {
      title: 'AI 功能',
    },
    usefulTools: {
      title: '實用工具',
    },
    analytics: {
      title: '追蹤分析',
    },
    content: {
      title: '內容管理',
    },
    websiteStatus: {
      diskSpace: {
        title: '磁碟空間',
      },
      renewal: {
        title: '續費資訊',
        days: '天',
        increaseCapacity: '增加容量',
        renewalNow: '立即續訂',
        dateRange: {
          to: '至'
        }
      }
    },
    news: {
      title: '新聞',
      learnMore: '了解更多',
    },
    userInfo: {
      title: '有疑問？',
      specialist: '您的售後服務專員',
      contactButton: '點擊聯絡',
    },
    statisticCard: {
      totalPages: '共 {total} 頁',
      cards: {
        activePages: '活躍頁面',
        totalVisits: '總訪問量',
        uniqueVisitors: '獨立訪客',
        systemAlerts: '系統提醒',
      },
      units: {
        pages: '頁',
        visits: '次',
        users: '人',
        alerts: '條',
      }
    },
    websiteAnalytics: {
      tabs: {
        overview: '數據概覽',
        content: '內容差異',
        hot: '熱門內容'
      },
      filters: {
        timeRange: {
          label: '時間範圍',
          options: {
            last30: '最近 30天',
            last60: '最近 60天',
            last90: '最近 90天'
          }
        },
        sortBy: {
          label: '排序方式',
          options: {
            content: '更新內容量',
            time: '時間'
          }
        }
      },
      content: {
        updateCount: '內容更新數量',
        updateTypes: '更新類型',
        articles: '文章：{count}篇',
        products: '產品頁：{count}頁',
        images: '圖片：{count}張',
        charts: {
          trend: '更新趨勢',
          distribution: '更新內容類型分佈',
          comparison: '更新內容差異比較'
        }
      },
      competitor: {
        add: '新增競爭對手',
        dialog: {
          title: '新增競爭對手',
          name: '網站名稱',
          namePlaceholder: '請輸入網站名稱',
          url: '網站地址',
          urlPlaceholder: '請輸入網站地址',
          confirm: '確定',
          cancel: '取消'
        },
        messages: {
          fillRequired: '請填寫完整資訊',
          addSuccess: '新增成功',
          addFailed: '新增失敗'
        }
      }
    },
    ga4Iframe: {
      loading: '載入中...',
      error: '載入 GA4 數據失敗'
    },
    userMenu: {
      title: '個人中心',
      backToDashboard: '返回控制台',
      items: {
        personalInfo: '個人資訊',
        accountBinding: '帳戶綁定',
        mfa: '多重認證',
        socialIdentity: '社交身份源管理',
        enterpriseIdentity: '企業身份源管理',
        accountSecurity: '帳戶安全',
        accessLog: '存取日誌'
      }
    },
    accessLog: {
      filter: {
        button: '篩選',
        title: '篩選條件',
        ipAddress: 'IP地址',
        ipPlaceholder: '輸入IP地址',
        dateRange: '日期範圍',
        startDate: '開始日期',
        endDate: '結束日期',
        reset: '重設',
        apply: '篩選'
      },
      table: {
        loginTime: '登入時間',
        loginUser: '登入用戶',
        ipAddress: 'IP 地址',
        status: '狀態',
        browser: '瀏覽器',
        location: '地理位置',
        statusSuccess: '成功',
        statusFailed: '失敗'
      },
      error: {
        fetchFailed: '獲取登入歷史失敗，請稍後重試'
      }
    },
    accountBinding: {
      email: {
        title: '電郵',
        unbind: '解除綁定',
        bind: '綁定',
        modify: '修改',
        notBound: '未綁定',
        current: '目前電郵',
        new: '新電郵',
        verificationCode: '驗證碼',
        currentVerificationCode: '目前電郵驗證碼',
        newVerificationCode: '新電郵驗證碼',
        unbindTitle: '解除綁定電郵',
        bindTitle: '綁定電郵',
        modifyTitle: '修改電郵',
        placeholder: '請輸入電郵',
        codePlaceholder: '請輸入6位驗證碼'
      },
      phone: {
        title: '手機號碼',
        unbind: '解除綁定',
        bind: '綁定',
        modify: '修改',
        notBound: '未綁定',
        current: '目前手機號碼',
        new: '新手機號碼',
        verificationCode: '驗證碼',
        currentVerificationCode: '目前手機號碼驗證碼',
        newVerificationCode: '新手機號碼驗證碼',
        unbindTitle: '解除綁定手機號碼',
        bindTitle: '綁定手機號碼',
        modifyTitle: '修改手機號碼',
        placeholder: '請輸入手機號碼',
        codePlaceholder: '請輸入6位驗證碼'
      },
      common: {
        confirm: '確認',
        cancel: '取消',
        sendCode: '發送驗證碼',
        countdown: '{seconds}秒',
        required: '請填寫所有必要資訊',
        invalidEmail: '請輸入正確的電郵格式',
        invalidPhone: '請輸入正確的手機號碼格式',
        success: {
          update: '{type}更新成功',
          unbind: '{type}已解除綁定'
        },
        error: {
          update: '更新{type}失敗，請檢查資訊是否正確',
          unbind: '解除綁定{type}失敗，請檢查驗證碼是否正確',
          sendCode: '發送驗證碼失敗，請稍後重試'
        }
      }
    },
    accountSafety: {
      score: {
        title: '安全評分',
        level: '安全級別：{level}',
        levels: {
          low: '低',
          medium: '中',
          high: '高'
        }
      },
      password: {
        title: '密碼設定',
        strength: {
          weak: '弱',
          medium: '中',
          strong: '強'
        },
        description: '建議使用較為複雜的密碼。',
        modify: '修改',
        modifyNow: '立即修改',
        dialog: {
          title: '修改密碼',
          oldPassword: '原始密碼',
          newPassword: '新密碼',
          confirmPassword: '確認密碼',
          oldPasswordRequired: '請輸入原始密碼',
          newPasswordRequired: '請輸入新密碼',
          passwordLength: '密碼長度不能少於6個字元',
          passwordMismatch: '兩次輸入的密碼不一致',
          placeholder: {
            oldPassword: '請輸入原始密碼',
            newPassword: '請輸入新密碼',
            confirmPassword: '請確認密碼'
          }
        }
      },
      accountDeletion: {
        title: '帳戶註銷',
        description: '永久刪除帳戶和所有數據，請謹慎操作。',
        delete: '註銷',
        dialog: {
          title: '帳戶註銷',
          warning: '註銷後，此帳戶的所有數據都將被刪除且不可逆，請謹慎操作！',
          account: '帳戶',
          password: '密碼',
          phone: '手機號碼',
          verificationCode: '驗證碼',
          placeholder: {
            account: '請輸入目前帳戶',
            password: '請輸入目前密碼',
            phone: '請輸入手機號碼',
            code: '請輸入驗證碼'
          }
        }
      },
      mfa: {
        title: '多重認證 (MFA)',
        enabled: '已開啟',
        disabled: '未開啟',
        enable: '開啟',
        disable: '關閉'
      },
      email: {
        title: '電郵綁定',
        bound: '已綁定',
        unbound: '未綁定',
        modify: '修改',
        bind: '綁定'
      },
      phone: {
        title: '手機綁定',
        bound: '已綁定',
        unbound: '未綁定',
        modify: '修改',
        bind: '綁定'
      },
      common: {
        confirm: '確認',
        cancel: '取消',
        sendCode: '發送驗證碼',
        countdown: '{seconds}秒',
        success: {
          passwordUpdate: '密碼修改成功',
          accountDeletion: '帳戶註銷請求已提交',
          codeSent: '驗證碼已發送'
        },
        error: {
          passwordUpdate: '修改密碼失敗，請檢查原密碼是否正確',
          accountDeletion: '帳戶註銷失敗，請檢查輸入資訊是否正確',
          sendCode: '發送驗證碼失敗，請稍後重試',
          verificationCode: '請輸入驗證碼',
          phoneNumber: '請輸入手機號碼'
        }
      }
    },
    enterpriseIdentity: {
      table: {
        account: '帳戶',
        status: '狀態',
        boundAccount: '綁定帳戶',
        actions: '操作',
        statusTags: {
          normal: '正常',
          abnormal: '異常'
        }
      },
      actions: {
        bind: '綁定',
        unbind: '解除綁定'
      },
      status: {
        notBound: '未綁定'
      },
      messages: {
        syncSuccess: '正在同步 {name}',
        viewDetails: '查看 {name} 的詳細資訊'
      }
    },
    mfa: {
      title: '多重認證',
      methods: {
        sms: {
          title: '短訊驗證碼',
          description: '使用短訊形式接收驗證碼認證登入'
        },
        email: {
          title: '電子郵件驗證',
          description: '使用郵件形式接收驗證碼認證登入'
        },
        otp: {
          title: 'OTP 密碼驗證',
          description: '使用 OTP 一次性密碼認證登入'
        }
      },
      dialog: {
        sms: {
          title: '綁定二次驗證手機號碼',
          phone: '輸入手機號碼',
          phonePlaceholder: '請輸入手機號碼',
          code: '短訊驗證碼',
          codePlaceholder: '請輸入6位驗證碼'
        },
        email: {
          title: '綁定二次驗證電郵',
          email: '輸入電郵帳戶',
          emailPlaceholder: '請輸入電郵帳戶',
          code: '電郵驗證碼',
          codePlaceholder: '請輸入6位驗證碼'
        },
        passkey: {
          title: '建立 Passkey',
          noData: '暫無數據',
          description: '借助 Passkey，您可以使用自己的指紋、面容、屏幕設定或實體安全密鑰錄製的帳戶，請僅在您自有的設備上設定 Passkey。',
          create: '建立 Passkey'
        }
      },
      button: {
        bind: '綁定',
        unbind: '解除綁定',
        sendCode: '發送驗證碼',
        resendCode: '{seconds}秒後重新發送',
        confirm: '確認',
        cancel: '取消'
      },
      message: {
        phoneRequired: '請輸入手機號碼',
        emailRequired: '請輸入電郵地址',
        codeRequired: '請輸入驗證碼',
        codeSent: '驗證碼已發送',
        bindSuccess: '{type}綁定成功',
        bindFailed: '{type}綁定失敗',
        unbindSuccess: '{type}解除綁定成功',
        unbindFailed: '{type}解除綁定失敗',
        passkeySuccess: 'Passkey建立成功'
      }
    },
    personalInfo: {
      title: '個人資訊',
      input_placeholder: "請輸入{label}",
      select_placeholder: "請選擇{label}",
      actions: {
        edit: '編輯',
        save: '儲存',
        cancel: '取消'
      },
      form: {
        username: '用戶名',
        userId: '用戶 ID',
        email: '電郵',
        phone: '手機號碼',
        name: '姓名',
        title: '職稱',
        gender: {
          label: '性別',
          male: '男',
          female: '女',
          other: '其他',
          unknown: '未知'
        },
        birthdate: '生日',
        company: '公司',
        idNumber: '身份證號碼',
        registrationTime: '註冊時間',
        address: '住址',
        avatar: {
          upload: '上載頭像',
          tips: '請先點擊編輯按鈕開啟編輯模式',
          success: '頭像上載成功',
          error: '頭像上載失敗，請重試',
          formatError: '上載頭像圖片只能是 JPG/PNG/GIF/BMP/WebP 格式!',
          sizeError: '上載頭像圖片大小不能超過 10MB!'
        }
      },
      message: {
        updateSuccess: '個人資訊更新成功',
        updateError: '個人資訊更新失敗，請重試'
      }
    },
    socialIdentity: {
      table: {
        account: '帳戶',
        status: '狀態',
        boundAccount: '綁定帳戶',
        actions: '操作',
        statusTags: {
          bound: '已綁定',
          notBound: '未綁定'
        }
      },
      actions: {
        bind: '綁定',
        unbind: '解除綁定'
      },
      sources: {
        wechat: {
          name: '微信',
          desc: '使用微信帳戶登入'
        },
        github: {
          name: 'Github',
          desc: '使用Github帳戶登入'
        },
        dingtalk: {
          name: '釘釘',
          desc: '使用釘釘帳戶登入'
        }
      },
      messages: {
        unbindSuccess: '已解除綁定 {name}',
        bindSuccess: '已成功綁定 {name}',
        bindFailed: '綁定 {name} 失敗，請重試',
        unbindFailed: '解除綁定 {name} 失敗，請重試',
        fetchError: '獲取社交身份源列表失敗'
      }
    },
    cms: {
      parent: '內容管理',
      create: '新增',
      news: {
        name: '新聞資訊',
        edit: '新聞編輯'
      },
      case: {
        name: '客戶案例',
        edit: '案例編輯'
      },
      product: {
        name: '產品中心',
        edit: '產品編輯'
      },
      job: {
        name: '職位招聘',
        edit: '職位編輯'
      },
      page: {
        name: '頁面管理',
        edit: '頁面編輯'
      },
      messages: {
        name: '留言簿',
        edit: '留言編輯'
      },
      recommend: {
        name: '推薦列表',
        detail: '推薦詳情',
        create: '創建推薦',
        edit: '編輯推薦'
      },
      categories: '分類',
      contact: {
        records: '聯絡記錄'
      }
    },
  },
  iam: {
    menu: {
      application: '應用',
      identitySource: {
        title: '身份源管理',
        social: '社交身份源',
        enterprise: '企業身份源'
      },
      userManagement: {
        title: '用戶管理',
        userList: '用戶列表',
        userGroup: '用戶組管理',
        organization: '組織機構',
        position: '職位管理'
      },
      permission: {
        title: '權限管理',
        role: '角色管理'
      },
      security: {
        title: '安全設定',
        mfa: '多重認證'
      },
      branding: {
        title: '品牌化',
        message: '訊息設定'
      }
    }
  },
  Router: {
    personalInfo: '個人資料',
  }
}

export default zh_HK
