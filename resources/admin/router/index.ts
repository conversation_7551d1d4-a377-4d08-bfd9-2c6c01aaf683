import { createRouter, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RouteRecordRaw } from 'vue-router'
import type { App } from 'vue'
// module routers
import { getModuleRoutes, getModuleViewComponents } from './constantRoutes'

const moduleRoutes = getModuleRoutes()
getModuleViewComponents()

export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/dashboard',
    component: () => import('/admin/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        meta: { title: 'Dashboard', icon: 'home', hidden: false, requiresAuth: true },
        component: () => import('@admin/views/dashboard/index.vue'),
      },
    ],
  },
]
  // @ts-ignore
  .concat(moduleRoutes)

// default routes, it will not change to menus
const defaultRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: '/',
    component: () => import('/admin/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: '/404',
        name: '404',
        meta: { title: '404' },
        component: () => import('/admin/components/404/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    name: 'login',
    meta: { title: '登录' },
    component: () => import('@/module/Iam/FrontendView/ui/Login/login.vue'),
    props: route => ({ app_code: route.query.app_code || 'admin' }),
  },
  {
    path: '/userSettings',
    name: 'userSettings',
    meta: { title: '设置' },
    component: () => import('/admin/layout/settingPage.vue'),
    children: [
      {
        path: '',
        name: 'PersonalInfo',
        component: () => import('/admin/layout/components/Menu/usercomponents/PersonalInfo.vue'),
        meta: { title: '個人資料' },
      },
      {
        path: 'account',
        name: 'AccountBinding',
        component: () => import('/admin/layout/components/Menu/usercomponents/AccountBinding.vue'),
        meta: { title: '账号绑定' },
      },
      {
        path: 'security',
        name: 'MultiFactorAuth',
        component: () => import('/admin/layout/components/Menu/usercomponents/MultiFactorAuth.vue'),
        meta: { title: '多因素认证' },
      },
      {
        path: 'socialIdentity',
        name: 'SocialIdentitySource',
        component: () => import('/admin/layout/components/Menu/usercomponents/SocialIdentitySource.vue'),
        meta: { title: '社交身份源' },
      },
      {
        path: 'enterpriseIdentity',
        name: 'EnterpriseIdentitySource',
        component: () => import('/admin/layout/components/Menu/usercomponents/EnterpriseIdentitySource.vue'),
        meta: { title: '企业身份源' },
      },
      {
        path: 'safety',
        name: 'AccountSafety',
        component: () => import('/admin/layout/components/Menu/usercomponents/AccountSafety.vue'),
        meta: { title: '账号安全' },
      },
      {
        path: 'log',
        name: 'AccessLog',
        component: () => import('/admin/layout/components/Menu/usercomponents/AccessLog.vue'),
        meta: { title: '访问日志' },
      },
    ],
  },
  {
    path: '/editorTiptap',
    name: 'editorTiptap',
    component: () => import('/admin/layout/autoPage.vue'),
    meta: { title: '模块编辑器' },
    children: [
      {
        path: '',
        name: 'moduleEditor',
        component: () => import('@/module/Editor/views/tiptap/FullscreenEditor.vue'),
        meta: { title: '模块编辑器' }
      }
    ]
  },
]

const routes = constantRoutes.concat(defaultRoutes)
const router = createRouter({
  history: createWebHashHistory(),
  routes,
  // 路由滚动
  scrollBehavior(to, from, savedPosition) {
    return savedPosition || { top: 0, behavior: 'smooth' }
  },
})

export function bootstrapRouter(app: App) {
  app.use(router)
}

export default router
