
.table-page {
  font-weight: 400;
  font-family: var(--font-family);
  color: var(--input-text-color);
  /* 表格样式优化 */
  .el-table--fit {
    .el-table__inner-wrapper::before {
      height: 0;
    }
  }

  .el-table {
    /* 设置表格内容字体 */
    .el-table__cell {
      color: var(--input-text-color);
    }

    /* 表头样式 */
    th.el-table__cell {
      font-weight: normal;
      height: 38px;
    }

    /* 表格行样式 */
    .el-table__row {
    }

    .el-table__body {
      td.el-table__cell {
        padding: 15px 0;
      }
    }
  }

  /* 按钮样式 */
  .el-button,
  .el-button.el-button--primary,
  .el-button.el-button-default,
  .el-button.button-no-border {
    // 按钮默认样式 白色背景，黑色字体，有边框
    color: var(--el-text-color-regulars); // #18191A
    padding: 0 12px;
    font-weight: 400;
    font-family: var(--font-family);
    font-size: 16px;
    border-radius: 5px;
    border: 1px solid var(--el-border-color-lighter);;
    min-width: 103px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon {
      // 图标样式
      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        display: block;
      }
    }
  }
  .el-button [class*=el-icon]+span,
  .el-button [class*=el-icon] + span{
    margin-left: 12px !important;
  }
  .el-button.el-button--primary {
    background-color: var(--theme-text-color);
    color: #fff;
    border: none;
  }
  .el-button.is-link {
    min-width: auto;
    color: var(--theme-text-color);
    border: none;
    padding: 0;
  }
  .el-button.button-no-border {
    // 按钮默认样式 白色背景，黑色字体，无边框
    border: none;
  }

  .el-button.back-btn {
    // 返回按钮样式
    background-color: #ececec;
    border: none;
  }

  .el-button.el-button-plus {
    // + 新增按钮样式
    color: var(--el-color-white) !important;
    background-color: var(--theme-text-color);
  }
  .el-button.button-cancel {
    // 提交的取消按钮样式
    color: #FD6074;
    border-color: #FD6074;
    background-color: transparent;
  }
  .el-button.el-button--text,
  .el-button.is-text, .el-button.is-link {
    display: inline-block;
    min-width: auto;
    height: auto;
    color: var(--input-text-color);
    border: none;
    background-color: transparent;
    padding: 0;
    &:hover, &:focus, &:active, &.is-hover, &.is-focus, &.is-active {
      background-color: transparent;
    }
  }
  .el-button--primary.is-text, .el-button--primary.is-link {
    display: inline-block;
    min-width: auto;
    height: auto;
    color: var(--theme-text-color);
    border: none;
    background-color: transparent;
    padding: 0;
    &:hover, &:focus, &:active, &.is-hover, &.is-focus, &.is-active {
      background-color: transparent;
    }
  }
  .el-button--primary.is-plain {
    border: 1px solid var(--theme-text-color);
    color: var(--theme-text-color);
    background-color: transparent;
    &:hover, &:focus, &:active, &.is-hover, &.is-focus, &.is-active {
      background-color: var(--theme-text-color);
      color: var(--el-color-white);
    }
  }

  /* 默认公共单个设置按钮图标 */
  .el-icon-custom {
    img {
      width: 16px;
      height: 16px;
      object-fit: contain;
      display: block;
    }
  }

  /* 表格操作列按钮样式 */
  .bwms-operate-btn-box {
    display: flex;
    align-items: flex-start;
    justify-content: left;
    gap: 26px;

    .bwms-operate-btn {
      padding: 0;
      min-width: 15px;
      width: 15px;
      height: 15px;
      border: none;

      .el-icon {
        img {
          width: 15px;
          height: 15px;
          object-fit: contain;
          display: block;
        }
      }
    }

    .el-button + .el-button {
      margin-left: 0;
    }
  }

  /* 分页样式 */
  .table-pagination-style {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    color: #5d5d5d;

    .el-select__placeholder {
      color: #5d5d5d;
    }

    .pagination-left {
      display: flex;
      align-items: center;
    }

    .pagination-right {
      display: flex;
      justify-content: flex-end;
    }

    .page-size-text {
      margin-right: 8px;
      font-size: 14px;
    }

    .page-size-select {
      width: 65px;
      height: 38px;
      margin-right: 15px;
    }

    .page-size-select .el-input__wrapper {
      height: 38px;
      line-height: 38px;
    }

    .page-size-select .el-input__inner {
      text-align: center;
    }

    .el-select .el-select__wrapper {
      min-height: 38px !important;
      line-height: 38px !important;
      font-size: 14px !important;
      box-shadow: 0 0 0 1px #e4e4e4 inset;
      padding: 1px 11px;
    }

    .el-pager {
      height: 38px !important;
      line-height: 38px !important;
      font-size: 14px !important;
      gap: 6px !important;

      .number {
        width: 38px !important;
        height: 38px !important;
        line-height: 38px !important;
        font-size: 14px !important;
        border-radius: 5px !important;
        margin: 0 0px !important;
        background-color: #fff;
      }
    }

    .el-pager .is-active {
      background-color: #007ee5 !important;
      color: #fff !important;
      font-weight: normal !important;
    }

    /* 添加左右箭头样式，保持与数字分页按钮一致 */
    .el-pagination .btn-prev,
    .el-pagination .btn-next {
      width: 38px !important;
      height: 38px !important;
      line-height: 38px !important;
      border-radius: 5px !important;
      padding: 0 !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      margin: 0 4px !important;
    }

    /* 箭头图标垂直居中对齐 */
    .el-pagination .btn-prev .el-icon,
    .el-pagination .btn-next .el-icon {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      width: 100% !important;
      height: 100% !important;
      font-size: 14px !important;
      border: 1px solid #e4e4e4;
      border-radius: 5px;
      opacity: 1;
    }
    .el-select .el-select__placeholder {
      color: #5d5d5d;
      font-size: 14px;
    }

    /* 悬停状态 */
    .el-pagination .btn-prev:hover,
    .el-pagination .btn-next:hover {
      color: #ccc !important;
      background: #efefef 0% 0% no-repeat padding-box;
    }

    /* 禁用状态 */
    .el-pagination .btn-prev.is-disabled,
    .el-pagination .btn-next.is-disabled {
      color: #c0c4cc !important;
      background: #efefef 0% 0% no-repeat padding-box;
      cursor: not-allowed !important;
    }
  }

  /* 标签 */
  .el-tag.tag--default {
    min-height: 40px;
    padding: 0 15px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 26px;
    position: relative;
    overflow: hidden;
    font-size: 18px;
    margin-right: 12px;
    margin-bottom: 12px;

    .el-tag__close {
      right: -5px;

      // &:hover {
      //   background-color: var(--theme-text-color);
      //   color: #fff;
      // }
    }

    &:hover {
      background-color: rgb(236 245 255);
      opacity: 0.85;
    }
  }
  .el-tag.el-tag--primary {
    border: 1px solid var(--theme-text-color);
    color: var(--theme-text-color);
  }

  /* 输入框，日期选择器，选择器, 单选框，复选框, 标签 */
.el-input,
.el-date-editor {
  font-size: var(--input-font-size);
  height: var(--input-height);
  min-height: var(--input-height);
  color: var(--input-text-color);

  .el-input__wrapper {
    padding: 1px 10px 1px 18px;
    border-radius: var(--input-border-radius);
    border: 1px solid var(--input-border-color);
    box-shadow: none !important;
    transition: border-color 0.2s;

    &:hover {
      border-color: var(--input-focus-color);
    }

    &.is-focus {
      border-color: var(--input-focus-color);
    }
  }

  .el-input__inner {
    height: 100%;
    color: var(--input-text-color);
    font-size: var(--input-font-size);
    font-weight: var(--input-font-weight);
  }

  /* placeholder-value */
  .el-input__placeholder {
    // 输入框的 placeholder
    font-size: var(--input-font-size);
    font-weight: var(--input-font-weight);
    color: var(--input-placeholder-color) !important;
  }

  .el-select__placeholder {
    // 下拉框的 placeholder
    font-size: var(--input-font-size);
    font-weight: var(--input-font-weight);
    color: var(--input-placeholder-color) !important;
  }

  .el-input__prefix,
  .el-input__suffix,
  .el-input__clear,
  .el-range__close-icon,
  .el-range__icon,
  .el-range-separator {
    // 输入框的前缀颜色, 输入框的后缀颜色, 输入框的清除按钮颜色, 输入框的关闭按钮颜色, 输入框的箭头颜色, 输入框的分割线颜色
    color: var(--input-caret-color);
    font-size: var(--input-font-size);
  }
}

/* 文本域 */
.el-textarea {
  font-size: var(--input-font-size);
  color: var(--input-text-color);
  
  .el-textarea__inner {
    color: var(--input-text-color);
    font-size: var(--input-font-size);
    font-weight: var(--input-font-weight);
    box-shadow: 0 0 0 1px var(--input-border-color) inset !important;
    padding: 14px 10px 14px 18px;
    
    &::placeholder {
      font-size: var(--input-font-size);
      font-weight: var(--input-font-weight);
      color: var(--input-placeholder-color);
    }
    &:hover {
      border: none;
      box-shadow: 0 0 0 1px var(--input-focus-color) inset !important;
    }
    
    &.is-focus {
      border: none;
      box-shadow: 0 0 0 1px var(--input-focus-color) inset !important;
    }
  }
}

.el-select {
  font-size: var(--input-font-size);
  height: var(--input-height);
  border-radius: var(--input-border-radius);
  color: var(--input-text-color);
  font-weight: var(--input-font-weight);

  .el-select__wrapper {
    padding: 1px 10px 1px 18px;
    border-radius: var(--input-border-radius);
    border: 1px solid var(--input-border-color);
    box-shadow: none !important;
    font-size: var(--input-font-size);
    line-height: var(--input-height);
    min-height: var(--input-height);
    transition: border-color 0.2s;

    &:hover {
      border-color: var(--input-focus-color);
    }

    &.is-focus {
      border-color: var(--input-focus-color);
    }
  }

  .el-input__wrapper {
    &.is-focus {
      border-color: var(--input-focus-color) !important;
      box-shadow: 0 0 0 1px var(--input-focus-color) inset !important;
    }
  }

  /* placeholder-value */
  .el-select__placeholder {
    font-size: var(--input-font-size);
    font-weight: var(--input-font-weight);
    color: var(--input-text-color);
  }
  /* placeholder灰色 */
  .el-select__placeholder.is-transparent {
    font-size: var(--input-font-size);
    font-weight: var(--input-font-weight);
    color: var(--input-placeholder-color);
  }

  .el-select__caret,
  .el-input__clear,
  .el-input__suffix,
  .el-input__prefix {
    // 下拉框的箭头颜色, 输入框的箭头颜色, 输入框的清除按钮颜色, 输入框的前缀颜色
    color: var(--input-caret-color);
    font-size: var(--input-font-size);
  }
}

.el-tree-select {
  .el-input {
    font-size: var(--input-font-size);
    height: var(--input-height);

    .el-input__wrapper {
      font-size: var(--input-font-size);
      height: var(--input-height);
      border-radius: var(--input-border-radius);
      border: 1px solid var(--input-border-color);
      box-shadow: none !important;
      transition: border-color 0.2s;

      &:hover {
        border-color: var(--input-focus-color);
      }

      &.is-focus {
        border-color: var(--input-focus-color) !important;
        box-shadow: 0 0 0 1px var(--input-focus-color) inset !important;
      }
    }

    .el-input__inner {
      font-size: var(--input-font-size);
      font-weight: var(--input-font-weight);
    }

    .el-select__placeholder {
      font-size: var(--input-font-size);
      font-weight: var(--input-font-weight);
      color: var(--input-text-color);
    }

    .el-select__caret {
      // 下拉框的箭头颜色
      color: var(--input-caret-color);
      font-size: var(--input-font-size);
    }
  }
}

.el-radio {
  .el-radio__label {
    font-size: var(--input-font-size) !important;
    font-weight: var(--input-font-weight);
    color: var(--input-text-color);
  }
}

.el-checkbox {
  .el-checkbox__label {
    font-size: var(--input-font-size) !important;
    font-weight: var(--input-font-weight);
    color: var(--input-text-color);
  }
}

/* 表单项 */
.el-form {
  width: 96%;
}
.el-form-item {
  margin-bottom: 26px;
}

.el-form-item__label {
  font-size: var(--form-item-label-font-size);
  font-weight: var(--form-item-label-font-weight);
  color: var(--form-item-label-color);
  margin-bottom: 8px;
}
.el-form-item__error {
  font-size: 14px;
  top: 104%;
}

/* tabs标签 */
.el-tabs {
  .el-tabs__active-bar {
    display: none;
  }

  .el-tabs__header {
    margin-bottom: 20px;

    .el-tabs__nav-wrap {
      .el-tabs__nav-scroll {
        .el-tabs__nav {
          .el-tabs__item {
            padding: 0 20px;
            font-size: 16px;
            color: #9a9a9a;
            height: 65px;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;

            &.is-active::after {
              content: "";
              display: block;
              width: 100%;
              height: 2px;
              background-color: var(--theme-text-color);
              position: absolute;
              bottom: 0;
              left: 0;
            }

            &.is-active,
            &:hover {
              color: var(--theme-text-color);
              font-weight: bold;
            }

            .tabs-num {
              margin-left: 10px;
              border-radius: 18px;
              min-width: 26px;
              height: 26px;
              padding: 0 5px;
              font-size: 14px;
              line-height: 1.35;
              background-color: #f5f5f5;
              color: #9a9a9a;
              font-weight: bold;

              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }

  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: var(--el-border-color-lights);
  }
}

/* 定制滚动条 */
.scroll-bar-custom {
  overflow: auto !important;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(189,216,248, 1);
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(189,216,248, 1);
    }
  }
}

.scroll-bar-custom-transparent {
  overflow: auto !important;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 3px;
    
    &:hover {
      background-color: transparent;
    }
  }
}

.add-tag-btn {
  width: 30px;
  height: 30px;
  border-radius: 100%;
  background: #8E8D8D;
  color: #FFFFFF;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

}

/* 筛选 popover 样式 */
.table-page.el-popover.el-popper {
  font-weight: var(--font-family);
  border-radius: var(--input-border-radius);
  box-shadow: 0px 6px 10px #00000017;
  border: 1px solid #DBDBDB;
  padding: 20px 12px 20px 20px;
  color: var(--input-text-color);
  font-size: var(--input-font-size);
  .el-popper__arrow {
    display: none;
  }
  /* 添加popover关闭按钮样式 */
  .popover-header {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
  }
}